package cec.jiutian.bc.ecs.domain.generalIp.event;

import cec.jiutian.bc.ecs.domain.generalIp.entity.GeneralIp;
import cec.jiutian.view.fun.DataProxy;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class GeneralIpDataProxy implements DataProxy<GeneralIp> {
    @Value("${aes.key}")
    private String aesKey;
    @Override
    public void beforeAdd(GeneralIp generalIp) {
        generalIp.setAuthKey(generalIp.getSysName(), aesKey);
    }

    @Override
    public void beforeUpdate(GeneralIp generalIp) {
        generalIp.setAuthKey(generalIp.getSysName(), aesKey);
    }
}
