package cec.jiutian.bc.ecs.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
public enum MessageGroupLevelEnum {

    LEVEL1("一级", "LEVEL1"),
    LEVEL2("二级", "LEVEL2"),
    LEVEL3("三级", "LEVEL3");


    private final String name;

    private final String code;

    MessageGroupLevelEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public static class ChoiceFetch implements ChoiceFetchHandler {
        @Override
        public List<VLModel> fetch(String[] params) {
            return Stream.of(MessageGroupLevelEnum.values())
                    .map(MessageGroupLevelEnum -> new VLModel(MessageGroupLevelEnum.getCode(), MessageGroupLevelEnum.getName()))
                    .collect(Collectors.toList());

        }

    }
}
