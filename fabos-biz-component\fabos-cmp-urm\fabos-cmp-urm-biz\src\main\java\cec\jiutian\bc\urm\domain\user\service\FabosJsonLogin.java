package cec.jiutian.bc.urm.domain.user.service;

import cec.jiutian.view.config.Comment;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Comment("自定义登录逻辑，需在spring boot入口类中修饰")
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Documented
@Inherited
public @interface FabosJsonLogin {

    Class<? extends LoginProxy> value();
}
