//package cec.jiutian.bc.ecs.domain.extendFabosJsonTest;
//
//import cec.jiutian.bc.flow.domain.model.entity.ExamineModel;
//import cec.jiutian.core.frame.constant.YesOrNoStatus;
//import cec.jiutian.core.frame.module.BaseModel;
//import cec.jiutian.core.view.fabosJson.FabosJson;
//import cec.jiutian.view.FabosJsonField;
//import cec.jiutian.view.expr.ExprBool;
//import cec.jiutian.view.field.Edit;
//import cec.jiutian.view.field.EditType;
//import cec.jiutian.view.field.View;
//import cec.jiutian.view.field.edit.ChoiceType;
//import cec.jiutian.view.field.edit.Search;
//import cec.jiutian.view.type.*;
//import jakarta.persistence.Entity;
//import jakarta.persistence.Table;
//import lombok.Getter;
//import lombok.Setter;
//import lombok.experimental.Accessors;
//
//@FabosJson(name = "动物",
//        orderBy = "Animal.createTime desc",
//        power = @Power(add = true, edit = true, delete = false, export = true, importable = false, viewDetails = false,
//                examineDetails = true, examine = true),
//        filter = {@Filter(value = "name is not null")},
//        formOperation = {
//                @FormOperation(
//                        name = "测试999",
//                        code = "Animal@form",
//                        position = FormOperation.Position.RIGHT,
//                        closeFormAfterClick = false,
//                        affectMode = FormOperation.AffectMode.edit,
//                        operationHandler = AnimalFormOperationHandler.class
//                )
//        },
//
//        rowOperation = {
//                @RowOperation(
//                        code = "Animal@TEST",
//                        ifExpr = "selectedItems[0].state !='Y'",
//                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
//                        mode = RowOperation.Mode.HEADER,
//                        type = RowOperation.Type.POPUP,
//                        popupType = RowOperation.PopupType.FORM,
//                        fabosJsonClass = AnimalView.class,
//                        submitMethod = RowOperation.SubmitMethod.HANDLER,
//                        operationHandler = AnimalOperationHandler.class,
//                        title = "Animal测试",
//                        show = @ExprBool(
//                                value = true,
//                                params = "Animal@TEST"
//                        )
//                ),
//        },
//        flowCode = "Animal",
//        flowProxy = AnimalFlowProxy.class,
//        drills = {
//                @Drill(title = "A测试1", code = "ATest1", icon = "fa fa-sliders", link = @Link(linkFabosJson = Dog.class, joinColumn = "id")),
//                @Drill(title = "A测试2", code = "ATest2", icon = "fa fa-sliders", link = @Link(linkFabosJson = Dog.class, joinColumn = "id"))
//        }
//)
//@Setter
//@Getter
//@Entity
//@Accessors(chain = true)
//@Table(name = "Animal")
//public class Animal extends ExamineModel {
//
//    @FabosJsonField(
//            views = @View(title = "名称"),
//            edit = @Edit(title = "名称", search = @Search(vague = true))
//    )
//    private String name;
//
//    @FabosJsonField(
//            views = @View(title = "年龄"),
//            edit = @Edit(title = "年龄", search = @Search(vague = true))
//    )
//    private Integer age;
//
//    @FabosJsonField(
//            views = @View(title = "状态"),
//            edit = @Edit(title = "状态",
//                    type = EditType.CHOICE,
//                    choiceType = @ChoiceType(fetchHandler = {YesOrNoStatus.ChoiceFetch.class}),
//                    search = @Search(vague = true))
//    )
//    private String state;
//}
