package cec.jiutian.bc.urm.domain.dictionary.service;

import cec.jiutian.common.util.StringUtils;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.frame.module.FabModule;
import cec.jiutian.bc.urm.domain.dictionary.entity.Dictionary;
import cec.jiutian.bc.urm.domain.dictionary.entity.DictionaryItem;
import cec.jiutian.bc.urm.dto.MetaDict;
import cec.jiutian.bc.urm.dto.MetaDictItem;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
public class DictionaryService {

    private final FabosJsonDao fabosJsonDao;
    private final JpaCrud jpaCrud;

    public DictionaryService(FabosJsonDao fabosJsonDao, JpaCrud jpaCrud) {
        this.fabosJsonDao = fabosJsonDao;
        this.jpaCrud = jpaCrud;
    }

    public List<MetaDictItem> getDictItems(String code) {
        List<DictionaryItem> items = fabosJsonDao.lambdaQuery(DictionaryItem.class).addCondition("dictionary.code = :code", new HashMap<String, Object>() {{
            this.put("code", code);
        }}).list();

        return items.stream().map(item -> new MetaDictItem(item.getCode(), item.getName())).collect(Collectors.toList());
    }

    public void saveMetaDictionary(List<MetaDict> items) {
        if(!CollectionUtils.isEmpty(items)){
            for (MetaDict dict : items) {
                Dictionary dictionary = Dictionary.fromMetaDict(dict);
//                dictionary.setOwnComponent(getFabModule(dict.getComponentName()));
                dictionary.setOid(dict.getOid());
                String[] values = new String[]{dict.getCode(),dict.getOid()};
                dictionary = fabosJsonDao.persistIfNotExistWithTenantId(Dictionary.class, dictionary, "code", values);
                dict.setId(dictionary.getId());
                if (!CollectionUtils.isEmpty(dict.getItems())) {
                    for (MetaDictItem op : dict.getItems()) {
                        DictionaryItem dictionaryItem = DictionaryItem.fromMetaDictItem(op);
                        dictionaryItem.setDictionary(dictionary);
                        dictionaryItem.setOid(dict.getOid());
                        DictionaryItem t = fabosJsonDao.queryEntity(DictionaryItem.class, "code = :code AND oid = :oid and dictionary.id = :id", new HashMap<String, Object>(3) {{
                            this.put("code", op.getCode());
                            this.put("oid", dict.getOid());
                            this.put("id", dict.getId());
                        }});
                        if (null == t) {
                            fabosJsonDao.persistAndFlush(dictionaryItem);
                        }
                    }
                }
            }
        }
    }

    public FabModule getFabModule(String moduleName) {
        if (StringUtils.isEmpty(moduleName)) {
            return null;
        }
        FabModule fabModule = new FabModule();
        fabModule.setName(moduleName.trim());
        List<FabModule> list = jpaCrud.select(fabModule);
        if (org.springframework.util.CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }
}
