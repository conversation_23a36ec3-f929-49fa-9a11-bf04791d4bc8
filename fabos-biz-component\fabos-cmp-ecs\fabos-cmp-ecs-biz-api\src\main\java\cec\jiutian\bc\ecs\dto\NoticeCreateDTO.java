package cec.jiutian.bc.ecs.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2023/5/29 11:33
 * @description：
 */
@Data
public class NoticeCreateDTO {

    /**
     * 三种推送方式 Email,SMS,App，多渠道推送以','分割
     */
    private String dispatchWay;

    /**
     * 推送人员
     */
    private List<PushUserDTO> users;

    /**
     * 通知标题
     */
    private String title;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 租户id 必填项
     */
    private String oid;
}
