package cec.jiutian.api.file.controller;

import cec.jiutian.api.file.feign.UbpFileFeignClient;
import cec.jiutian.api.file.model.FabosJsonFileSuper;
import cec.jiutian.api.file.utils.FileUtil;
import cec.jiutian.api.remoteCallLog.dto.RemoteCallResult;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.base.BaseApi;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import cec.jiutian.core.frame.i18n.FabosI18nTranslate;
import cec.jiutian.core.frame.service.FabosJsonCoreService;
import cec.jiutian.core.view.fabosJson.util.DateUtil;
import cec.jiutian.core.view.fabosJson.view.FabosJsonModel;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.HtmlEditorType;
import com.alibaba.fastjson2.JSON;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;


/**
 * <AUTHOR>
@Slf4j
@RestController
@RequestMapping(FabosJsonRestPath.FABOS_FILE)
public class FabosFileUploadController {

    @Autowired(required = false)
    private UbpFileFeignClient ubpFileFeign;
    @Autowired(required = false)
    private IUploadService uploadService;


    @SneakyThrows
    @PostMapping("/upload/{fabosJson}/{field}")
    @Transactional
    public BaseApi upload(@PathVariable("fabosJson") String fabosJsonName, @PathVariable("field") String fieldName, @RequestParam("file") MultipartFile file) {
        //生成存储路径
        FabosJsonModel fabosJsonModel = FabosJsonCoreService.getFabosJson(fabosJsonName);
//        FabosJsonPowerUtil.powerLegal(fabosJsonModel, powerObject -> powerObject.isEdit() || powerObject.isAdd());
        Edit edit = fabosJsonModel.getFabosJsonFieldMap().get(fieldName).getFabosJsonField().edit();

        FabosJsonFileSuper FabosJsonFileSuper = new FabosJsonFileSuper(file);
        String relativePath = File.separator + DateUtil.getFormatDate(new Date(), DateUtil.DATE);
        String modifiedFileName = FileUtil.processFileRenaming(FabosJsonFileSuper)
                .replace(edit.attachmentType().fileSeparator(), "");
        switch (edit.type()) {
            case ATTACHMENT:
                AttachmentType attachmentType = edit.attachmentType();
                //校验扩展名
                if (attachmentType.fileTypes().length > 0) {
                    String extensionName = FabosJsonFileSuper.getFilenameExtension();
                    if (Stream.of(attachmentType.fileTypes()).noneMatch(type -> type.contains(extensionName))) {
                        return BaseApi.errorApi(FabosI18nTranslate.$translate("fabosJson.upload_error.file_format") + ": " + extensionName);
                    }
                }
                if (!"".equals(attachmentType.path())) {
                    relativePath = attachmentType.path() + relativePath;
                }
                //校验文件大小
                if (attachmentType.size() > 0 && file.getSize() / 1024 > attachmentType.size()) {
                    return BaseApi.errorApi(FabosI18nTranslate.$translate("fabosJson.upload_error.size") + ": " + attachmentType.size() + "KB");
                }
                // 设置公共文件标记
                FabosJsonFileSuper.setNonProtected(attachmentType.nonProtected());
                switch (edit.attachmentType().type()) {
                    case IMAGE:
                        AttachmentType.ImageType imageType = edit.attachmentType().imageType();
                        // 通过MultipartFile得到InputStream，从而得到BufferedImage
                        BufferedImage bufferedImage = ImageIO.read(file.getInputStream());
                        if (bufferedImage == null) {
                            return BaseApi.errorApi(FabosI18nTranslate.$translate("fabosJson.upload_error.not_image"));
                        }
                        int width = bufferedImage.getWidth();
                        int height = bufferedImage.getHeight();
                        if (imageType.minWidth() > width || imageType.maxWidth() < width) {
                            return BaseApi.errorApi(FabosI18nTranslate.$translate("fabosJson.upload_error.image_width") + String.format("[%s,%s]", imageType.minWidth(), imageType.maxWidth()));
                        }
                        if (imageType.minHeight() > height || imageType.maxHeight() < height) {
                            return BaseApi.errorApi(FabosI18nTranslate.$translate("fabosJson.upload_error.image_height") + String.format("[%s,%s]", imageType.minWidth(), imageType.maxWidth()));
                        }
                        break;
                    case BASE:
                        break;
                }
                break;
            case HTML_EDITOR:
                HtmlEditorType htmlEditorType = edit.htmlEditorType();
                if (!"".equals(htmlEditorType.path())) {
                    relativePath = htmlEditorType.path() + relativePath;
                }
                break;
        }
        try {
            String result = null;
            if (Objects.isNull(uploadService)) {
                RemoteCallResult<String> callResult = ubpFileFeign.fileUpload(file, JSON.toJSONString(FabosJsonFileSuper));
                if (StringUtils.isBlank(callResult.getData())) {
                    return BaseApi.errorApi(FabosI18nTranslate.$translate("fabosJson.upload_error") + ", " + callResult.getMessage());
                }
                result = callResult.getData();
            } else {
                String errMsg = uploadService.createFile(file, relativePath, modifiedFileName, FabosJsonFileSuper);
                if (StringUtils.isNotBlank(errMsg)) {
                    return BaseApi.errorApi(FabosI18nTranslate.$translate("fabosJson.upload_error") + ", " + errMsg);
                }
                result = FabosJsonFileSuper.getModifiedFileName();
            }
            return BaseApi.successApi(result);
        } catch (Exception e) {
            log.error("fabosJson upload error", e);
            return BaseApi.errorApi(FabosI18nTranslate.$translate("fabosJson.upload_error") + " " + e.getMessage());
        }
    }

    @PostMapping("/uploads/{fabosJson}/{field}")
    @Transactional
    public BaseApi uploads(@PathVariable("fabosJson") String fabosJsonName, @PathVariable("field") String fieldName, @RequestParam("file") MultipartFile[] files) {
        List<String> paths = new ArrayList<>();
        for (MultipartFile file : files) {
            BaseApi fabosJsonApiModel = upload(fabosJsonName, fieldName, file);
            paths.add(fabosJsonApiModel.getMsg());
        }
        return BaseApi.successApi(String.join(",", paths));
    }

    public interface IUploadService {
        String createFile(MultipartFile file, String relativePath, String modifiedFileName, FabosJsonFileSuper FabosJsonFileSuper);
    }

}
