package cec.jiutian.bc.generalModeler.domain.namingRule.model;


import cec.jiutian.bc.generalModeler.domain.namingRule.proxy.NamingRuleParameterDataProxy;
import cec.jiutian.bc.generalModeler.enumeration.NamingRuleEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.config.Comment;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.Set;

@FabosJson(
        name = "规则编码参数", dataProxy = NamingRuleParameterDataProxy.class
)
@Table(name = "naming_rule_parameter",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"naming_rule_id", "parameterName"})
        }
)
@Entity
@Getter
@Setter
public class NamingRuleParameter extends MetaModel {

    //    关系字段，与关系表中的JoinColumn中指定的字段名对应
    @Comment("引用字段，对应namingrule的ID")
    @FabosJsonField(
            views = @View(title = "编码规则", column = "namingRuleCode"),
            edit = @Edit(title = "编码规则", show = false, readonly = @Readonly(add = true, edit = true), search = @Search(vague = true),
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "namingRuleCode")
            )
    )
    @ManyToOne(fetch = FetchType.LAZY)
    private NamingRule namingRule;

    @FabosJsonField(
            views = @View(title = "参数编码"),
            edit = @Edit(title = "参数编码", readonly = @Readonly(add = false, edit = true), notNull = true, search = @Search(vague = true)
            )
    )
    private String parameterName;

    @FabosJsonField(
            views = @View(title = "参数类型"),
            edit = @Edit(title = "参数类型", readonly = @Readonly(add = false, edit = true), notNull = true, type = EditType.CHOICE, search = @Search(vague = true),
                    choiceType = @ChoiceType(
                            fetchHandler = NamingRuleEnum.class
                    )
            )
    )
    private String parameterType;

    @FabosJsonField(
            views = @View(title = "依赖参数"),
            edit = @Edit(title = "依赖参数", type = EditType.input_text,
                    placeHolder = "多个参数以逗号分隔",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "parameterType == 'SR'"), notNull = true
            )
    )
    @Comment("该参数依赖哪些其他参数，参数类型为SR时使用，多个参数以逗号分隔")
    private String dependParameters;

    @FabosJsonField(
            views = @View(title = "参数序号"),
            edit = @Edit(title = "参数序号", type = EditType.NUMBER, numberType = @NumberType(min = 1, max = 100), notNull = true
            )
    )
    @Comment("参数序号")
    private Integer parameterSequence;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = false)
    @JoinColumn(name = "sr_parameter_id")
    @FabosJsonField(
            views = @View(title = "序号值列表", column = "namingRuleParameterSrValueSet"
                    , type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "序号值列表",
                    type = EditType.TAB_TABLE_ADD,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "parameterType == 'SR'"))
    )
    @Comment("该参数下生成的各个版本的序号值")
    private Set<NamingRuleParameterSrValue> namingRuleParameterSrValueSet;

    @FabosJsonField(
            views = @View(title = "常量值"),
            edit = @Edit(title = "常量值", type = EditType.input_text,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "parameterType == 'CS'"), notNull = true)
    )
    @Comment("常量值，当参数类型为CS时使用")
    private String constantValue;

    @FabosJsonField(
            views = @View(title = "起始值"),
            edit = @Edit(title = "起始值", type = EditType.NUMBER, numberType = @NumberType(min = 1),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "parameterType == 'SR'"), notNull = true)
    )
    @Comment("序号的起始值，当参数类型为SR时使用")
    private Integer startNumber;

    @FabosJsonField(
            views = @View(title = "序号位数"),
            edit = @Edit(title = "序号位数", type = EditType.NUMBER, numberType = @NumberType(min = 1, max = 30),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "parameterType == 'SR'"), notNull = true)
    )
    @Comment("序号所占位数，不足时左侧补0，生成到满位数值后，从起始值开始重新生成")
    private Integer lengthNumber;

    @FabosJsonField(
            edit = @Edit(title = "说明", notNull = false,
                    type = EditType.TEXTAREA, inputType = @InputType(length = 400))
    )
    private String desciption;

}
