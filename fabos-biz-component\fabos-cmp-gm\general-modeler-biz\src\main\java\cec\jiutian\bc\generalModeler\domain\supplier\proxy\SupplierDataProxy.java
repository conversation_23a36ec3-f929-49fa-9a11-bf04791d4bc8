package cec.jiutian.bc.generalModeler.domain.supplier.proxy;

import cec.jiutian.bc.generalModeler.domain.supplier.model.Supplier;
import cec.jiutian.bc.generalModeler.enumeration.SupplierStatusEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date ：2024/10/31 17:06
 * @description：
 */
@Component
public class SupplierDataProxy implements DataProxy<Supplier> {

    @Resource
    private JpaCrud jpaCrud;

    @Override
    public void beforeAdd(Supplier supplier) {
        supplier.setStatus(SupplierStatusEnum.Enum.Effective.getCode());
        Supplier condition = new Supplier();
        condition.setSupplierCode(supplier.getSupplierCode());
        Supplier data = jpaCrud.selectOne(condition);
        if (data != null) {
            throw new FabosJsonApiErrorTip("供应商编码不可重复，请确认");
        }
        supplier.setStatus(SupplierStatusEnum.Enum.Effective.getCode());
    }

    @Override
    public void beforeDelete(Supplier supplier) {
        if (SupplierStatusEnum.Enum.Effective.getCode().equals(supplier.getStatus())) {
            if (SupplierStatusEnum.Enum.Effective.getCode().equals(supplier.getStatus())) {
                throw new FabosJsonApiErrorTip("已生效供应商不可删除");
            }
        }
    }
}
