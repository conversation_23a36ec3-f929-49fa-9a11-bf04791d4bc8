package cec.jiutian.bc.urm.domain.user.entity;

import cec.jiutian.bc.urm.dto.MetaUserinfo;
import cec.jiutian.core.frame.config.SystemSecrecyConfig;
import cec.jiutian.core.frame.i18n.FabosI18nTranslate;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Map;

@Getter
@Setter
public class LoginModel {

    private boolean pass; //校验是否通过

    private boolean resetPwd = false; //是否需要重置密码

    private boolean defaultPwd = false; //是否默认密码,默认密码需要变更

    private boolean useVerifyCode = false; //是否需要验证码

    private boolean toLogin = false; //锁屏过后是否调整到登录页

    private boolean needPhoneNumber=false;

    private String reason; //未校验通过原因

    private String token;

    private LocalDateTime expire;

    private Map<String, Object> authorizationInfo; // access/refresh token

    private transient User user;

    private MetaUserinfo metaUserinfo;
    private String level = SystemSecrecyConfig.LEVEL;

    public LoginModel(boolean pass, User user) {
        this.pass = pass;
        this.setUser(user);
    }

    public LoginModel(boolean pass, User user, MetaUserinfo metaUserinfo) {
        this.pass = pass;
        this.setUser(user);
        this.setMetaUserinfo(metaUserinfo);
    }

    public LoginModel(boolean pass, String reason) {
        this.pass = pass;
        this.reason = FabosI18nTranslate.$translate(reason);;
    }

    public LoginModel(boolean pass, String reason, boolean useVerifyCode) {
        this.pass = pass;
        this.reason = FabosI18nTranslate.$translate(reason);
        this.useVerifyCode = useVerifyCode;
    }

    public LoginModel(boolean pass, String reason, boolean useVerifyCode,boolean needPhoneNumber) {
        this.pass = pass;
        this.reason = FabosI18nTranslate.$translate(reason);
        this.useVerifyCode = useVerifyCode;
        this.needPhoneNumber = needPhoneNumber;
    }

    public LoginModel() {
    }

}
