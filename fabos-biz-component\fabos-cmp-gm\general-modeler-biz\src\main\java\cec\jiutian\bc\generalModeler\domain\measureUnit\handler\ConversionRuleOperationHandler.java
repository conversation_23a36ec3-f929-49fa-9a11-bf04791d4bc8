package cec.jiutian.bc.generalModeler.domain.measureUnit.handler;

import cec.jiutian.bc.generalModeler.domain.measureUnit.model.UnitConversionRule;
import cec.jiutian.bc.generalModeler.domain.measureUnit.model.UnitConversionRuleData;
import cec.jiutian.bc.generalModeler.domain.measureUnit.service.UnitConversionRuleDomainService;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2024/10/25 11:19
 * @description：
 */
@Component
public class ConversionRuleOperationHandler implements OperationHandler<UnitConversionRule, UnitConversionRuleData> {

    @Resource
    private UnitConversionRuleDomainService conversionRuleDomainService;

    @Override
    public String exec(List<UnitConversionRule> data, UnitConversionRuleData modelObject, String[] param) {
        return null;
    }

    @Override
    public UnitConversionRuleData fabosJsonFormValue(List<UnitConversionRule> data, UnitConversionRuleData fabosJsonForm, String[] param) {
//       if (CollectionUtils.isNotEmpty(data)) {
//           UnitConversionRule unitConversionRule = data.get(0);
//           UnitConversionRuleData ruleData = new UnitConversionRuleData();
//           ruleData.setSoruceMeasureUnit(unitConversionRule.getSoruceMeasureUnit());
//           ruleData.setTargetMeasureUnit(unitConversionRule.getTargetMeasureUnit());
//           ruleData.setFormula(unitConversionRule.getFormula());
//           ruleData.setCalculateFormula(unitConversionRule.getCalculateFormula());
//           //ruleData.setRuleDescription(unitConversionRule.getRuleDescription());
//           ruleData.setPrecision(unitConversionRule.getPrecision());
//           ruleData.setFormulaTemplate(unitConversionRule.getFormulaTemplate());
//           ruleData.setCoefficient(unitConversionRule.getCoefficient());
//           ruleData.setConstantTerm(unitConversionRule.getConstantTerm());
//
//           return ruleData;
//       }
        return fabosJsonForm;
    }

}
