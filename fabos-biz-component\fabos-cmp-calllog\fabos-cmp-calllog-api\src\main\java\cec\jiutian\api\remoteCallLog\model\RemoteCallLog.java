package cec.jiutian.api.remoteCallLog.model;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.BoolType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Formula;

import java.util.Date;

/**
 * 远程调用日志
 */
@Entity
@Table(name = "fd_remote_call_log")
@FabosJson(
        name = "远程调用日志", power = @Power(add = false, edit = false, delete = false, importable = false, export = false)
)
@Getter
@Setter
public class RemoteCallLog extends MetaModel {

    // 调用方式（rest、feign、dubbo）
    @FabosJsonField(
            views = @View(title = "调用方式"),
            edit = @Edit(title = "调用方式", search = @Search)
    )
    private String callMethod;

    // 接口名称
    @FabosJsonField(
            views = @View(title = "接口名称"),
            edit = @Edit(title = "接口名称", search = @Search)
    )
    private String interfaceName;

    // 发起方系统编码
    @FabosJsonField(
            views = @View(title = "发起方编码"),
            edit = @Edit(title = "发起方编码", search = @Search)
    )
    private String issuerCode;

    // 发起方业务数据标识（主键ID + 创建时间戳）
    @FabosJsonField(
            views = @View(title = "发起方业务数据标识"),
            edit = @Edit(title = "发起方业务数据标识", search = @Search)
    )
    private String issuerDataId;

    // 远程目标系统编码(ERP、MES、WMS)
    @FabosJsonField(
            views = @View(title = "目标编码"),
            edit = @Edit(title = "目标编码", search = @Search)
    )
    private String remoteTargetCode;

    // 请求地址
    @FabosJsonField(
            views = @View(title = "请求地址"),
            edit = @Edit(title = "请求地址", search = @Search(vague = true))
    )
    private String requestUrl;

    // 请求消息体
    @FabosJsonField(
            views = @View(title = "请求消息体", toolTip = true),
            edit = @Edit(title = "请求消息体", type = EditType.TEXTAREA)
    )
    private String requestBody;

    // 响应消息体
    @FabosJsonField(
            views = @View(title = "响应消息体", toolTip = true),
            edit = @Edit(title = "响应消息体", type = EditType.TEXTAREA)
    )
    private String responseBody;

    // 请求时间
    @FabosJsonField(
            views = @View(title = "请求时间"),
            edit = @Edit(title = "请求时间", type = EditType.DATE, search = @Search(vague = true))
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date requestTime;

    // 是否成功 （重试成功也更新该标记）
    @FabosJsonField(
            views = @View(title = "是否成功"),
            edit = @Edit(title = "是否成功", type = EditType.BOOLEAN, boolType = @BoolType(trueText = "成功", falseText = "失败"), search = @Search)
    )
    @Column(name = "success_flag")
    private Boolean successFlag;

    @Formula("case when success_flag then '成功' else '失败' end")
    private String successFlagShowField;

    // 重试时间 (最近一次)
    @FabosJsonField(
            views = @View(title = "最近重试时间"),
            edit = @Edit(title = "最近重试时间", type = EditType.DATE, search = @Search(vague = true))
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date retryTime;

    // 重试次数
    @FabosJsonField(
            views = @View(title = "重试次数"),
            edit = @Edit(title = "重试次数")
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Integer retryCount;

    // 重试响应消息体（最近一次）
    @FabosJsonField(
            views = @View(title = "重试响应消息体", toolTip = true),
            edit = @Edit(title = "重试响应消息体", type = EditType.TEXTAREA)
    )
    private String retryResponseBody;
}
