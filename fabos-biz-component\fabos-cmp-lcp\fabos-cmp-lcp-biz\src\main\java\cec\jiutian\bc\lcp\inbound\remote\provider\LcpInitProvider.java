package cec.jiutian.bc.lcp.inbound.remote.provider;

import cec.jiutian.bc.urm.dto.InitDataDTO;
import cec.jiutian.bc.urm.dto.MetaDict;
import cec.jiutian.bc.urm.dto.MetaMenu;
import cec.jiutian.component.utils.ReadInitMenuDataFromCSVUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 接收数据初始化的通知，并初始化本业务组件内的自定义菜单与自定义字典数据。
 *
 */
@Slf4j
@Component
public class LcpInitProvider implements InitDataDTO.IInitDataProvider {

    @Override
    public List<MetaMenu> initData(InitDataDTO dto) {
        List<MetaMenu> menus = new ArrayList<>();
        Optional.ofNullable(ReadInitMenuDataFromCSVUtil.convertCsvToList(dto.getComponentName())).ifPresent(e -> menus.addAll(e));
        return menus;
    }

    @Override
    public List<MetaDict> initDict(InitDataDTO dto) {
        return null;
    }
}
