package cec.jiutian.bc.job.domain.mail.event;

import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.util.FabosJsonPowerUtil;
import cec.jiutian.bc.job.domain.mail.entity.JobMail;
import cec.jiutian.view.fun.DataProxy;
import jakarta.mail.internet.MimeMessage;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class JobMailDataProxy implements DataProxy<JobMail> {

    @Autowired(required = false)
    private JavaMailSenderImpl javaMailSender;

    @SneakyThrows
    @Override
    public void beforeAdd(JobMail jobMail) {
        FabosJsonPowerUtil.requireNonNull(javaMailSender, "Sending mailbox not configured");
        MimeMessage mimeMessage = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, StandardCharsets.UTF_8.name());
        jobMail.setCreateBy(UserContext.getAccount());
        jobMail.setCreateTime(LocalDateTime.now());
        helper.setSubject(jobMail.getSubject());
        helper.setTo(jobMail.getRecipient());
        helper.setFrom(Objects.requireNonNull(javaMailSender.getUsername()));
        if (StringUtils.isNotBlank(jobMail.getCc())) helper.setCc(jobMail.getCc().split("\\|"));
        helper.setText(jobMail.getContent(), true);
        try {
            javaMailSender.send(mimeMessage);
            jobMail.setStatus(true);
        } catch (Exception e) {
            log.error("mail send error", e);
            jobMail.setStatus(false);
            Optional.ofNullable(e.toString()).ifPresent(it -> jobMail.setErrorInfo(it.substring(0, 5000)));
        }
    }

}
