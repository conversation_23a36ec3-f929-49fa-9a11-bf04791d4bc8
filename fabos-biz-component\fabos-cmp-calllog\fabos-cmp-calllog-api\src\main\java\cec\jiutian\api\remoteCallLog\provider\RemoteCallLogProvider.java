package cec.jiutian.api.remoteCallLog.provider;

import cec.jiutian.api.remoteCallLog.dto.RemoteCallLogDto;
import cec.jiutian.api.remoteCallLog.dto.RemoteCallLogRetryDto;
import cec.jiutian.api.remoteCallLog.model.RemoteCallLog;
import cec.jiutian.api.remoteCallLog.port.mq.RemoteCallLogSender;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.meta.model.MetadataModel;
import jakarta.persistence.Table;
import org.springframework.stereotype.Component;

import java.util.HashMap;

@Component
public class RemoteCallLogProvider {

    private final FabosJsonDao fabosJsonDao;

    private final RemoteCallLogSender remoteCallLogSender;

    public RemoteCallLogProvider(FabosJsonDao fabosJsonDao, RemoteCallLogSender remoteCallLogSender) {
        this.fabosJsonDao = fabosJsonDao;
        this.remoteCallLogSender = remoteCallLogSender;
    }

    /**
     * 发送远程调用日志消息
     *
     * @param remoteCallLogDto 远程调用日志
     */
    public void sendRemoteCallLogMessage(RemoteCallLogDto remoteCallLogDto) {
        RemoteCallLog remoteCallLog = new RemoteCallLog();
        remoteCallLog.setCallMethod(remoteCallLogDto.getCallMethod());
        remoteCallLog.setInterfaceName(remoteCallLogDto.getInterfaceName());
        remoteCallLog.setIssuerCode(remoteCallLogDto.getIssuerCode());
        remoteCallLog.setRemoteTargetCode(remoteCallLogDto.getRemoteTargetCode());
        remoteCallLog.setRequestUrl(remoteCallLogDto.getRequestUrl());
        remoteCallLog.setRequestTime(remoteCallLogDto.getRequestTime());
        remoteCallLog.setRequestBody(remoteCallLogDto.getRequestBody());
        remoteCallLog.setResponseBody(remoteCallLogDto.getResponseBody());
        remoteCallLog.setSuccessFlag(remoteCallLogDto.getSuccessFlag());
        remoteCallLog.setIssuerDataId(remoteCallLogDto.getBussinessDataId() + "-" + System.currentTimeMillis());


        String businessTableName = null;
        final Class<?> classObject = remoteCallLogDto.getBussinessModelClass();
        if (classObject.isAnnotationPresent(Table.class)) {
            businessTableName = classObject.getAnnotation(Table.class).name();
        } else {
            MetadataModel metadataModel = fabosJsonDao.queryEntity(MetadataModel.class, "code = :code", new HashMap<String, Object>(4) {{
                this.put("code", remoteCallLogDto.getBussinessModelClass().getName());
            }});
            businessTableName = metadataModel.getName();
        }

        if (businessTableName == null) {
            throw new RuntimeException("未找到业务数据表名");
        } else {
            fabosJsonDao.getEntityManager().createNativeQuery("update " + businessTableName + " set remote_call_log_id = ?1 where id = ?2")
                    .setParameter(1, remoteCallLog.getIssuerDataId())
                    .setParameter(2, remoteCallLogDto.getBussinessDataId())
                    .executeUpdate();
        }

        // 记录日志
        remoteCallLogSender.sendRemoteCallLogMessage(remoteCallLog);

    }

    /**
     * 重试远程调用日志消息
     *
     * @param remoteCallLogRetryDto 重试日志
     */
    public void resendRemoteCallLogMessage(RemoteCallLogRetryDto remoteCallLogRetryDto) {
        RemoteCallLog remoteCallLog = remoteCallLogRetryDto.getRemoteCallLog();
        remoteCallLog.setRetryCount(remoteCallLog.getRetryCount() == null ? 1 : remoteCallLog.getRetryCount() + 1);
        remoteCallLog.setRetryTime(remoteCallLogRetryDto.getRetryTime());
        remoteCallLog.setRetryResponseBody(remoteCallLogRetryDto.getRetryResponseBody());
        remoteCallLog.setSuccessFlag(remoteCallLogRetryDto.getSuccessFlag());
        // 记录日志
        remoteCallLogSender.sendRemoteCallLogMessage(remoteCallLog);

    }
}
