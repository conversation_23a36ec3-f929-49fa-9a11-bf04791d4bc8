package cec.jiutian.bc.urm.ModelRowPermission.event;

import cec.jiutian.bc.urm.ModelRowPermission.entity.ModelRowPermission;
import cec.jiutian.meta.model.MetadataModel;
import cec.jiutian.view.fun.DataProxy;
import org.springframework.stereotype.Component;

@Component
public class ModelRowPermissionDataProxy implements DataProxy<ModelRowPermission> {
    @Override
    public void beforeAdd(ModelRowPermission modelRowPermission) {
        MetadataModel model = modelRowPermission.getModel();
        modelRowPermission.setMetadataId(model.getId());
        modelRowPermission.setModelDisplayName(model.getDisplayName());
    }
}
