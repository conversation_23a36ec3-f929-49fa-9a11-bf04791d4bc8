package cec.jiutian.bc.job.domain.mail.service;

import cec.jiutian.view.expr.ExprBool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.mail.MailProperties;
import org.springframework.stereotype.Service;


@Service
public class NotifyEmailRender implements ExprBool.ExprHandler {

    @Autowired(required = false)
    private MailProperties mailProperties;

    @Override
    public boolean handler(boolean expr, String[] params) {
        return null != mailProperties;
    }

}
