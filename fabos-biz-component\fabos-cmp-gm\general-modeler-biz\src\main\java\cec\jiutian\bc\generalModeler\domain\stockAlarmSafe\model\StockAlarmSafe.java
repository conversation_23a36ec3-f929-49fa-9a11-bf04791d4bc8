package cec.jiutian.bc.generalModeler.domain.stockAlarmSafe.model;

import cec.jiutian.bc.generalModeler.domain.material.model.StockView;
import cec.jiutian.bc.generalModeler.domain.stockAlarmSafe.proxy.StockAlarmSafeDataProxy;
import cec.jiutian.bc.generalModeler.enumeration.ConfigControlTypeEnum;
import cec.jiutian.bc.generalModeler.enumeration.ProductEnum;
import cec.jiutian.bc.generalModeler.enumeration.StockTypeEnum;
import cec.jiutian.bc.generalModeler.enumeration.WarehouseBlockPurposeEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import jakarta.persistence.ConstraintMode;
import jakarta.persistence.Entity;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

@Table(name = "BWS_STOCK_ALARM_SAFE", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"materialCode", "configStorageAreaType"})
})
@FabosJson(
        orderBy = "StockAlarmSafe.createTime desc",
        name = "安全库存配置",
        dataProxy = StockAlarmSafeDataProxy.class
)
@Entity
@Getter
@Setter
public class StockAlarmSafe extends MetaModel {


    @FabosJsonField(
            views = @View(title = "物资名称", column = "name"),
            edit = @Edit(title = "物资名称",
                    readonly = @Readonly(add = false),
                    type = EditType.REFERENCE_TABLE,
                    notNull = true, search = @Search(vague = true),
                    referenceTableType = @ReferenceTableType(),
                    filter = @Filter(value = "StockView.currentStatus = 'ACTIVE'")
            )
    )
    @ManyToOne
    @JoinColumn(name = "stock_view_id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    private StockView material;

    @FabosJsonField(
            views = @View(title = "物资编号"),
            edit = @Edit(title = "物资编号", readonly = @Readonly(), show = false, search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "code")
            )
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "品号"),
            edit = @Edit(title = "品号", readonly = @Readonly(), search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "brandCode"))
    )
    private String brandCode;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格", readonly = @Readonly(), search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "specification")
            )
    )
    private String materialSpecification;


    @FabosJsonField(
            views = @View(title = "单位"),
            edit = @Edit(title = "单位", readonly = @Readonly()),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "accountUnit_unitChnName")
            )
    )
    private String unit;

    @FabosJsonField(
            views = @View(title = "类别"),
            edit = @Edit(title = "类别", type = EditType.CHOICE, readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = StockTypeEnum.class)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "stockType")
            )
    )
    private String stockType;
    @FabosJsonField(
            views = @View(title = "子类别"),
            edit = @Edit(title = "子类别", type = EditType.CHOICE, readonly = @Readonly,
                    choiceType = @ChoiceType(fetchHandler = ProductEnum.class)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "material", beFilledBy = "stockSubType")
            )
    )
    private String stockSubType;

    @FabosJsonField(
            views = @View(title = "区域类型"),
            edit = @Edit(title = "区域类型", readonly = @Readonly(add = false),
                    type = EditType.CHOICE, notNull = true, search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = WarehouseBlockPurposeEnum.class), defaultVal = "Unlimited")
    )
    private String configStorageAreaType;


    @FabosJsonField(
            views = @View(title = "控制类型"),
            edit = @Edit(title = "控制类型",
                    type = EditType.CHOICE, notNull = true,
                    choiceType = @ChoiceType(fetchHandler = ConfigControlTypeEnum.class), defaultVal = "Both")
    )
    private String configControlType;


    @FabosJsonField(
            views = @View(title = "库存上限值"),
            edit = @Edit(title = "库存上限值",
                    numberType = @NumberType(min = 0, max = 9999999, precision = 2), dependFieldDisplay = @DependFieldDisplay(notNull = "configControlType == 'UpperLimit' || configControlType == 'Both'", showOrHide = "configControlType == 'UpperLimit' || configControlType == 'Both'"))
    )
    private Double configHighValue;

    @FabosJsonField(
            views = @View(title = "库存下限值"),
            edit = @Edit(title = "库存下限值",
                    numberType = @NumberType(min = 0, max = 9999999, precision = 2), dependFieldDisplay = @DependFieldDisplay(notNull = "configControlType == 'LowLimit' || configControlType == 'Both'", showOrHide = "configControlType == 'LowLimit' || configControlType == 'Both'"))
    )
    private Double configLowValue;


//    @FabosJsonField(
//            views = @View(title = "单位"),
//            edit = @Edit(title = "单位")
//    )
//    private String configUnit;


//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
//    @FabosJsonField(
//            views = @View(title = "排除日期", type = ViewType.DATE),
//            edit = @Edit(title = "排除日期", notNull = true,
//                    readonly = @Readonly(add = false),
//                    type = EditType.DATE, dateType = @DateType(type = DateType.Type.DATE, pickerMode = DateType.PickerMode.ALL))
//    )
//    private Date eliminateDate;
//
//    @FabosJsonField(
//            views = @View(title = "排除标记"),
//            edit = @Edit(title = "排除标记",
//                    type = EditType.CHOICE,
//                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class))
//    )
//    private String eliminateFlag;

}
