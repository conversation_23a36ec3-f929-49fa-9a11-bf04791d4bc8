package cec.jiutian.bc.generalModeler.domain.namingRule.proxy;


import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRule;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleParameter;
import cec.jiutian.bc.generalModeler.enumeration.NamingRuleEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.view.fun.DataProxy;

import java.util.Arrays;
import java.util.List;

public class NamingRuleDataProxy implements DataProxy<NamingRule> {

    @Override
    public void beforeAdd(NamingRule namingRule) {
//      校验SR类型参数指定的依赖参数的有效性,及必须有依赖参数
        if (!checkSrParameterDependencies(namingRule.getNamingRuleParameterList())) {
            throw new FabosJsonApiErrorTip("顺序号类型（SR）必须正确的录入依赖参数，请检查！");
        }
        // 设置子模型的父模型对象
        for (NamingRuleParameter namingRuleParameter : namingRule.getNamingRuleParameterList()) {
            namingRuleParameter.setNamingRule(namingRule);
        }


    }


    @Override
    public void beforeUpdate(NamingRule namingRule) {
//      校验SR类型参数指定的依赖参数的有效性
        if (!checkSrParameterDependencies(namingRule.getNamingRuleParameterList())) {
            throw new FabosJsonApiErrorTip("顺序号类型（SR）必须正确的录入依赖参数，请检查！");
//            迁移后使用国际化
//            throw new FabosJsonApiErrorTip(FabosI18nTranslate.$translate("namingRule.depend_parameter_not_exist"));
        }
        // 设置子模型的父模型对象
        for (NamingRuleParameter namingRuleParameter : namingRule.getNamingRuleParameterList()) {
            namingRuleParameter.setNamingRule(namingRule);
        }
    }

    public static boolean checkSrParameterDependencies(List<NamingRuleParameter> namingRuleParameters) {
        for (NamingRuleParameter parameter : namingRuleParameters) {
            if (NamingRuleEnum.Enum.SR.name().equals(parameter.getParameterType())) {
                if (parameter.getDependParameters() == null) {
                    return false;
                }
                String[] dependencies = parameter.getDependParameters().split(",");
                // 检查是否有依赖参数
                if (dependencies.length < 1) {
                    return false;
                }
                // 检查依赖参数中是否包含自身
                if (Arrays.stream(dependencies).anyMatch(parameter.getParameterName()::equals)) {
                    return false;
                }
                // 检查依赖参数是否存在
                for (String dependency : dependencies) {
                    boolean found = false;
                    for (NamingRuleParameter p : namingRuleParameters) {
                        if (p.getParameterName().equals(dependency.trim())) {
                            found = true;
                            break;
                        }
                    }
                    if (!found) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

}
