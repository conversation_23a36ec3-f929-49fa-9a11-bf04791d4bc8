package cec.jiutian.bc.ecs.inbound.local.enums;


import lombok.Getter;

import java.util.HashMap;

@Getter
public enum DispatchWayEnum {

    EMAIL("邮件", DispatchWayEnum.EMAIL_CODE),

    SMS("短信", DispatchWayEnum.SMS_CODE),

    APP("站内信", DispatchWayEnum.APP_CODE),

    WECHAT("微信", DispatchWayEnum.WECHAT_CODE);

    private final String way;
    private final String code;

    public final static String EMAIL_CODE = "Email";
    public final static String SMS_CODE = "SMS";
    public final static String APP_CODE = "App";
    public final static String WECHAT_CODE = "WeChat";

    DispatchWayEnum(String way, String code) {
        this.way = way;
        this.code = code;
    }

    public String getWay() {
        return way;
    }

    public String getCode() {
        return code;
    }

    private static final HashMap<String, String> wayMap = new HashMap<>();
    static {
        for (DispatchWayEnum dispatchWayEnum : DispatchWayEnum.values()) {
            wayMap.put(dispatchWayEnum.getCode(), dispatchWayEnum.getWay());
        }
    }

    public static String getWayByCode(String code) {
        return wayMap.get(code);
    }
}
