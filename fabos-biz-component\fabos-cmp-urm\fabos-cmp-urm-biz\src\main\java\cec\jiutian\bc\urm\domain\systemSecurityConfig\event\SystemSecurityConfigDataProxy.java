package cec.jiutian.bc.urm.domain.systemSecurityConfig.event;

import cec.jiutian.bc.urm.domain.systemBasicConfig.entity.SystemBasicConfig;
import cec.jiutian.bc.urm.domain.systemSecurityConfig.entity.SystemSecurityConfig;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.fc.log.domain.permissionOperationLog.manager.PermissionLogManager;
import cec.jiutian.fc.log.enums.OperationTargetEnum;
import cec.jiutian.fc.log.enums.OperationTypeEnum;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.TypedQuery;
import org.springframework.stereotype.Component;

@Component
public class SystemSecurityConfigDataProxy implements DataProxy<SystemSecurityConfig> {

    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private PermissionLogManager permissionLogManager;

    @Override
    public void beforeAdd(SystemSecurityConfig systemBasicConfig) {
        EntityManager entityManager = fabosJsonDao.getEntityManager();
        // 创建查询
        try {
            TypedQuery<SystemSecurityConfig> query =
                    entityManager.createQuery("SELECT s FROM SystemSecurityConfig s", SystemSecurityConfig.class);
            // 设置分页参数
            query.setFirstResult(0);
            query.setMaxResults(1);
            SystemSecurityConfig result = query.getSingleResult();
            if (result != null) {
                throw new FabosJsonApiErrorTip("已存在系统安全配置，不能再继续添加");
            }
        } catch (NoResultException e) {
            return;
        }
    }

    @Override
    public void beforeDelete(SystemSecurityConfig systemBasicConfig) {
        throw new FabosJsonApiErrorTip("系统安全配置不得删除！");
    }
    @Override
    public void afterDelete(SystemSecurityConfig systemBasicConfig) {
        permissionLogManager.addOrDelete(systemBasicConfig, OperationTargetEnum.Enum.CONFIG, OperationTypeEnum.Enum.DELETE);
    }
    @Override
    public void afterAdd(SystemSecurityConfig systemBasicConfig) {
        permissionLogManager.addOrDelete(systemBasicConfig, OperationTargetEnum.Enum.CONFIG, OperationTypeEnum.Enum.ADD);
    }

    @Override
    public void beforeUpdate(SystemSecurityConfig systemBasicConfig) {
        EntityManager entityManager = fabosJsonDao.getEntityManager();
        entityManager.clear();
        SystemSecurityConfig systemSecurityConfigDb = entityManager.find(SystemSecurityConfig.class, systemBasicConfig.getId());
        permissionLogManager.update(systemSecurityConfigDb,systemBasicConfig, OperationTargetEnum.Enum.CONFIG, OperationTypeEnum.Enum.UPDATE);

    }
}
