package cec.jiutian.bc.urm.inbound.local.service.command.init;


import cec.jiutian.bc.urm.domain.menu.entity.Menu;
import cec.jiutian.bc.urm.inbound.local.config.SystemConfig;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description:  控制初始化的菜单权限问题
 */
@Service
public class RoleMenuService {
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private SystemConfig systemConfig;

    /**
     *  获取超级管理员菜单权限
     * @return
     */
    public List<Menu> getSuperUserMenuList(){
        //超级管理员暂时不需要配置权限   鉴权处统一特殊处理。拥有所有权限。
        return fabosJsonDao.queryEntityList(Menu.class);
    }

    /**
     * 系统管理员权限
     * @return
     */
    public List<Menu> getSystemMenuList(){
        return getByMenuValueList(systemConfig.getSystemPermissionList());
    }

    /**
     * 安全审计员权限
     * @return
     */
    public List<Menu> getAuditMenuList(){
        return getByMenuValueList(systemConfig.getAuditPermissionList());
    }

    /**
     * 安全保密员权限
     * @return
     */
    public List<Menu> getSecurityMenuList(){
        return getByMenuValueList(systemConfig.getSecurityPermissionList());
    }

    private List<Menu> getByMenuValueList(List<String> menuValues){
        EntityManager em = fabosJsonDao.getEntityManager();
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Menu> cq = cb.createQuery(Menu.class);
        Root<Menu> root = cq.from(Menu.class);
        cq.select(root).where(cb.in(root.get("moduleValue")).value(menuValues));
        TypedQuery<Menu> q = em.createQuery(cq);
        return q.getResultList();
    }
}
