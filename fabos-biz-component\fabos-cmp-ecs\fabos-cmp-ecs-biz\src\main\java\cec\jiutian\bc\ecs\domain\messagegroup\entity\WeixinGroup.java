package cec.jiutian.bc.ecs.domain.messagegroup.entity;

import cec.jiutian.bc.ecs.domain.messagegroup.event.WeixinGroupDataProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "ecs_weixin_group")
@FabosJson(
        name = "微信群组管理",
        orderBy = "WeixinGroup.createTime desc",
        dataProxy = WeixinGroupDataProxy.class
)
@Getter
@Setter
public class WeixinGroup extends MetaModel {

    /**
     * 微信组名称
     */
    @FabosJsonField(
            views = @View(title = "微信组名称"),
            edit = @Edit(title = "微信组名称", search = @Search(vague = true))
    )
    private String name;

    /**
     * 微信组管理员
     * 微信组管理员
     */
    @FabosJsonField(
            views = @View(title = "微信组管理员"),
            edit = @Edit(title = "微信组管理员", tips = "录入管理员ID", search = @Search(vague = true))
    )
    private String managerId;

    /**
     * 微信组ID
     * 微信组ID，由微信接口返回
     */
    @FabosJsonField(
            views = @View(title = "群组ID"),
            edit = @Edit(title = "群组ID", readonly = @Readonly, search = @Search())
    )
    private String chatId;

    /**
     * 微信组成员
     */
    @FabosJsonField(
            views = @View(title = "微信组成员"),
            edit = @Edit(title = "微信组成员", tips = "录入成员ID，多个成员用逗号分隔。需至少两个成员", formColumns = 3, type = EditType.TEXTAREA, search = @Search(vague = true))
    )
    private String memberIds;

    /**
     * 备注
     */
    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注", formColumns = 3, type = EditType.TEXTAREA)
    )
    private String description;


}
