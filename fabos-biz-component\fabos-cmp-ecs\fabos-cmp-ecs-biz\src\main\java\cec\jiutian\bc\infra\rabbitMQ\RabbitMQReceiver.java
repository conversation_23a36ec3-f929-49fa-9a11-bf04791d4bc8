package cec.jiutian.bc.infra.rabbitMQ;

import cec.jiutian.bc.ecs.config.EcsMessageRabbitMQConfig;
import cec.jiutian.bc.ecs.domain.message.service.MessageService;
import cec.jiutian.bc.ecs.dto.SendAlarmMessageDTO;
import cec.jiutian.bc.ecs.dto.SendMsgGroupDTO;
import cec.jiutian.bc.ecs.dto.SendMsgToPersonDTO;
import cec.jiutian.bc.infra.remote.controller.WebSocketServer;
import cec.jiutian.common.util.JacksonUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Service
@Slf4j
@DependsOn("ecsMessageRabbitMQConfig")
public class RabbitMQReceiver {

    @Resource
    private MessageService messageService;

    @Resource
    private EcsMessageRabbitMQConfig ecsMessageRabbitMQConfig;
//    @RabbitListener(queues = "#{@ecsMessageRabbitMQConfig.getEcsQueueName()}")
//    public void receiveEcsMessage(String message) {
//        log.info("Received ECS message: " + message);
//        MessageCreateDTO messageCreateDTO = null;
//        try {
//            messageCreateDTO = JacksonUtil.fromJson(message, MessageCreateDTO.class);
//        } catch (IOException e) {
//            log.error("消息json转换失败：{}", message);
//            return;
//        }
//        try {
//            messageService.processMessage(messageCreateDTO);
//        } catch (Exception e){
//            log.error("消息发送失败：{}", e.getMessage());
//        }
//    }

    @RabbitListener(queues = "#{@ecsMessageRabbitMQConfig.getGroupQueueName()}")
    public void receiveGroupMessage(String message) {
        log.info("Received Group message: " + message);
        SendMsgGroupDTO sendMsgGroupDTO = null;
        try {
            sendMsgGroupDTO = JacksonUtil.fromJson(message, SendMsgGroupDTO.class);
        } catch (IOException e) {
            log.error("消息json转换失败：{}", message);
            return;
        }
        try {
            messageService.sendMsgToGroup(sendMsgGroupDTO);
        } catch (Exception e) {
            log.error("消息发送失败：{}", e.getMessage());
        }
    }

    @RabbitListener(queues = "#{@ecsMessageRabbitMQConfig.getPersonQueueName()}")
    public void receivePersonMessage(String message) {
        log.info("Received Person message: " + message);
        SendMsgToPersonDTO sendMsgToPersonDTO = null;
        try {
            sendMsgToPersonDTO = JacksonUtil.fromJson(message, SendMsgToPersonDTO.class);
        } catch (IOException e) {
            log.error("消息json转换失败：{}", message);
            return;
        }

        try {
            messageService.sendMsgToPerson(sendMsgToPersonDTO);
        } catch (Exception e){
            log.error("消息发送失败：{}", e.getMessage());
        }
    }

    @RabbitListener(queues = "#{@ecsMessageRabbitMQConfig.getAlarmQueueName()}")
    public void receiveAlarmMessage(String message) {
        log.info("Received Alarm message: " + message);
        SendAlarmMessageDTO sendAlarmMessageDTO = null;
        try {
            sendAlarmMessageDTO = JacksonUtil.fromJson(message, SendAlarmMessageDTO.class);
        } catch (IOException e) {
            log.error("消息json转换失败：{}", message);
        }
        try {
            SendAlarmMessageDTO finalSendAlarmMessageDTO = sendAlarmMessageDTO;
            sendAlarmMessageDTO.getReceivers().forEach(
                    receiver -> {
                        try {
                            WebSocketServer.sendImmediateAlarm(finalSendAlarmMessageDTO.getContent(), receiver);
                        } catch (Exception e) {
                            log.error("报警消息发送失败：{}", e.getMessage());
                        }
                    }
            );
        } catch (Exception e) {
            log.error("即时报警消息发送失败：{}", e.getMessage());
        }
    }


}
