//package cec.jiutian.bc.ecs.domain.notice.event;
//
//import cec.jiutian.bc.ecs.domain.notice.entity.Notice;
//import cec.jiutian.bc.ecs.inbound.local.enums.DispatchWayEnum;
//import cec.jiutian.bc.ecs.outbound.adapter.client.WorkflowClient;
//import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
//import cec.jiutian.core.frame.module.MetaUser;
//import cec.jiutian.view.fun.DataProxy;
//import com.alibaba.fastjson2.JSONObject;
//import org.springframework.stereotype.Service;
//import org.springframework.util.CollectionUtils;
//
//import java.util.*;
//
//@Service
//public class NoticeDataProxy implements DataProxy<Notice> {
//    private final WorkflowClient workflowClient;
//    public NoticeDataProxy(WorkflowClient workflowClient) {
//        this.workflowClient = workflowClient;
//    }
//
//    private static final String businessCategory="NoticeMessage";
//
//    @Override
//    public void beforeAdd(Notice notice) {
//        List<MetaUser> users = notice.getUsers();
//        if (CollectionUtils.isEmpty(users)) {
//            throw new FabosJsonApiErrorTip("请添加推送人员");
//        }
//        setJoiner(users, ",", notice);
//    }
//
//    @Override
//    public void beforeUpdate(Notice notice) {
//        List<MetaUser> users = notice.getUsers();
//        if (CollectionUtils.isEmpty(users)) {
//            throw new FabosJsonApiErrorTip("请添加推送人员");
//        }
//        setJoiner(users, ",", notice);
//    }
//
//    @Override
//    public void afterAdd(Notice notice) {
//        //发起审批流程
////        startFlow(notice);
//    }
//
//    private void startFlow(Notice notice){
//        workflowClient.startOrCompleteTask("message",businessCategory,notice.getId(), JSONObject.from(notice));
//    }
//
//    private void setJoiner(List<MetaUser> users, String separator, Notice notice) {
//        if (CollectionUtils.isEmpty(users)) {
//            return ;
//        }
//        StringJoiner accountJoiner = new StringJoiner(separator);
//        StringJoiner nameJoiner = new StringJoiner(separator);
//        for (MetaUser user : users) {
//            accountJoiner.add(user.getAccount());
//            nameJoiner.add(user.getName());
//        }
//        notice.setDispatchUserAccount(accountJoiner.toString());
//        notice.setDispatchUsers(nameJoiner.toString());
//    }
//
//    @Override
//    public void afterFetch(Collection<Map<String, Object>> list) {
//        if (CollectionUtils.isEmpty(list)) {
//            return;
//        }
//        for (Map<String, Object> map : list) {
//            String ways = (String) map.get("dispatchWay");
//            String[] split = ways.split(",");
//
//            StringJoiner wayJoiner = new StringJoiner(",");
//            for (String s : split) {
//                wayJoiner.add(DispatchWayEnum.getWayByCode(s));
//            }
//            map.put("dispatchWay", wayJoiner.toString());
//        }
//    }
//}
