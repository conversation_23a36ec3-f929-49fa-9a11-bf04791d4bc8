package cec.jiutian.bc.urm.inbound.local.service.command.init;

import cec.jiutian.bc.urm.domain.systemBasicConfig.entity.SystemBasicConfig;
import cec.jiutian.bc.urm.domain.systemSecurityConfig.entity.SystemSecurityConfig;
import cec.jiutian.bc.urm.domain.systemSecurityConfig.enums.PasswordComplexityEnum;
import cec.jiutian.bc.urm.domain.systemSecurityConfig.enums.UnlockMethodEnum;
import cec.jiutian.bc.urm.inbound.local.config.SystemConfig;
import cec.jiutian.data.jpa.JpaCrud;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description:  系统配置和安全配置
 */
@Service
@Slf4j
public class SystemConfigInitService {

    @Resource
    private SystemConfig systemConfig;
    @Resource
    private JpaCrud jpaCrud;

    public void initSystemConfig(){
        List<SystemSecurityConfig> select = jpaCrud.select(new SystemSecurityConfig());
        if(CollectionUtils.isEmpty(select)){
            log.info("初始化系统配置中--");
            SystemBasicConfig systemBasicConfig = new SystemBasicConfig();
            systemBasicConfig.setSystemName("FABOS");
            jpaCrud.insert(systemBasicConfig);
            log.info("初始化系统安全配置中--");
            SystemSecurityConfig systemSecurityConfig = new SystemSecurityConfig();
            systemSecurityConfig.setTriadManagementEnabled(false);
//            systemSecurityConfig.setSingleLoginEnabled(false);
            systemSecurityConfig.setTimeoutLogoutMinutes(600);
            systemSecurityConfig.setPasswordValidityDays(180);
            systemSecurityConfig.setAuthenticationFailureCount(5);
            systemSecurityConfig.setAccountAutoUnlockMinutes(24*60);
            systemSecurityConfig.setAccountUnlockMethod(UnlockMethodEnum.Auto.getMethod());
            systemSecurityConfig.setPasswordMinLength(6);
            systemSecurityConfig.setPasswordComplexity(PasswordComplexityEnum.NONE.getCode());
            jpaCrud.insert(systemSecurityConfig);
        }

    }


}
