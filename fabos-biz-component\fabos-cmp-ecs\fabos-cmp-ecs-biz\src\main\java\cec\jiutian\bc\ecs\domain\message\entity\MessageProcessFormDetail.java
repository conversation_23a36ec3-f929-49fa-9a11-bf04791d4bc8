package cec.jiutian.bc.ecs.domain.message.entity;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.type.Power;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Entity
@Table(name = "ecs_message_process_form_detail")
@FabosJson(
        name = "消息处理表单详情",
        orderBy = "MessageProcessFormDetail.createTime desc",
        power = @Power(edit = false, delete = false, add = false, export = false, importable = false, print = false)
)
@Getter
@Setter
public class MessageProcessFormDetail extends MetaModel {

    @Column(name = "message_id")
    private String messageId;

    @Column(name = "form_id")
    private String formId;

    /**
     * 表单URL
     * 根据参数拼接的完整Url
     */
    private String formUrl;

    /**
     * 表单名称
     */
    @FabosJsonField(
            views = @View(title = "表单名称"),
            edit = @Edit(title = "表单名称")
    )
    private String formName;

    /**
     * 是否处理完成
     * true/false
     */
    @FabosJsonField(
            views = @View(title = "是否处理完成"),
            edit = @Edit(title = "是否处理完成"))
    private boolean processCompletedFlag;


    @ManyToOne
    @FabosJsonField(
            views = @View(title = "处理人", column = "name"))
    private MetaUser processUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "处理时间", type = ViewType.text)
    )
    private LocalDateTime processTime;

}
