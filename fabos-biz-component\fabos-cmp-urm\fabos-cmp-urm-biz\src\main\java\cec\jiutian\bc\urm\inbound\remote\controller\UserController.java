package cec.jiutian.bc.urm.inbound.remote.controller;

import cec.jiutian.bc.urm.domain.user.entity.LoginModel;
import cec.jiutian.bc.urm.domain.user.entity.UserinfoVo;
import cec.jiutian.bc.urm.inbound.local.service.command.UrmCommandService;
import cec.jiutian.common.constant.SessionKey;
import cec.jiutian.common.util.JacksonUtil;
import cec.jiutian.core.frame.module.R;
import cec.jiutian.core.service.FabosJsonSessionService;
import cec.jiutian.core.view.fabosJson.view.FabosJsonApiModel;
import com.wf.captcha.SpecCaptcha;
import com.wf.captcha.base.Captcha;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/fabos-cmp-urm")
public class UserController {

    private final UrmCommandService urmCommandService;
    private final FabosJsonSessionService sessionService;

    public UserController(UrmCommandService urmCommandService,
                          FabosJsonSessionService sessionService) {
        this.urmCommandService = urmCommandService;
        this.sessionService = sessionService;
    }

    /**
     * 登录
     *
     * @param account    用户名
     * @param password   密码
     * @param verifyCode 验证码
     */
    @GetMapping(value = "/login")
    public Map<String, Object> login(@RequestParam String account, @RequestParam String password,
                                     @RequestParam(required = false) String verifyCode,@RequestParam(required = false) String phoneNumber) throws IOException {
        LoginModel loginModel = urmCommandService.login(account, password, verifyCode,phoneNumber);
        loginModel.setToLogin(urmCommandService.checkToLogin(account));
        Map<String, Object> mapAO = JacksonUtil.fromJsonToMap(JacksonUtil.toJson(loginModel));
        if (loginModel.getUser() != null) {
            Map<String, Object> user = (Map) mapAO.get("user");
            user.remove("password");
        }
        if (loginModel.getMetaUserinfo() != null) {
            Map<String, Object> metaUserinfo = (Map) mapAO.get("metaUserinfo");
            metaUserinfo.remove("password");
        }
        return mapAO;
    }

    /**
     * 登出
     */
    @GetMapping(value = "/logout")
//    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public FabosJsonApiModel logout(HttpServletRequest request) {
        return urmCommandService.logout(request);
    }

    /**
     * 修改密码
     *
     * @param account         账号
     * @param oldPassword     旧密码
     * @param newPassword     新密码
     * @param confirmPassword 确认新密码
     */
    @GetMapping(value = "/changePassword")
//    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public FabosJsonApiModel changePwd(@RequestParam("account") String account,
                                       @RequestParam("oldPassword") String oldPassword,
                                       @RequestParam("newPassword") String newPassword,
                                       @RequestParam("confirmPassword") String confirmPassword) {
        return urmCommandService.changePassword(account, oldPassword, newPassword, confirmPassword);
    }

    @GetMapping(value = "/resetPassword")
    public R<FabosJsonApiModel> resetPassword(@RequestParam("account") String account) {
        urmCommandService.resetPassword(account);
        return R.ok();
    }


    /**
     * 生成验证码
     *
     * @param height 验证码高度
     */
    @GetMapping("/code-img")
    public void createCode(HttpServletResponse response,
                           @RequestParam(required = false, defaultValue = "38") Integer height) throws Exception {
        response.setContentType("image/jpeg"); // 设置响应的类型格式为图片格式
        response.setDateHeader("Expires", 0);
        response.setHeader("Pragma", "no-cache"); // 禁止图像缓存
        response.setHeader("Cache-Control", "no-cache");
        Captcha captcha = new SpecCaptcha(150, height, 4);
        sessionService.put(SessionKey.VERIFY_CODE, captcha.text(), 60, TimeUnit.SECONDS);
        captcha.out(response.getOutputStream());
    }

    @GetMapping("/userinfo")
//    @EruptRouter(verifyType = EruptRouter.VerifyType.LOGIN)
    public UserinfoVo userinfo() {
        return urmCommandService.getUserInfo();
    }

//    /**
//     * 校验token是否失效
//     *
//     * @param request
//     * @return
//     */
//    @PostMapping("/check")
//    public R<AuthorizationCheckDTO> userinfo(HttpServletRequest request, @RequestParam(name = "account", required = true) String account) {
//        String token = request.getHeader(SessionKey.TOKEN);
//        return R.ok(urmCommandService.checkToken(token, account));
//    }

}
