package cec.jiutian.api.remoteCallLog.port.mq;

import cec.jiutian.api.remoteCallLog.config.RemoteCallLogMQConfig;
import cec.jiutian.api.remoteCallLog.model.RemoteCallLog;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.common.util.JacksonUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import jakarta.annotation.Resource;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.stereotype.Component;

@Component
public class RemoteCallLogSender {
    @Resource
    private AmqpTemplate rabbitTemplate;

    @Resource
    private RemoteCallLogMQConfig remoteCallLogMQConfig;

    public void sendRemoteCallLogMessage(RemoteCallLog remoteCallLog) {
        String message = null;
        try {
            message = JacksonUtil.toJson(remoteCallLog);
        } catch (JsonProcessingException e) {
            throw new ServiceException(e);
        }
        rabbitTemplate.convertAndSend(remoteCallLogMQConfig.getQueueName(), message);
    }
}
