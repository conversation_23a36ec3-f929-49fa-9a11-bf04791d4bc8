package cec.jiutian.bc.generalModeler.domain.measureUnit.model;

import cec.jiutian.bc.generalModeler.domain.measureUnit.handler.ConversionRuleOperationHandler;
import cec.jiutian.bc.generalModeler.domain.measureUnit.proxy.UnitConversionRuleDataProxy;
import cec.jiutian.bc.generalModeler.enumeration.ConversionFormulaTemplateEnum;
import cec.jiutian.bc.urm.domain.dictionary.handler.DictChoiceFetchHandler;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.InputGroup;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date ：2024/10/24 17:16
 * @description：
 */
@Table(name = "unit_conversion_rule")
@FabosJson(
        name = "单位换算规则",
        dataProxy = UnitConversionRuleDataProxy.class,
        rowOperation = {
                @RowOperation(
                        title = "试算",
                        code = "UnitConversionRule@Calculate",
                        operationHandler = ConversionRuleOperationHandler.class,
                        mode = RowOperation.Mode.BUTTON,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        submitMethod = RowOperation.SubmitMethod.HANDLER,
                        fabosJsonClass = UnitConversionRuleData.class
                )
        }
)
@Entity
@Getter
@Setter
public class UnitConversionRule extends MetaModel {

    @FabosJsonField(
            views = @View(title = "主计量单位", column = "unitChnName"),
            edit = @Edit(title = "主计量单位",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "unitChnName",
                            type = ReferenceTableType.SelectShowTypeMTO.LIST, groupField = "unitType"),
                    search = @Search(vague = true)
            )
    )
    @ManyToOne
    @JoinColumn(name = "source_unit_id")
    private MeasureUnit sourceMeasureUnit;

    @FabosJsonField(
            views = @View(title = "辅计量单位", column = "unitChnName"),
            edit = @Edit(title = "辅计量单位",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "unitChnName",
                            type = ReferenceTableType.SelectShowTypeMTO.LIST, groupField = "unitType")
            )
    )
    @ManyToOne
    @JoinColumn(name = "target_unit_id")
    private MeasureUnit targetMeasureUnit;

    @FabosJsonField(
            views = @View(title = "转换精度"),
            edit = @Edit(title = "转换精度",
                    notNull = true,
                    numberType = @NumberType(min = 0, max = 999999),
                    inputGroup = @InputGroup(prefix = "小数点后", postfix = "位"))
    )
    private Integer precision;

    @FabosJsonField(
            views = @View(title = "换算公式"),
            edit = @Edit(title = "换算公式", show = false)
    )
    private String formula;

    @FabosJsonField(
            views = @View(title = "换算公式模版", show = false),
            edit = @Edit(title = "换算公式模版", notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ConversionFormulaTemplateEnum.class))
    )
    private String formulaTemplate;

    @FabosJsonField(
            views = @View(title = "换算公式_计算用", show = false),
            edit = @Edit(title = "换算公式_计算用", show = false)
    )
    private String calculateFormula;

    @FabosJsonField(
            views = @View(title = "转换系数", show = false),
            edit = @Edit(title = "转换系数", notNull = true,
                    inputType = @InputType(regex = "^\\d+(\\.\\d+)?$"))
    )
    private String coefficient;

    @FabosJsonField(
            views = @View(title = "修正值", show = false),
            edit = @Edit(title = "修正值",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "formulaTemplate=='TwoVariableMultiplication' || formulaTemplate=='TwoVariableDivision'"),
                    inputType = @InputType(regex = "^(\\-|\\+)?\\d+(\\.\\d+)?$"))
    )
    private String constantTerm;

    @FabosJsonField(
            views = @View(title = "主计量单位分类", show = false),
            edit = @Edit(title = "主计量单位分类", show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                            fetchHandlerParams = "JLDW04"),
                    search = @Search(vague = true)
            )
    )
    private String mainUnitType;

    @FabosJsonField(
            views = @View(title = "辅计量单位分类", show = false),
            edit = @Edit(title = "辅计量单位分类", show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                            fetchHandlerParams = "JLDW04"),
                    search = @Search(vague = true)
            )
    )
    private String auxiliaryUnitType;

    @FabosJsonField(
            views = @View(title = "辅计量单位名称", show = false),
            edit = @Edit(title = "辅计量单位名称", show = false)
    )
    private String targetMeasureUnitName;
//
//    @FabosJsonField(
//            views = @View(title = "换算规则描述"),
//            edit = @Edit(title = "换算规则描述")
//    )
//    private String ruleDescription;
}
