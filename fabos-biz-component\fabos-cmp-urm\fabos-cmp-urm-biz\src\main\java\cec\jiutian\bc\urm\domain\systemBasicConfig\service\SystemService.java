package cec.jiutian.bc.urm.domain.systemBasicConfig.service;

import cec.jiutian.bc.urm.domain.systemBasicConfig.entity.SystemBasicConfig;
import cec.jiutian.bc.urm.domain.systemSecurityConfig.entity.SystemSecurityConfig;
import cec.jiutian.data.jpa.JpaCrud;
import cn.hutool.core.bean.BeanUtil;
import jakarta.annotation.Resource;
import jakarta.persistence.TypedQuery;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 */
@Service
public class SystemService {
    @Resource
    private JpaCrud jpaCrud;

    @Value("${app.id}")
    private String appId;
    public SystemBasicConfig getSystemConfig() {
//        return jpaCrud.selectOne(new SystemBasicConfig());
        TypedQuery<SystemBasicConfig> query = jpaCrud.acquireEntityManager().createQuery("SELECT s FROM SystemBasicConfig s where (systemApplicationId = :appId or systemApplicationId is null)", SystemBasicConfig.class);
        query.setParameter("appId", appId);

        // 设置分页参数
        query.setFirstResult(0);
        query.setMaxResults(1);
        SystemBasicConfig result = query.getSingleResult();
        return result;
    }

    public SystemSecurityConfig getSystemSecurityConfig() {
        return jpaCrud.selectOne(new SystemSecurityConfig());
    }
    public Map<String, Object> getCombinedSystemConfig() {
        SystemBasicConfig systemConfig = getSystemConfig();
        if (Objects.isNull(systemConfig)) {
            return new HashMap<>();
        }
        Map<String, Object> combined = BeanUtil.beanToMap(systemConfig);
        combined.put("systemSecurityConfig", getSystemSecurityConfig());
        return combined;
    }
}
