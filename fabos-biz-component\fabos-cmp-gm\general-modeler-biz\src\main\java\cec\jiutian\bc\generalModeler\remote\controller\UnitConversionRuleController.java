package cec.jiutian.bc.generalModeler.remote.controller;

import cec.jiutian.bc.generalModeler.service.UnitConversionRuleService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2024/10/25 10:29
 * @description：
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/unitConversion")
public class UnitConversionRuleController {
    @Resource
    private UnitConversionRuleService unitConversionRuleService;

    @Operation(summary = "获取换算规则试算结果")
    @PostMapping("/calculateResult")
    public String calculateResult(@RequestBody Map<String, Object> map) {
        return unitConversionRuleService.calculateResult(map);
    }
}
