package cec.jiutian.bc.ecs.domain.modle;

import cec.jiutian.core.frame.constant.RegexConst;
import cec.jiutian.core.frame.constant.SwitchStatus;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.SubTableField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Table(name = "fd_meta_user", indexes = {
        @Index(columnList = "account,oid"),
        @Index(columnList = "account,password")
})
@Entity
@Getter
@Setter
@FabosJsonI18n
@FabosJson(name = "消息用户视图")
@NoArgsConstructor
public class UserForMsg extends MetaModel {

    @FabosJsonField(
            views = @View(title = "账号", index = -9),
            edit = @Edit(title = "账号", search = @Search(vague = true), notNull = true, readonly = @Readonly(add = false))
    )
    @SubTableField
    private String account;

    @FabosJsonField(
            views = @View(title = "姓名"),
            edit = @Edit(title = "姓名", search = @Search(vague = true), notNull = true)
    )
    @SubTableField
    private String name;

    @FabosJsonField(
            views = @View(title = "性别"),
            edit = @Edit(title = "性别", search = @Search
            )
    )
    private String gender;

    @FabosJsonField(
            views = @View(title = "工号"),
            edit = @Edit(title = "工号", notNull = true, search = @Search(vague = true))
    )
    @SubTableField
    private String employeeNumber;

    /**
     * 该状态用于业务是否能选择该用户，该字段禁用不影响登录
     */
    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", search = @Search(defaultVal = "Y"),
                    defaultVal = "Y",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {SwitchStatus.ChoiceFetch.class}))
    )
    private String state;

    @FabosJsonField(
            views = @View(title = "工种"),
            edit = @Edit(title = "工种", search = @Search(vague = true))
    )
    private String jobType;


    @Column(unique = true)
    @FabosJsonField(
            views = @View(title = "电话号码", show = false),
            edit = @Edit(title = "电话号码", search = @Search(vague = true), readonly = @Readonly(add = false), notNull = true, inputType = @InputType(regex = RegexConst.PHONE_REGEX)
            ))
    private String phoneNumber;
    @FabosJsonField(
            views = @View(title = "电子邮件", show = false),
            edit = @Edit(title = "电子邮件", search = @Search(vague = true), notNull = true, inputType = @InputType(regex = RegexConst.EMAIL_REGEX))
    )
    private String emailAddress;
}
