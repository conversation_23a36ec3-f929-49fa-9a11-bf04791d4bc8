package cec.jiutian.bc.job.domain.job.service;

import cec.jiutian.bc.job.outbound.port.publisher.JobPublish;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.bc.job.domain.job.entity.JobInfo;
import cec.jiutian.bc.job.domain.log.entity.JobLog;
import jakarta.annotation.Resource;
import jakarta.transaction.Transactional;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.quartz.JobDataMap;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.impl.JobDetailImpl;
import org.quartz.impl.StdSchedulerFactory;
import org.quartz.impl.triggers.CronTriggerImpl;
import org.quartz.simpl.SimpleThreadPool;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;


@Service
@Transactional
public class JobService implements DisposableBean {

    /**
     * 执行任务线程.
     */
    private static final String PROP_THREAD_COUNT = "org.quartz.threadPool.threadCount";
    /**
     * 执行任务线程数.
     */
    private static final int DEFAULT_THREAD_COUNT = 1;

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Autowired(required = false)
    private JavaMailSenderImpl javaMailSender;

    @Resource
    @Getter
    StringRedisTemplate stringRedisTemplate;

    public static final String MAIL_SENDER_KEY = "mailSensor";

    private final Map<String, JobInfoStdSchedulerFactory> schedulerFactoryMap = new ConcurrentHashMap<>();

    public void triggerJob(JobInfo job) {
        new JobAction().trigger(job, javaMailSender);
    }

    public synchronized void addJob(JobInfo job) throws SchedulerException, ParseException {
        String code = job.getCode();
        if (!schedulerFactoryMap.containsKey(code)) {
            if (job.getStatus()) {
                StdSchedulerFactory ssf = new StdSchedulerFactory();
                ssf.initialize(getSchedulerProp(code));
                Scheduler scheduler = ssf.getScheduler();
                // job
                JobDetailImpl jobDetail = new JobDetailImpl();
                JobDataMap jobDataMap = new JobDataMap();
                jobDataMap.put(code, job);
                jobDataMap.put(MAIL_SENDER_KEY, javaMailSender);
                jobDetail.setJobDataMap(jobDataMap);
                jobDetail.setName(code);
                jobDetail.setJobClass(JobAction.class);
                // trigger
                CronTriggerImpl trigger = new CronTriggerImpl();
                trigger.setName(code);
                trigger.setCronExpression(job.getCron());
                scheduler.scheduleJob(jobDetail, trigger);
                scheduler.start();
                schedulerFactoryMap.put(code, new JobInfoStdSchedulerFactory(job, ssf));
            }
        }
    }

    public synchronized void modifyJob(JobInfo job) throws SchedulerException, ParseException {
        if (schedulerFactoryMap.containsKey(job.getCode())) {
            if (job.equals(schedulerFactoryMap.get(job.getCode()).getJob())) {
                return;
            } else {
                this.deleteJob(job);
            }
        }
        this.addJob(job);
    }

    public synchronized void deleteJob(JobInfo job) throws SchedulerException {
        JobInfoStdSchedulerFactory sf = schedulerFactoryMap.get(job.getCode());
        if (null != sf) {
            Scheduler scheduler = sf.getStdSchedulerFactory().getScheduler();
            scheduler.deleteJob(new JobKey(job.getCode()));
            if (!scheduler.isShutdown()) scheduler.shutdown();
            schedulerFactoryMap.remove(job.getCode());
        }
    }

    private Properties getSchedulerProp(String schedulerName) {
        Properties props = new Properties();
        props.setProperty(StdSchedulerFactory.PROP_SCHED_MAKE_SCHEDULER_THREAD_DAEMON, "true");
        props.setProperty(StdSchedulerFactory.PROP_SCHED_INTERRUPT_JOBS_ON_SHUTDOWN_WITH_WAIT, "true");
        props.setProperty(StdSchedulerFactory.PROP_SCHED_INTERRUPT_JOBS_ON_SHUTDOWN, "true");
        props.setProperty(StdSchedulerFactory.PROP_SCHED_INSTANCE_ID, "AUTO");
        props.setProperty(StdSchedulerFactory.PROP_SCHED_INSTANCE_NAME, schedulerName);
        props.setProperty(StdSchedulerFactory.PROP_THREAD_POOL_CLASS, SimpleThreadPool.class.getName());
        props.setProperty(PROP_THREAD_COUNT, Integer.toString(DEFAULT_THREAD_COUNT));
        return props;
    }

    public void saveJobLog(JobLog jobLog) {
        fabosJsonDao.persistAndFlush(jobLog);
    }

    @Override
    public void destroy() throws SchedulerException {
        for (JobInfoStdSchedulerFactory value : schedulerFactoryMap.values()) {
            value.getStdSchedulerFactory().getScheduler().shutdown();
        }
    }


    @Getter
    @Setter
    @NoArgsConstructor
    private static class JobInfoStdSchedulerFactory {

        private JobInfo job;

        private StdSchedulerFactory stdSchedulerFactory;

        public JobInfoStdSchedulerFactory(JobInfo job, StdSchedulerFactory stdSchedulerFactory) {
            this.job = job;
            this.stdSchedulerFactory = stdSchedulerFactory;
        }
    }

}
