package cec.jiutian.bc.infra.util;

import java.text.DecimalFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2023/6/7 10:18
 * @description：
 */
public class GenericUtils {

    /**
     * @Description 获取百分比
     * <AUTHOR>
     * @Date 2023/6/7 11:29 
     */
    public static String getPercent(int x, int y){
        String result = "0.00%";
        if (y != 0){
            double a = x * 1.0;
            double b = y * 1.0;
            double fen = a / b;

            DecimalFormat df = new DecimalFormat("##0.00%");

            result = df.format(fen);
        }
        return result;
    }

    /**
     * @Description 获取两个时间的分钟差和秒差（数值类型）
     * <AUTHOR>
     * @Date 2023/6/7 11:29 
     */
    public static Map<String,Integer> getMinAndSecondByNum(LocalDateTime startTime, LocalDateTime endTime){
        Map<String,Integer> map = new HashMap<>();
        if (startTime == null || endTime == null){
            map.put("min",0);
            map.put("sec",0);
            return map;
        }
        long nd = 1000 * 24 * 60 * 60;// 一天的毫秒数
        long nh = 1000 * 60 * 60;// 一小时的毫秒数
        long nm = 1000 * 60;// 一分钟的毫秒数
        long ns = 1000;// 一秒钟的毫秒数
        long diff;
        long day = 0;
        long hour = 0;
        long min = 0;
        long second = 0;
        String result = null;
        // 获得两个时间的毫秒时间差异
        diff = getMs(endTime) - getMs(startTime);
        // 计算差多少天
        day = diff / nd;
        // 计算差多少小时
        hour = diff / nh;
        // 计算差多少分钟
        min = diff / nm;
        // 计算差多少秒
        second = diff / ns;
        // 计算出xx分xx秒
        int timeInSecond = (int)second;
        int hours = timeInSecond/3600;
        timeInSecond = timeInSecond-(hours*3600);
        int minutes = timeInSecond/60;
        int minutesResult = (hours*3600)/60+minutes;
        timeInSecond = timeInSecond-(minutes*60);
        int seconds = timeInSecond;
        map.put("min",minutesResult);
        map.put("sec",seconds);

        return map;
    }

    private static long getMs(LocalDateTime localDateTime) {
        // 将 LocalDateTime 转换为 ZonedDateTime
        ZoneId zoneId = ZoneId.systemDefault(); // 使用系统默认时区
        ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);
        // 从 ZonedDateTime 获取 Instant
        Instant instant = zonedDateTime.toInstant();
        // 从 Instant 获取自纪元以来的毫秒数
        return instant.toEpochMilli();
    }
}
