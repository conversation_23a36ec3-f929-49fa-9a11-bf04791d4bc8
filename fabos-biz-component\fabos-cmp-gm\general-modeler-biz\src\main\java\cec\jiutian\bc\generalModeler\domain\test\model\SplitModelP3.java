package cec.jiutian.bc.generalModeler.domain.test.model;

import cec.jiutian.core.frame.constant.SwitchStatus;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;


@FabosJson(
        name = "组合模型p3"
)
@Table(name = "split_model_p3"
)
@Entity
@Getter
@Setter
public class SplitModelP3 extends MetaModel {
    @FabosJsonField(
            views = @View(title = "用户姓名"),
            edit = @Edit(title = "用户姓名", search = @Search())
    )
    private String userName;
    @FabosJsonField(
            views = @View(title = "角色名称"),
            edit = @Edit(title = "角色名称", search = @Search())
    )
    private String roleName;

    @FabosJsonField(
            views = @View(title = "用户状态"),
            edit = @Edit(title = "状态", search = @Search(defaultVal = "Y"),
                    defaultVal = "Y",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {SwitchStatus.ChoiceFetch.class}))
    )
    private String state;

    @FabosJsonField(
            edit = @Edit(title = "数量", notNull = false,
                    type = EditType.NUMBER)
    )
    private Integer qty;

    @FabosJsonField(
            views = @View(title = "用户DTO唯一标识", show = false),
            edit = @Edit(title = "用户DTO唯一标识", show = false)
    )
    private String extraId;

}
