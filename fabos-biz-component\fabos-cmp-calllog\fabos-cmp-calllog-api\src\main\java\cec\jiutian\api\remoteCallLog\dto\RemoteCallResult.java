package cec.jiutian.api.remoteCallLog.dto;

import cec.jiutian.api.remoteCallLog.enums.RemoteCallCodeEnum;
import com.alibaba.fastjson2.JSON;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.stereotype.Component;

import java.io.Serializable;

@Setter
@Getter
@Component
@NoArgsConstructor
public class RemoteCallResult<T> implements Serializable {

    /**
     * 响应状态码，来源与枚举，状态码为200时，表示调用成功, repsonseBody为调用结果，否则responseBody为调用失败信息
     */
    private String status;

    /**
     * 响应状态码描述
     */
    private String message;
    /**
     * 响应数据
     */
    private T data;

    /**
     * 如果需要将异常信息提供到调用方，可以传入接收到的异常
     *
     * @param e 异常
     */
    public RemoteCallResult(Exception e) {
        this.status = RemoteCallCodeEnum.INTERNAL_SERVER_ERROR.getCode();
        this.message = e.getMessage();
        this.data = null;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    public static RemoteCallResult<?> success() {
        RemoteCallResult<?> result = new RemoteCallResult<>();
        result.setStatus(RemoteCallCodeEnum.SUCCESS.getCode());
        result.setMessage(RemoteCallCodeEnum.SUCCESS.getDescription());
        return result;
    }

    public static <T> RemoteCallResult<T> success(T data) {
        RemoteCallResult<T> result = new RemoteCallResult<>();
        result.setStatus(RemoteCallCodeEnum.SUCCESS.getCode());
        result.setMessage(RemoteCallCodeEnum.SUCCESS.getDescription());
        result.setData(data);
        return result;
    }

    public static <T> RemoteCallResult<T> error(String code, String message) {
        RemoteCallResult<T> result = new RemoteCallResult<>();
        result.setStatus(code);
        result.setMessage(message);
        return result;
    }
}
