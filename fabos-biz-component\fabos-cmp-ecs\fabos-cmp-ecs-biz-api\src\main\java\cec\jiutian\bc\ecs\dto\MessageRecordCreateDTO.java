package cec.jiutian.bc.ecs.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2023/5/24 14:11
 * @description：
 */
@ApiModel("MessageRecordCreateDTO")
@Data
public class MessageRecordCreateDTO {

    private String title;

    private String gid;

    /**
     * 消息id
     */
    @ApiModelProperty("消息id")
    private String messageId;

    /**
     * 推送方式
     */
    @ApiModelProperty("推送方式")
    private String dispatchWay;

    /**
     * 推送人员
     */
    @ApiModelProperty("接收人员")
    private String dispatchUser;

    /**
     * 消息内容
     */
    @ApiModelProperty("消息内容")
    private String content;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTs;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createUser;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String lastTrxnUser;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String lastEventComment;

    private String oid;

    private List<PushUserDTO> userDTOList;
}
