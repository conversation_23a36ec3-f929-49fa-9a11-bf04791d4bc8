package cec.jiutian.bc.urm.domain.role.event;

import cec.jiutian.bc.urm.domain.role.entity.Role;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.fc.log.domain.permissionOperationLog.manager.PermissionLogManager;
import cec.jiutian.fc.log.enums.OperationTargetEnum;
import cec.jiutian.fc.log.enums.OperationTypeEnum;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import jakarta.persistence.LockModeType;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

@Service
public class RoleDataProxy implements DataProxy<Role> {
    @Resource
    private PermissionLogManager permissionLogManager;
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Override
    public void beforeAdd(Role role) {
        Integer sort = role.getSort();
        if (sort == null || sort<1 || sort > 9999) {
            role.setSort(9999);
        }
        role.setMenus(duplicateFilter(role.getMenus()));
        role.setCreateTime(LocalDateTime.now());
        role.setCreateBy(UserContext.getAccount());
        role.setUpdateTime(role.getCreateTime());
        role.setUpdateBy(UserContext.getAccount());

        // 设置role上的autoIncrementId值为递增+1，查询时需上锁
        role.setAutoIncrementId(
                fabosJsonDao.getEntityManager()
                        .createQuery("SELECT COALESCE(MAX(r.autoIncrementId),0) FROM Role r", Integer.class)
                        .setLockMode(LockModeType.PESSIMISTIC_WRITE)
                        .getSingleResult() + 1
        );
    }

    @Override
    public void beforeUpdate(Role role) {
        Integer sort = role.getSort();
        if (sort != null && sort<1) {
            role.setSort(null);
        }
        role.setMenus(duplicateFilter(role.getMenus()));
        role.setUpdateTime(LocalDateTime.now());
        role.setUpdateBy(UserContext.getAccount());
        //日志
        EntityManager entityManager = fabosJsonDao.getEntityManager();
        entityManager.clear();
        Role roleDb = entityManager.find(Role.class, role.getId());
        permissionLogManager.update(roleDb,role, OperationTargetEnum.Enum.ROLE, OperationTypeEnum.Enum.UPDATE);
    }

    private static <T extends MetaModel> List<T> duplicateFilter(List<T> list) {
        List<T> result = new ArrayList<>();
        if (list == null) {
            return result;
        }
        HashSet<String> set = new HashSet<>();
        for (T t : list) {
            if (t.getId() == null || set.contains(t.getId())) {
                continue;
            }
            set.add(t.getId());
            result.add(t);
        }
        return result;
    }
    @Override
    public void afterAdd(Role role) {
        permissionLogManager.addOrDelete(role, OperationTargetEnum.Enum.ROLE, OperationTypeEnum.Enum.ADD);
    }

    @Override
    public void afterDelete(Role role) {
        permissionLogManager.addOrDelete(role, OperationTargetEnum.Enum.ROLE, OperationTypeEnum.Enum.DELETE);
    }

}
