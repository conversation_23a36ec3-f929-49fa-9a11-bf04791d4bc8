package cec.jiutian.bc.generalModeler.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class AlarmStockTypeEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (AlarmStockTypeEnum.Enum data : AlarmStockTypeEnum.Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        Advent("临期"),
        OverDull("超期"),
        Dull("呆滞"),

        UpperLimit("超过上限"),
        LowerLimit("超过下限"),
        UpperLowerLimit("超过上下限");

        private final String value;

    }
}
