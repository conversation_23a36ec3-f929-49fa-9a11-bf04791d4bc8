package cec.jiutian.bc.generalModeler.domain.material.proxy;

import cec.jiutian.bc.generalModeler.domain.material.model.MaterialCategory;
import cec.jiutian.bc.generalModeler.domain.material.service.MaterialCategoryRepository;
import cec.jiutian.common.util.SpringContextUtils;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class MaterialCategoryDataProxy implements DataProxy<MaterialCategory> {
    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private JpaCrud jpaCrud;


    @Override
    public void beforeAdd(MaterialCategory materialCategory) {
        //设置级别和物料属性模板
        if (materialCategory.getParentCategory() == null) {
            materialCategory.setCategoryLevel(1);
        } else {
            MaterialCategory parentMaterialCategory = fabosJsonDao.getEntityManager().find(MaterialCategory.class, materialCategory.getParentCategory().getId());
            materialCategory.setCategoryLevel(parentMaterialCategory.getCategoryLevel() + 1);
            materialCategory.setMaterialAttributeTemplate(parentMaterialCategory.getMaterialAttributeTemplate());

        }
    }

    @Override
    public void beforeUpdate(MaterialCategory materialCategory) {
        //调整级别和物料属性模板
        if (materialCategory.getParentCategory() == null) {
            materialCategory.setCategoryLevel(1);
        } else {
            MaterialCategory parentMaterialCategory = fabosJsonDao.getEntityManager().find(MaterialCategory.class, materialCategory.getParentCategory().getId());
            materialCategory.setCategoryLevel(parentMaterialCategory.getCategoryLevel() + 1);
            materialCategory.setMaterialAttributeTemplate(parentMaterialCategory.getMaterialAttributeTemplate());
        }
        // 变更所有下级的物料属性模板
        MaterialCategory byId = fabosJsonDao.findById(MaterialCategory.class, materialCategory.getId());
        if (byId.getMaterialAttributeTemplate() != materialCategory.getMaterialAttributeTemplate()) {
            MaterialCategoryRepository materialCategoryRepository = SpringContextUtils.getBean(MaterialCategoryRepository.class);
            materialCategoryRepository.updateWithAllChid(materialCategory.getId(), materialCategory.getMaterialAttributeTemplate().getId());
        }
    }
}
