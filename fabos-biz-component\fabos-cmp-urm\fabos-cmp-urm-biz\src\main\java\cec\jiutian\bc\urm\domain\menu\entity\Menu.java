package cec.jiutian.bc.urm.domain.menu.entity;

import cec.jiutian.bc.urm.domain.menu.event.MenuDataProxy;
import cec.jiutian.bc.urm.domain.menu.handler.MenuConditionHandler;
import cec.jiutian.bc.urm.dto.MetaMenu;
import cec.jiutian.core.frame.constant.ButtonJumpTypeEnum;
import cec.jiutian.core.frame.constant.ButtonStyleEnum;
import cec.jiutian.core.frame.constant.MenuButtonTypeEnum;
import cec.jiutian.core.frame.constant.MenuPageTypeEnum;
import cec.jiutian.core.frame.constant.MenuRouterTypeEnum;
import cec.jiutian.core.frame.constant.MenuShowTypeEnum;
import cec.jiutian.core.frame.constant.MenuStatus;
import cec.jiutian.core.frame.constant.MenuSubTypeEnum;
import cec.jiutian.core.frame.constant.MenuTypeEnum;
import cec.jiutian.core.frame.constant.RegexConst;
import cec.jiutian.core.frame.constant.TerminalTypeEnum;
import cec.jiutian.core.frame.context.MetaContext;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.handler.SqlChoiceFetchHandler;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceTreeType;
import cec.jiutian.view.SubTableField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.constant.AnnotationConst;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Tree;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.RandomStringUtils;

import java.time.LocalDateTime;

@FabosJson(name = "菜单管理",
        orderBy = "Menu.sequenceNumber asc, Menu.createTime asc",
        filter = @Filter(conditionHandler = MenuConditionHandler.class),
        tree = @Tree(pid = "parent.id", expandLevel = 1),
        dataProxy = {MenuDataProxy.class})
@Table(name = "fd_menu", uniqueConstraints = @UniqueConstraint(columnNames = {"moduleValue"}))
@Entity
@Getter //@Data重写hashcode 方法导致了栈溢出，所以使用setter，getter代替
@Setter
@TemplateType(type = "treeForm")
public class Menu extends MetaModel {

    public static final String VALUE = "moduleValue";

    @ManyToOne(cascade = CascadeType.MERGE)
    @FabosJsonField(
            sort = 1,
            edit = @Edit(
                    title = "上级树节点",
                    type = EditType.REFERENCE_TREE,
                    referenceTreeType = @ReferenceTreeType(pid = "parent.id")
            )
    )
    private Menu parent;

    @Column(length = AnnotationConst.CODE_LENGTH)
    @FabosJsonField(
            edit = @Edit(title = "编码", readonly = @Readonly)
    )
    private String moduleCode;
    @FabosJsonField(
            sort = 2,
            edit = @Edit(
                    title = "菜单类型",
                    search = @Search(vague = true),
                    type = EditType.CHOICE,
                    defaultVal = "model",
                    choiceType = @ChoiceType(
                            fetchHandler = {MenuTypeEnum.ChoiceFetch.class}
                    )
            )
    )
    private String moduleTypeCode;

    @FabosJsonField(
            sort = 3,
            edit = @Edit(
                    title = "菜单子类型",
                    search = @Search(vague = true),
                    type = EditType.CHOICE,
                    defaultVal = "usual",
                    choiceType = @ChoiceType(
                            fetchHandler = {MenuSubTypeEnum.ChoiceFetch.class}
                    ),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "moduleTypeCode == 'model'")
            )
    )
    private String subType;

    @FabosJsonField(
            sort = 4,
            views = @View(title = "名称"),
            edit = @Edit(title = "名称",
                    notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(regex = RegexConst.FULL_CHAR_REGEX))
    )
    @SubTableField
    private String name;

    @FabosJsonField(
            sort = 5,
            views = @View(title = "图标"),
            edit = @Edit(title = "图标",
                    type = EditType.select_icon))
    private String moduleIconText;

    @FabosJsonField(
            sort = 6,
            views = @View(title = "排序"),
            edit = @Edit(title = "排序",
                    notNull = true,
                    numberType = @NumberType(min = 0, max = 9999))
    )
    private Integer sequenceNumber;

    @FabosJsonField(
            sort = 7,
            views = @View(title = "路由名称"),
            edit = @Edit(title = "路由名称", tips = "模型编码/路由编码/按钮编码",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "moduleTypeCod != 'router'"))
    )
    private String moduleValue;

    @FabosJsonField(
            sort = 8,
            edit = @Edit(
                    title = "状态",
                    type = EditType.CHOICE,
                    notNull = true,
                    defaultVal = "1",
                    choiceType = @ChoiceType(
                            fetchHandler = {MenuStatus.ChoiceFetch.class}
                    ),
                    //是否可分配权限
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "moduleTypeCode!='button'")
            )
    )
    private Integer status;

    @FabosJsonField(
            sort = 9,
            edit = @Edit(
                    title = "是否隐藏",
                    notNull = true,
                    defaultVal = "false",
                    //是否在页面菜单可见
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "moduleTypeCode!='button'")
            )
    )
    @Column(columnDefinition="default false")
    private Boolean hideFlag;

    @ManyToOne
    @FabosJsonField(
            sort = 10,
            views = @View(title = "跳转菜单", column = "name"),
            edit = @Edit(title = "跳转菜单", type = EditType.REFERENCE_TREE,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "jumpType=='model'"))
    )
    private JumpMenuView routerModule;

    @FabosJsonField(
            sort = 11,
            views = @View(title = "页面"),
            edit = @Edit(
                    title = "页面",
                    search = @Search(vague = true),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            fetchHandler = SqlChoiceFetchHandler.class,
                            //查询已发布的页面供选择
                            fetchHandlerParams = {"select publish_page_id, page_code from page_design where is_publish = 'Y'", "5000"}
                    )
                    , dependFieldDisplay = @DependFieldDisplay(showOrHide = "moduleTypeCode=='lowCode' || (buttonType=='default' && pageType == 'lowCode')")
            )
    )
    private String pageId;

    @FabosJsonField(
            sort = 12,
            views = @View(title = "按钮类型"),
            edit = @Edit(title = "按钮类型",
                    type = EditType.CHOICE,
                    defaultVal = "default",
                    choiceType = @ChoiceType(
                            fetchHandler = {MenuButtonTypeEnum.ChoiceFetch.class}
                    ),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "moduleTypeCode=='button'")
            )
    )
    private String buttonType;

    @FabosJsonField(
            sort = 13,
            views = @View(title = "页面类型"),
            edit = @Edit(title = "页面类型",
                    type = EditType.CHOICE,
                    defaultVal = "model",
                    choiceType = @ChoiceType(
                            fetchHandler = {MenuPageTypeEnum.ChoiceFetch.class}
                    ),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "buttonType=='default'")
            )
    )
    private String pageType;

    @FabosJsonField(
            sort = 14,
            views = @View(title = "页面地址"),
            edit = @Edit(title = "页面地址",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "moduleTypeCode=='router'|| buttonType =='jump' || jumpType == 'link' || (buttonType == 'default' && pageType =='static') "))
    )
    private String href;

    //    跳转方式
    @FabosJsonField(
            sort = 15,
            views = @View(title = "跳转方式"),
            edit = @Edit(title = "跳转方式",
                    type = EditType.CHOICE,
                    defaultVal = "link",
                    choiceType = @ChoiceType(
                            fetchHandler = {MenuRouterTypeEnum.ChoiceFetch.class}
                    ),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "moduleTypeCode=='router' || (moduleTypeCode == 'button' && buttonType =='jump')"))
    )
    private String routerType;

    //    展示方式
    @FabosJsonField(
            sort = 16,
            views = @View(title = "展示方式"),
            edit = @Edit(title = "展示方式",
                    type = EditType.CHOICE,
                    defaultVal = "popup",
                    choiceType = @ChoiceType(
                            fetchHandler = {MenuShowTypeEnum.ChoiceFetch.class}
                    ),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "moduleTypeCode == 'button' && buttonType =='default'"))
    )
    private String showType;

    //    跳转类型
    @FabosJsonField(
            sort = 17,
            views = @View(title = "跳转类型"),
            edit = @Edit(title = "跳转类型",
                    type = EditType.CHOICE,
                    defaultVal = "model",
                    choiceType = @ChoiceType(
                            fetchHandler = {ButtonJumpTypeEnum.ChoiceFetch.class}
                    ),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "moduleTypeCode=='button' && buttonType =='jump'"))
    )
    private String jumpType;

    @FabosJsonField(
            sort = 18,
            edit = @Edit(
                    title = "终端类型",
                    type = EditType.CHOICE,
                    defaultVal = "WEB",
                    choiceType = @ChoiceType(
                            fetchHandler = {TerminalTypeEnum.ChoiceFetch.class}
                    )
            )
    )
    private String terminalType;

    @FabosJsonField(
            sort = 19,
            edit = @Edit(
                    title = "按钮样式",
                    type = EditType.CHOICE,
                    defaultVal = "primary",
                    choiceType = @ChoiceType(
                            fetchHandler = {ButtonStyleEnum.ChoiceFetch.class}
                    ),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "moduleTypeCode=='button'")
            )
    )
    private String buttonStyle;

    @FabosJsonField(
            sort = 20,
            edit = @Edit(
                    title = "是否仅显示图标",
                    notNull = true,
                    defaultVal = "false",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "moduleTypeCode=='button'")
            )
    )
    private Boolean onlyShowIcon;

    @FabosJsonField(
            sort = 21,
            views = @View(title = "备注"),
            edit = @Edit(title = "备注", type = EditType.TEXTAREA)
    )
    private String moduleDescription;

    private String applicationName;


    public Menu() {

    }

    public String generateCode(int length) {
        return RandomStringUtils.randomAlphanumeric(length);
    }

    public Menu(String buttonType, String showType, String code, String name, String type, String value, Integer status, Integer sort, String icon, Menu parent) {
        this.buttonType = buttonType;
        this.showType = showType;
        this.moduleCode = code;
        this.name = name;
        this.status = status;
        this.moduleTypeCode = type;
        this.moduleValue = value;
        this.sequenceNumber = sort;
        this.moduleIconText = icon;
        this.parent = parent;
        this.setCreateTime(LocalDateTime.now());
    }

    public Menu(String buttonType, String showType, String code, String name, String type, String value, Integer status, Integer sort, String icon, Menu parent, String applicationName) {
        this.buttonType = buttonType;
        this.showType = showType;
        this.moduleCode = code;
        this.name = name;
        this.status = status;
        this.moduleTypeCode = type;
        this.moduleValue = value;
        this.sequenceNumber = sort;
        this.moduleIconText = icon;
        this.parent = parent;
        this.setCreateTime(LocalDateTime.now());
        this.applicationName = applicationName;
    }

    public void create(Menu parent, String name, String code, String moduleValue, String moduleTypeCode, Integer sequenceNumber, Integer status, String icon, String applicationName) {
        this.parent = parent;
        this.name = name;
        this.moduleValue = moduleValue;
        this.moduleCode = code;
        this.moduleTypeCode = moduleTypeCode;
        this.status = status;
        this.sequenceNumber = sequenceNumber;
        this.moduleIconText = icon;
        this.setCreateTime(LocalDateTime.now());
        this.setCreateBy(MetaContext.getUser().getName());
        this.setUpdateTime(LocalDateTime.now());
        this.setUpdateBy(MetaContext.getUser().getName());
        this.setHideFlag(false);
        this.setApplicationName(applicationName);
    }

    public void update(Menu parent, String name, String code, String moduleValue, String moduleTypeCode, Integer sequenceNumber, Integer status, String icon, String applicationName) {
        this.parent = parent;
        this.name = name;
        this.moduleValue = moduleValue;
        this.moduleCode = code;
        this.moduleTypeCode = moduleTypeCode;
        this.status = status;
        this.sequenceNumber = sequenceNumber;
        this.moduleIconText = icon;
        this.setUpdateTime(LocalDateTime.now());
        this.setUpdateBy(MetaContext.getUser().getName());
        this.setApplicationName(applicationName);
    }

    public static Menu fromMetaMenu(MetaMenu metaMenu) {
        if (null == metaMenu) return null;
        Menu menu = new Menu(metaMenu.getButtonType(), metaMenu.getShowType(), metaMenu.getCode(),
                metaMenu.getName(), null == metaMenu.getType() ? null : metaMenu.getType(),
                metaMenu.getValue(), null == metaMenu.getStatus() ? null : metaMenu.getStatus(),
                metaMenu.getSort(), metaMenu.getIcon(), fromMetaMenu(metaMenu.getParentMenu()), metaMenu.getApplicationName());
        menu.setId(metaMenu.getId());
        if (menu.getHideFlag() == null) {
            menu.setHideFlag(false);
        }
        if (menu.getStatus() == 2) {
            //菜单逻辑调整  status枚举2为隐藏  已经被取消  新增了字段HideFlag
            menu.setHideFlag(true);
            menu.setStatus(1);
        }
        //菜单类型调整 模型的新增了子类型
        if (MenuSubTypeEnum.checkExist(menu.getModuleTypeCode())) {
            // 菜单类型调整
            menu.setSubType(menu.getModuleTypeCode());
            menu.setModuleTypeCode(MenuTypeEnum.MODEL.getCode());
        }
        return menu;
    }
}
