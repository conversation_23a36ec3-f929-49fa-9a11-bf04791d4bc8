package cec.jiutian.bc.generalModeler.domain.warehouseArea.model;


import cec.jiutian.bc.generalModeler.domain.warehouseArea.handler.WarehouseHoldHandler;
import cec.jiutian.bc.generalModeler.domain.warehouseArea.handler.WarehouseReferenceAddHandler;
import cec.jiutian.bc.generalModeler.domain.warehouseArea.handler.WarehouseUnHoldHandler;
import cec.jiutian.bc.generalModeler.domain.warehouseArea.proxy.WarehouseProxy;
import cec.jiutian.bc.generalModeler.enumeration.WarehousePurposeEnum;
import cec.jiutian.bc.generalModeler.enumeration.WarehouseStateEnum;
import cec.jiutian.bc.generalModeler.enumeration.WarehouseTypeEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceAddType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "仓库",
        dataProxy = WarehouseProxy.class,
        orderBy = "Warehouse.createTime desc",
        rowOperation = {
                @RowOperation(
                        code = "Warehouse@Hold",
                        operationHandler = WarehouseHoldHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        title = "锁定",
                        ifExpr = "lockState == 'Locked'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE),
                @RowOperation(
                        code = "Warehouse@UnHold",
                        operationHandler = WarehouseUnHoldHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        title = "解锁",
                        ifExpr = "lockState == 'Normal'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE),
        }
)
@Table(name = "mos_warehouse",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"code", "oid"}),
                @UniqueConstraint(columnNames = {"name", "oid"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class Warehouse extends MetaModel {
    @FabosJsonField(
            views = @View(title = "仓库编码"),
            edit = @Edit(title = "仓库编码", notNull = true, search = @Search(vague = true),
                    readonly = @Readonly(add = false, edit = true), inputType = @InputType(length = 40))
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "仓库名称"),
            edit = @Edit(title = "仓库名称", notNull = true, search = @Search(vague = true),
                    readonly = @Readonly(add = false, edit = true), inputType = @InputType(length = 40))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "仓库描述"),
            edit = @Edit(title = "仓库描述", type = EditType.input_text)
    )
    private String description;

    @FabosJsonField(
            views = @View(title = "仓库类型"),
            edit = @Edit(title = "仓库类型", notNull = true, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = WarehouseTypeEnum.class))
    )
    private String type;

    @FabosJsonField(
            views = @View(title = "类别"),
            edit = @Edit(title = "类别", notNull = true, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = WarehousePurposeEnum.class))
    )
    private String purpose;

    @ManyToOne(fetch = FetchType.EAGER)
    @FabosJsonField(
            views = @View(title = "保管员", column = "name"),
            edit = @Edit(title = "保管员",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name", type = ReferenceTableType.SelectShowTypeMTO.LIST),
                    allowAddMultipleRows = false
            )
    )
    @JoinColumn(foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    private MetaUser metaUser;
    @FabosJsonField(
            views = @View(title = "盘点锁"),
            edit = @Edit(title = "盘点锁", show = false, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = WarehouseStateEnum.class))
    )
    private String lockState;

    @FabosJsonField(
            views = @View(title = "地理位置", show = false),
            edit = @Edit(title = "地理位置", type = EditType.input_text)
    )
    private String position;

//    @ManyToMany  //多对多
//    @JoinTable(
//            name = "e_warehouse_materialcategory", //中间表表名，如下为中间表的定义，详见hibernate ManyToMany
//            joinColumns = @JoinColumn(name = "warehouse_id", referencedColumnName = "id"),
//            inverseJoinColumns = @JoinColumn(name = "material_category_id", referencedColumnName = "id"))
//    @FabosJsonField(
//            views = @View(title = "允许存放物料品类"),
//            edit = @Edit(
//                    title = "允许存放物料品类",
//                    type = EditType.TAB_TREE
//            )
//    )
//    private Set<MaterialCategory> storageCategory;

    @FabosJsonField(
            views = @View(title = "管理要求"),
            edit = @Edit(title = "管理要求", type = EditType.input_text)
    )
    private String manageRequirement;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注", type = EditType.TEXTAREA)
    )
    private String remark;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "warehouse_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "允许存放物资分类", type = EditType.TAB_REFER_ADD),
            referenceAddType = @ReferenceAddType(referenceClass = "StockCategoryView",
                    referenceAddHandler = WarehouseReferenceAddHandler.class),
            views = @View(title = "允许存放物资分类", type = ViewType.TABLE_VIEW, extraPK = "materialCategoryId")
    )
    private List<WarehouseDetail> warehouseDetails;


}
