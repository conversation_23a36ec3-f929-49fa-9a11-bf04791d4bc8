package cec.jiutian.bc.ecs.domain.messagegroup.entity;

import cec.jiutian.bc.ecs.domain.message.UpgradeTimesEnum;
import cec.jiutian.bc.ecs.domain.messagegroup.event.MessageGroupDataProxy;
import cec.jiutian.bc.ecs.enumeration.MessageGroupLevelEnum;
import cec.jiutian.bc.ecs.enums.MessageWayEnum;
import cec.jiutian.common.annonation.UniqueColumn;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.constant.YesOrNoStatus;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.BoolType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.field.edit.TabTableReferType;
import cec.jiutian.view.type.Filter;
import com.alibaba.fastjson2.JSON;
import jakarta.persistence.ConstraintMode;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 */
@Entity
@Table(name = "ecs_message_group", uniqueConstraints = @UniqueConstraint(columnNames = {"group_name", "oid"})
)
@Getter
@Setter
@FabosJson(name = "告警类型", dataProxy = {MessageGroupDataProxy.class},
        orderBy = "MessageGroup.createTime desc", formColumnElements = 6)
@FabosJsonI18n
public class MessageGroup extends MetaModel {

    // ---告警类型基础信息
    @FabosJsonField(
            edit = @Edit(title = "告警类型基础信息", type = EditType.DIVIDE, index = 100
            )
    )
    @Transient
    private String baseDivide;

    /**
     * 消息组名称
     */
    @FabosJsonField(
            views = @View(title = "类型名称"),
            edit = @Edit(title = "类型名称", notNull = true, search = @Search(vague = true), index = 101)
    )
    private String groupName;

    /**
     * 消息组编码
     */
    @UniqueColumn
    @FabosJsonField(
            views = @View(title = "类型编码"),
            edit = @Edit(title = "类型编码", notNull = true, search = @Search(vague = true), index = 102)
    )
    private String groupCode;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "告警分类", column = "name"),
            edit = @Edit(title = "告警分类", notNull = true, search = @Search(vague = true), index = 103,
                    type = EditType.REFERENCE_TABLE, referenceTableType = @ReferenceTableType(type = ReferenceTableType.SelectShowTypeMTO.LIST)
            )
    )
    private MessageCategory messageCategory;

    /**
     * 是否为即时告警
     * 即时告警会通过websocket发送前端，前端弹窗或弹出提示，使用户能够及时看到告警信息
     */
    @FabosJsonField(
            views = @View(title = "是否为即时告警"),
            edit = @Edit(title = "是否为即时告警", search = @Search(vague = true), index = 106)
    )
    private Boolean immediateAlarmFlag;

    @FabosJsonField(
            views = @View(title = "类型标题"),
            edit = @Edit(title = "类型标题", notNull = true, search = @Search(vague = true), index = 104)
    )
    private String groupTitle;

    @FabosJsonField(
            views = @View(title = "告警级别"),
            edit = @Edit(title = "告警级别", search = @Search(vague = true), index = 105,
                    choiceType = @ChoiceType(fetchHandler = MessageGroupLevelEnum.ChoiceFetch.class),
                    type = EditType.CHOICE, notNull = true)
    )
    private String groupLevel;

    @FabosJsonField(
            views = @View(title = "是否启用"),
            edit = @Edit(title = "是否启用", index = 107,
                    defaultVal = "true",
                    type = EditType.BOOLEAN,
                    boolType = @BoolType(trueText = "启用", falseText = "禁用"), search = @Search(vague = true)
            )
    )
    private Boolean enableFlag;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "ecs_first_user",
            joinColumns = @JoinColumn(name = "notice_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "user_id", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)))
    @FabosJsonField(
            views = @View(title = "推送人员", column = "name", export = false, type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "推送人员", index = 1,
                    filter = @Filter(value = "MetaUser.state = 'Y'"),
                    type = EditType.TAB_TABLE_REFER,
                    referenceTableType = @ReferenceTableType)
    )
    private List<MetaUser> firstUsers;

    @FabosJsonField(
            views = @View(title = "推送方式"),
            edit = @Edit(title = "推送方式", notNull = true, index = 108,
                    type = EditType.multiple_select,
                    choiceType = @ChoiceType(fetchHandler = MessageWayEnum.ChoiceFetch.class),
                    search = @Search(vague = true)
            )
    )
    private String firstWay;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "微信群", column = "name"),
            edit = @Edit(title = "微信群", index = 109, type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(type = ReferenceTableType.SelectShowTypeMTO.LIST)
//                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "firstWay.includes('WeChat') ")
            )
    )
    private WeixinGroup firstWeixinGroup;
    /**
     * 备注
     */
    @FabosJsonField(
            edit = @Edit(title = "备注", notNull = false, index = 110,
                    type = EditType.TEXTAREA, inputType = @InputType(length = 400), formColumns = 1)
    )
    private String desciption;

    @FabosJsonField(
            views = @View(title = "是否需要处理"),
            edit = @Edit(title = "是否需要处理", index = 111,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoStatus.ChoiceFetch.class)
            )
    )
    private String isProcess;

    @ManyToMany(fetch = FetchType.EAGER)
    @FabosJsonField(
            views = @View(title = "表单跳转地址配置", column = "name", type = ViewType.TABLE_VIEW, export = false),
            edit = @Edit(title = "表单跳转地址配置", type = EditType.TAB_TABLE_REFER, index = 112,
                    allowAddMultipleRows = true,
                    tabTableReferType = @TabTableReferType(type = TabTableReferType.SelectShowTypeMTM.LIST,
                            label = "name"
                    ),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "isProcess == 'Y'")
            )
    )
    @JoinTable(name = "ecs_messagegroup_process_form_config",
            joinColumns = @JoinColumn(name = "message_group_id", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)),
            inverseJoinColumns = @JoinColumn(name = "message_process_form_config_id", referencedColumnName = "id",
                    foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)))
    List<MessageProcessFormConfig> messageProcessFormConfigs;

    /**
     * 升级次数
     */
    @FabosJsonField(
            views = @View(title = "升级次数"),
            edit = @Edit(title = "升级次数", notNull = true, index = 113,
                    defaultVal = "1",
                    type= EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = UpgradeTimesEnum.ChoiceFetch.class),
                    numberType = @NumberType(min = 1, max = 5),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "isProcess == 'Y'"))
    )
    private Long upNumber;

    // ---升级配置
    @FabosJsonField(
            edit = @Edit(title = "升级配置", type = EditType.DIVIDE, index = 300,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='2'")
            )
    )
    @Transient
    private String upgradeDivide;


    // ---一次升级配置
//    @FabosJsonField(
//            edit = @Edit(title = "一次升级配置", type = EditType.DIVIDE, index = 300,
//                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='2'")
//            )
//    )
//    @Transient
//    private String secondDivide;

    /**
     * 一次升级间隔时间(分)
     */
    @FabosJsonField(
            views = @View(title = "一次升级间隔时间"),
            edit = @Edit(title = "一次升级间隔时间", notNull = true, index = 301, tips = "单位：分钟",
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='2'"),
                    numberType = @NumberType(min = 1, max = 60000))
    )
    private Long secondIntervalTime;


    @FabosJsonField(
            views = @View(title = "一次升级推送方式"),
            edit = @Edit(title = "一次升级推送方式", index = 302,
                    type = EditType.multiple_select, notNull = true,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='2'"),
                    choiceType = @ChoiceType(fetchHandler = MessageWayEnum.ChoiceFetch.class), search = @Search(vague = true)
            )
    )
    private String secondWay;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "一次升级微信群", column = "name"),
            edit = @Edit(title = "一次升级微信群", index = 303,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='2'"),
                    type = EditType.REFERENCE_TABLE, referenceTableType = @ReferenceTableType(type = ReferenceTableType.SelectShowTypeMTO.LIST)
            )
    )
    private WeixinGroup secondWeixinGroup;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "ecs_second_user",
            joinColumns = @JoinColumn(name = "notice_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "user_id", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)
            ))
    @FabosJsonField(
            views = @View(title = "一次升级推送人员", column = "name", export = false, type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "一次升级推送人员", index = 304,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='2'"),
                    filter = @Filter(value = "MetaUser.state = 'Y'"),
                    type = EditType.TAB_TABLE_REFER,
                    tabTableReferType = @TabTableReferType())
    )
    private List<MetaUser> secondUsers;

    // ---二次升级配置
//    @FabosJsonField(
//            edit = @Edit(title = "二次升级配置", type = EditType.DIVIDE, index = 400,
//                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='3'"))
//    )
//    @Transient
//    private String thirdDivide;

    /**
     * 二次升级间隔时间(分)
     */
    @FabosJsonField(
            views = @View(title = "二次升级间隔时间"),
            edit = @Edit(title = "二次升级间隔时间", notNull = true, index = 401,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='3'"),
                    numberType = @NumberType(min = 1, max = 60000))
    )
    private Long thirdIntervalTime;

    @FabosJsonField(
            views = @View(title = "二次升级推送方式"),
            edit = @Edit(title = "二次升级推送方式", index = 402,
                    type = EditType.multiple_select, notNull = true,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='3'"),
                    choiceType = @ChoiceType(fetchHandler = MessageWayEnum.ChoiceFetch.class), search = @Search(vague = true)
            )
    )
    private String thirdWay;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "二次升级微信群", column = "name"),
            edit = @Edit(title = "二次升级微信群", index = 403,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='3'"),
                    type = EditType.REFERENCE_TABLE, referenceTableType = @ReferenceTableType(type = ReferenceTableType.SelectShowTypeMTO.LIST)
            )
    )
    private WeixinGroup thirdWeixinGroup;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "ecs_third_user",
            joinColumns = @JoinColumn(name = "notice_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "user_id", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)))
    @FabosJsonField(
            views = @View(title = "二次升级推送人员", column = "name", export = false, type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "二次升级推送人员", index = 404,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='3'"),
                    filter = @Filter(value = "MetaUser.state = 'Y'"),
                    type = EditType.TAB_TABLE_REFER,
                    tabTableReferType = @TabTableReferType())
    )
    private List<MetaUser> thirdUsers;


    // ---三次升级配置
//    @FabosJsonField(
//            edit = @Edit(title = "三次升级配置", type = EditType.DIVIDE, index = 500,
//                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='4'"))
//    )
//    @Transient
//    private String fourthDivide;

    /**
     * 三次升级间隔时间(分)
     */
    @FabosJsonField(
            views = @View(title = "三次升级间隔时间"),
            edit = @Edit(title = "三次升级间隔时间", notNull = true, index = 501,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='4'"),
                    numberType = @NumberType(min = 1, max = 60000))
    )
    private Long fourthIntervalTime;

    @FabosJsonField(
            views = @View(title = "三次升级推送方式"),
            edit = @Edit(title = "三次升级推送方式", index = 502,
                    type = EditType.multiple_select, notNull = true,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='4'"),
                    choiceType = @ChoiceType(fetchHandler = MessageWayEnum.ChoiceFetch.class), search = @Search(vague = true)
            )
    )
    private String fourthWay;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "三次升级微信群", column = "name"),
            edit = @Edit(title = "三次升级微信群", index = 503,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='4'"),
                    type = EditType.REFERENCE_TABLE, referenceTableType = @ReferenceTableType(type = ReferenceTableType.SelectShowTypeMTO.LIST)
            )
    )
    private WeixinGroup fourthWeixinGroup;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "ecs_fourth_user",
            joinColumns = @JoinColumn(name = "notice_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "user_id", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)))
    @FabosJsonField(
            views = @View(title = "三次升级推送人员", column = "name", export = false, type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "三次升级推送人员", index = 504,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='4'"),
                    filter = @Filter(value = "MetaUser.state = 'Y'"),
                    type = EditType.TAB_TABLE_REFER,
                    tabTableReferType = @TabTableReferType())
    )
    private List<MetaUser> fourthUsers;


    // ---四次升级配置
//    @FabosJsonField(
//            edit = @Edit(title = "四次升级配置", type = EditType.DIVIDE, index = 600,
//                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='5'"))
//    )
//    @Transient
//    private String fifthDivide;

    /**
     * 四次升级间隔时间(分)
     */
    @FabosJsonField(
            views = @View(title = "四次升级间隔时间"),
            edit = @Edit(title = "四次升级间隔时间", notNull = true, index = 601,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='5'"),
                    numberType = @NumberType(min = 1, max = 60000))
    )
    private Long fifthIntervalTime;

    @FabosJsonField(
            views = @View(title = "四次升级推送方式"),
            edit = @Edit(title = "四次升级推送方式", index = 602,
                    type = EditType.multiple_select, notNull = true,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='5'"),
                    choiceType = @ChoiceType(fetchHandler = MessageWayEnum.ChoiceFetch.class), search = @Search(vague = true)
            )
    )
    private String fifthWay;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "四次升级微信群", column = "name"),
            edit = @Edit(title = "四次升级微信群", index = 603,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='5'"),
                    type = EditType.REFERENCE_TABLE, referenceTableType = @ReferenceTableType(type = ReferenceTableType.SelectShowTypeMTO.LIST)
            )
    )
    private WeixinGroup fifthWeixinGroup;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "ecs_fifth_user",
            joinColumns = @JoinColumn(name = "notice_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "user_id", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)))
    @FabosJsonField(
            views = @View(title = "四次升级推送人员", column = "name", export = false, type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "四次升级推送人员", index = 604,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='5'"),
                    filter = @Filter(value = "MetaUser.state = 'Y'"),
                    type = EditType.TAB_TABLE_REFER,
                    tabTableReferType = @TabTableReferType())
    )
    private List<MetaUser> fifthUsers;


    // ---推送人员配置
//    @FabosJsonField(
//            edit = @Edit(title = "告警推送人员配置", type = EditType.DIVIDE, index = 99999
//            )
//    )
//    @Transient
//    private String toUserDivide;

    public String getDispatchWay() {
        Map<String, Object> dispatchWay = new HashMap<>(4);
        dispatchWay.put("first", StringUtils.join(this.getFirstWay().split(","), ","));

        if (this.getUpNumber() != null && this.getUpNumber() > 1) {
            dispatchWay.put("second", StringUtils.join(this.getSecondWay().split(","), ","));
        }
        if (this.getUpNumber() != null && this.getUpNumber() > 2) {
            dispatchWay.put("third", StringUtils.join(this.getThirdWay().split(","), ","));
        }
        return JSON.toJSONString(dispatchWay);
    }

    public String getDispatchUser() {
        Map<String, Object> dispatchUser = new HashMap<>(3);
        dispatchUser.put("first", StringUtils.join(this.getFirstUsers().stream()
                .map(MetaUser::getPhoneNumber)
                .collect(Collectors.toList()), ","));
        if (this.getUpNumber() != null && this.getUpNumber() > 1) {
            dispatchUser.put("second", StringUtils.join(this.getSecondUsers().stream()
                    .map(MetaUser::getPhoneNumber)
                    .collect(Collectors.toList()), ","));
        }
        if (this.getUpNumber() != null && this.getUpNumber() > 2) {
            dispatchUser.put("third", StringUtils.join(this.getThirdUsers().stream()
                    .map(MetaUser::getPhoneNumber)
                    .collect(Collectors.toList()), ","));
        }
        return JSON.toJSONString(dispatchUser);
    }
}
