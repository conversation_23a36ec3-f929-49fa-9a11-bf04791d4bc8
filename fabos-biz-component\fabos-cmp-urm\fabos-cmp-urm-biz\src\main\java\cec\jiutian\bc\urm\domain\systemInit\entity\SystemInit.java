package cec.jiutian.bc.urm.domain.systemInit.entity;

import cec.jiutian.bc.urm.domain.systemBasicConfig.event.SystemBasicConfigDataProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.type.Power;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;


@Entity
@Table(name = "system_init")
@Getter
@Setter
@FabosJson(
        name = "基础配置",
        orderBy = "SystemInit.createTime desc",
        dataProxy = {SystemBasicConfigDataProxy.class},
        power = @Power(add = false,delete = false)
)
public class SystemInit extends MetaModel {

    @FabosJsonField(
            views = @View(title = "初始化项"),
            edit = @Edit(title = "初始化项", notNull = true)
    )
    private String initItem;

    @FabosJsonField(
            views = @View(title = "初始化时间"),
            edit = @Edit(title = "初始化时间", notNull = true)
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime initTime;

}
