package cec.jiutian.bc.ecs.domain.message.handler;

import cec.jiutian.bc.ecs.domain.message.entity.Message;
import cec.jiutian.bc.ecs.domain.message.entity.MessageProcessDetailMto;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.util.ListUtils;
import cec.jiutian.core.frame.constant.YesOrNoStatus;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年03月06日 9:34
 */
@Component
public class MessageProcessCompletedHandler implements OperationHandler<Message, MessageProcessDetailMto> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    @Transactional
    public String exec(List<Message> messageList, MessageProcessDetailMto messageProcessDetailMto, String[] param) {
        ListUtils.forEach(messageList, item -> {
            if (YesOrNoStatus.YES.getValue().equals(item.getIsProcess())) {
                item.setProcessCompleteFlag(YesOrNoStatus.YES.getValue());
                item.setProcessDetail(messageProcessDetailMto.getProcessDetail());
                item.setProcessUser(fabosJsonDao.getById(MetaUser.class, UserContext.get().getUserId()));
                item.setProcessTime(LocalDateTime.now());
                fabosJsonDao.mergeAndFlush(item);
            }
        });
        return "msg.success('操作成功!')";
    }
}
