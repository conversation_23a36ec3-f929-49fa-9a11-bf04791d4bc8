package cec.jiutian.bc.generalModeler.domain.material.model;

import cec.jiutian.bc.generalModeler.domain.material.proxy.ProductCategoryDataProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceTreeType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.config.Comment;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Tree;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;


@Entity
@Table(name = "product_category",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"code"})
        }
)
@FabosJson(
        name = "产品/零部件分类", dataProxy = ProductCategoryDataProxy.class,
        orderBy = "ProductCategory.sort",
        tree = @Tree(pid = "parentCategory.id", expandLevel = 4)
)
@Getter
@Setter
@TemplateType(type = "treeForm")
public class ProductCategory extends MetaModel {

    @FabosJsonField(
            views = @View(title = "分类代码"),
            edit = @Edit(title = "分类代码", readonly = @Readonly(add = false, edit = true), notNull = true)
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "分类名称"),
            edit = @Edit(title = "分类名称", notNull = true, readonly = @Readonly(add = false, edit = true))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "显示顺序"),
            edit = @Edit(title = "显示顺序", numberType = @NumberType(min = 1))
    )
    private Integer sort;

    @ManyToOne
    @FabosJsonField(
            edit = @Edit(
                    title = "上级分类代码",
                    type = EditType.REFERENCE_TREE,
                    notNull = false,
                    referenceTreeType = @ReferenceTreeType(pid = "parentCategory.id", expandLevel = 4)
            )
    )
    private ProductCategory parentCategory;

    @FabosJsonField(
            edit = @Edit(title = "分类级别", show = false),
            views = @View(title = "分类级别", show = true)
    )
    @Comment("逻辑字段，生成的当前类别的级别")
    private Integer categoryLevel;


    @FabosJsonField(
            edit = @Edit(title = "描述", notNull = false,
                    type = EditType.TEXTAREA, inputType = @InputType(length = 400))
    )
    private String desciption;

    @FabosJsonField(
            views = @View(title = "主类别"),
            edit = @Edit(title = "主类别",
                    search = @Search(value = true), readonly = @Readonly, show = false)
    )
    @Comment("后端赋值")
    private String mainCategory;

//    @ManyToOne
//    @FabosJsonField(
//            views = @View(title = "物料属性模板", column = "code"),
//            edit = @Edit(title = "物料属性模板", notNull = false,
//                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "categoryLevel == 2"),
//                    type = EditType.REFERENCE_TABLE,
//                    referenceTableType = @ReferenceTableType(id = "id", label = "code")
//            )
//    )
//    private MaterialAttributeTemplate materialAttributeTemplate;


}
