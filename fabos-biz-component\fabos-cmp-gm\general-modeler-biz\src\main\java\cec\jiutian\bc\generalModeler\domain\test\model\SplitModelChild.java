package cec.jiutian.bc.generalModeler.domain.test.model;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.config.Comment;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

@FabosJson(
        name = "组合模型子级",
        dataProxy = SplitModelChildDataProxy.class
)
@Table(name = "split_model_full_child"
)
@Entity
@Getter
@Setter
public class SplitModelChild extends MetaModel {


    @ManyToOne
    @Comment("引用字段，对应Full的ID")
    @FabosJsonField(
            views = @View(title = "父级", column = "displayName"),
            edit = @Edit(title = "父级", notNull = true, search = @Search(vague = true),
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "displayName")
            )
    )
    private SplitModelFull splitModelFull;


    @Transient
    @FabosJsonField(
            views = @View(title = "父表字段1")
    )
    private String p1;
}
