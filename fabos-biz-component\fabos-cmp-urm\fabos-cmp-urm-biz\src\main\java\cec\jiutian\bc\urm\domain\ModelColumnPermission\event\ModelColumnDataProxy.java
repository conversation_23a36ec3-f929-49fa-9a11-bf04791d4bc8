package cec.jiutian.bc.urm.domain.ModelColumnPermission.event;

import cec.jiutian.bc.urm.domain.ModelColumnPermission.entity.ModelColumnPermission;
import cec.jiutian.meta.model.MetadataModelField;
import cec.jiutian.view.fun.DataProxy;
import org.springframework.stereotype.Component;

@Component
public class ModelColumnDataProxy implements DataProxy<ModelColumnPermission> {
    @Override
    public void beforeAdd(ModelColumnPermission modelColumnPermission) {
        MetadataModelField field = modelColumnPermission.getField();
        modelColumnPermission.setModelDisplayName(field.getReferenceModel().getDisplayName());
        modelColumnPermission.setFiledDisplayName(field.getDisplayName());
        modelColumnPermission.setFieldId(field.getId());
        modelColumnPermission.setMetadataId(field.getId());
    }
}
