package cec.jiutian.bc.urm.domain.role.event;

import cec.jiutian.bc.urm.domain.role.entity.Role;
import cec.jiutian.bc.urm.domain.role.entity.RoleAuthDistribute;
import cec.jiutian.bc.urm.domain.user.service.UserService;
import cec.jiutian.core.frame.constant.MenuTypeEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.fc.log.domain.permissionOperationLog.dto.PermissionDTO;
import cec.jiutian.fc.log.domain.permissionOperationLog.manager.PermissionLogManager;
import cec.jiutian.fc.log.enums.PermissionTypeEnum;
import cec.jiutian.view.fun.OperationHandler;
import cec.jiutian.bc.urm.domain.menu.entity.Menu;
import cec.jiutian.bc.urm.dto.MetaUserinfo;
import cec.jiutian.bc.urm.enums.UserManagerTypeEnum;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import cec.jiutian.bc.urm.service.RoleAuthDistributeService;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 */
@Component
@Slf4j
public class RoleAuthOperationHandler implements OperationHandler<RoleAuthDistribute, Menu> {
    private final EntityManagerFactory entityManagerFactory;
    private final UserService userService;
    private final FabosJsonDao fabosJsonDao;
    private final PermissionLogManager permissionLogManager;

    private final RoleAuthDistributeService roleAuthDistributeService;


    public RoleAuthOperationHandler(EntityManagerFactory entityManagerFactory, UserService userService, FabosJsonDao fabosJsonDao, PermissionLogManager permissionLogManager, RoleAuthDistributeService roleAuthDistributeService) {
        this.entityManagerFactory = entityManagerFactory;
        this.userService = userService;
        this.fabosJsonDao = fabosJsonDao;
        this.permissionLogManager = permissionLogManager;
        this.roleAuthDistributeService = roleAuthDistributeService;
    }
    @Override
    public String exec(List<RoleAuthDistribute> data, Menu modelObject, String[] param) {
        MetaUserinfo curr = userService.getSimpleUserInfo();
        if(curr==null){
            return "msg.error('current user is not login!')";
        }
        String roleId = data.get(0).getId();
        String sql1 = "select menu_id from fd_role_menu  where role_id = '" + roleId +"'";
        List<String> idsDb = fabosJsonDao.getJdbcTemplate().query(sql1, (rs, rowNum) -> rs.getString("menu_id"));

        EntityManager entityManager = entityManagerFactory.createEntityManager();
        try {
            entityManager.getTransaction().begin();
            if(param!=null){
                // 使用SQL进行删除操作
                String deletedSql = "DELETE FROM fd_role_menu WHERE role_id = ?";
                Query dquery = entityManager.createNativeQuery(deletedSql);
                dquery.setParameter(1, roleId); // 设置要删除记录的ID值
                dquery.executeUpdate(); // 执行删除操作
                for (String menuId:param) {
                    String sql="INSERT INTO fd_role_menu (role_id, menu_id) VALUES( ?1, ?2)";
                    Query query=entityManager.createNativeQuery(sql);
                    query.setParameter(1,roleId);
                    query.setParameter(2,menuId);
                    query.executeUpdate();
                }
            }
            entityManager.flush();
            entityManager.getTransaction().commit();
        }catch (Exception e){
            entityManager.getTransaction().rollback();
        }finally {
            entityManager.close();
        }
        List<String> ids = Arrays.stream(param).toList();
        try {
            permissionLogManager.updatePermission(roleAuthDistributeService.getPermissionDTO(roleId,data.get(0).getName(),idsDb,ids), PermissionTypeEnum.MENU);
        }catch (Exception e){
            e.printStackTrace();
            log.error("角色添加菜单添加三员日志异常:",e);
        }
        return "msg.success('成功')";
    }


}
