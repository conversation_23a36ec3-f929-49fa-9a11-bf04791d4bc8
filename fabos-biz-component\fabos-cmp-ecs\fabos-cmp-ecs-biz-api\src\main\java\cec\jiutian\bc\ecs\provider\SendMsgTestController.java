package cec.jiutian.bc.ecs.provider;

import cec.jiutian.bc.ecs.dto.SendMsgGroupDTO;
import cec.jiutian.bc.ecs.dto.SendMsgToPersonDTO;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/fabos-cmp-ecs/message")
public class SendMsgTestController {

    @Resource
    private EcsMessageProvider rabbitMQSender;

    @RequestMapping("/sendMsgToP")
    public void sendMsg(@RequestBody SendMsgToPersonDTO sendMsgToPersonDTO) {
        rabbitMQSender.sendPersonMessage(sendMsgToPersonDTO);
    }

    @RequestMapping("/sendMsgToG")
    public void sendGroupMsg(@RequestBody SendMsgGroupDTO sendMsgGroupDTO) {
        rabbitMQSender.sendGroupMessage(sendMsgGroupDTO);
    }

}
