package cec.jiutian.bc.ecs.enums;


import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
public enum MessageWayEnum {

    Email("邮件","Email"),
    WeChat("微信", "WeChat"),

//    SMS("短信","SMS"),

    App("站内信", "App");


    private final String name;

    private final String code;

    MessageWayEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public static class ChoiceFetch implements ChoiceFetchHandler {
        @Override
        public List<VLModel> fetch(String[] params) {
            return Stream.of(MessageWayEnum.values())
                    .map(messageWayEnum -> new VLModel(messageWayEnum.getCode(), messageWayEnum.getName()))
                    .collect(Collectors.toList());

        }

    }
}
