package cec.jiutian.bc.infra.notify;

import cec.jiutian.core.prop.FabosJsonProp;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;


@Component
public class RedisNotifyDataProxy implements DataProxy<Object> {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private FabosJsonProp fabosJsonProp;

    @Override
    public void afterAdd(Object o) {
        this.publish(DataAction.ADD, o);
    }


    @Override
    public void afterUpdate(Object o) {
        this.publish(DataAction.UPDATE, o);
    }

    @Override
    public void afterDelete(Object o) {
        this.publish(DataAction.DELETE, o);
    }

    private void publish(DataAction action, Object data) {
        if (fabosJsonProp.isRedisSession()) {
//            if (null == DataProxyContext.params()[0]) {
//                throw new FabosJsonWebApiRuntimeException("DataProxy params[0] not found → redis channel");
//            }
//            stringRedisTemplate.convertAndSend(DataProxyContext.params()[0], GsonFactory.getGson().toJson(new NotifyData<>(action, data)));
        }
    }

}
