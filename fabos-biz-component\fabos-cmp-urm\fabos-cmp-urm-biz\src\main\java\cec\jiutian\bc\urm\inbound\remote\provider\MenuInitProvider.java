package cec.jiutian.bc.urm.inbound.remote.provider;

import cec.jiutian.bc.urm.domain.menu.service.MenuService;
import cec.jiutian.bc.urm.dto.MenuData;
import cec.jiutian.bc.urm.dto.MetaMenu;
import cec.jiutian.bc.urm.provider.IMenuInitProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/29
 */
@Slf4j
@Component
public class MenuInitProvider implements IMenuInitProvider {
    private static List<MenuData> baseMenuList=new ArrayList<>();

    private final MenuService menuService;

    public MenuInitProvider(MenuService menuService) {
        this.menuService = menuService;
    }

    @Override
    public void menuInit(List<MenuData> items) {
        // TODO: huay 2024/5/29 实现菜单的数据初始化
        baseMenuList.addAll(items);
        try {
            menuService.batchSaveOrUpdate(items);
        }catch (Exception e){
            log.error("菜单加载失败！",e);
        }
    }

    @Override
    public synchronized void menuInit(String id) {
        try {
            baseMenuList.forEach(it->it.setOid(id));
            menuService.batchSaveOrUpdate(baseMenuList);
        }catch (Exception e){
            log.error("菜单加载失败！",e);
        }
    }

    @Override
    public void menuDataInit(List<MetaMenu> items) {
        menuService.saveMetaMenus4Init(items);
    }
}
