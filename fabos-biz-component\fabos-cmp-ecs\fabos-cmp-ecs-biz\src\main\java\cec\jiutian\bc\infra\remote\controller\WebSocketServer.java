package cec.jiutian.bc.infra.remote.controller;

import cec.jiutian.bc.ecs.enums.WebscoketProcessTypeEnum;
import cec.jiutian.common.util.JacksonUtil;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import jakarta.websocket.OnClose;
import jakarta.websocket.OnMessage;
import jakarta.websocket.OnOpen;
import jakarta.websocket.Session;
import jakarta.websocket.server.ServerEndpoint;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;

@Component
@Slf4j
@ServerEndpoint("/fabos-cmp-ecs/websocket/getMessage")
public class WebSocketServer {

    // 因为可能有多个客户端所以这里需要保证线程安全
    private static Map<String,Session> sessionMap = new ConcurrentHashMap<>();
    private static Map<String, ConcurrentLinkedQueue<String>> userSessionMap = new ConcurrentHashMap<>();
    private static Map<String,String> sessionUserMap = new ConcurrentHashMap<>();
    // 建立连接时执行的操作
    @OnOpen
    public void onOpen(Session session){
        List<String> ids = session.getRequestParameterMap().get("id");
        if (CollectionUtils.isEmpty(ids)) {
            throw new FabosJsonApiErrorTip("用户未登录");
        }
        String id = ids.get(0);
        // 每个websocket连接对于服务端来说都是一个Session
        sessionMap.put(session.getId(), session);
        sessionUserMap.put(session.getId(), id);
        ConcurrentLinkedQueue<String> sessionQueue = userSessionMap.get(id);
        if (sessionQueue == null) {
            sessionQueue = new ConcurrentLinkedQueue<>();
            userSessionMap.put(id, sessionQueue);
        }
        sessionQueue.add(session.getId());
        log.info("websocket is open: {}", id);
    }

    /**
     * 收到客户端消息时执行的操作
     * @param text 接受到客户端的消息
     * @return 返回给客户端的消息
     */
    @OnMessage
    public String onMessage(String text){
//        log.info("收到一条新消息: " + text);
        return "send success";
    }

    // 连接关闭时执行的操作
    @OnClose
    public void onClose(Session session) {
        log.info("sessionId: " + session.getId() + " 关闭连接, session状态："+session.isOpen());
        sessionMap.remove(session.getId());
        String userKey = sessionUserMap.get(session.getId());
        sessionUserMap.remove(session.getId());
        ConcurrentLinkedQueue<String> sessionIds = userSessionMap.get(userKey);
        sessionIds.remove(session.getId());
        log.info("websocket is close");
    }

    @Scheduled(fixedRate = 15000) // 每隔15s执行一次
    public static void sendMessage() throws IOException {
        for(String key: sessionMap.keySet()){ // 给所有客户端发送消息
            HashMap<String, Object> res = new HashMap<>();
//            res.put("newMessage", true);
            res.put("beat", "beat");
            sessionMap.get(key).getBasicRemote().sendText(JacksonUtil.toJson(res));
        }
    };

    public static void sendMessage(String message,String userKey) throws IOException {
        if (!userSessionMap.containsKey(userKey)){
            return;
        }
        ConcurrentLinkedQueue<String> concurrentLinkedQueue = userSessionMap.get(userKey);
        for (String sessionId : concurrentLinkedQueue) {
            sessionMap.get(sessionId).getBasicRemote().sendText(message);
        }
    };

    public static void sendMessage(String userKey) throws IOException {
        log.info("sendMessage websocket userKey: {}", userKey);
        HashMap<String, Object> res = new HashMap<>(4);
        res.put("newMessage", true);
        sendMessage(JacksonUtil.toJson(res), userKey);
    };

    /**
     * 发送即时报警消息
     *
     * @param alarmMessage 报警消息内容
     * @param userKey      用户标识
     * @throws IOException 如果发送消息失败
     */
    public static void sendImmediateAlarm(String alarmMessage, String userKey) throws IOException {
        log.info("sendImmediateAlarm websocket userKey: {}, alarmMessage: {}", userKey, alarmMessage);
        HashMap<String, Object> res = new HashMap<>(4);
        res.put("processType", WebscoketProcessTypeEnum.ImmediateAlarm.getCode());
        res.put("alarmMessage", alarmMessage);
        sendMessage(JacksonUtil.toJson(res), userKey);
    }

}
