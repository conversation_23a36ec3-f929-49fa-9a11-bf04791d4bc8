package cec.jiutian.bc.generalModeler.domain.supplier.handler;

import cec.jiutian.bc.generalModeler.domain.supplier.model.Supplier;
import cec.jiutian.bc.generalModeler.enumeration.SupplierStatusEnum;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2024/10/31 16:42
 * @description：
 */
@Component
public class SupplierInValidateOperationHandler implements OperationHandler<Supplier, Void> {

//    @Resource
//    private FabosJsonDao jsonDao;

    @Resource
    private JpaCrud jpaCrud;

    @Override
    public String exec(List<Supplier> data, Void modelObject, String[] param) {
        //EntityManager entityManager = jsonDao.getEntityManager();
        if (CollectionUtils.isNotEmpty(data)) {
            Supplier supplier = data.get(0);
            supplier.setStatus(SupplierStatusEnum.Enum.Invalid.getCode());
            jpaCrud.update(supplier);
        }
        //entityManager.persist(data);
        return "msg.success('操作成功')";
    }
}
