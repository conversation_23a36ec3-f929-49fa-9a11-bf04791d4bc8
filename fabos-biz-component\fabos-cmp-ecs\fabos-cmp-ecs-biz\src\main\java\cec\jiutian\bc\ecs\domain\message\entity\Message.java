package cec.jiutian.bc.ecs.domain.message.entity;

import cec.jiutian.bc.ecs.domain.message.UpgradeTimesEnum;
import cec.jiutian.bc.ecs.domain.message.event.MessageDataProxy;
import cec.jiutian.bc.ecs.domain.message.handler.MessageCategoryChangeDynamicHandler;
import cec.jiutian.bc.ecs.domain.message.handler.MessageGroupChangeDynamicHandler;
import cec.jiutian.bc.ecs.domain.messagegroup.entity.MessageCategory;
import cec.jiutian.bc.ecs.domain.messagegroup.entity.MessageGroup;
import cec.jiutian.bc.ecs.domain.messagegroup.entity.WeixinGroup;
import cec.jiutian.bc.ecs.enumeration.MessageGroupLevelEnum;
import cec.jiutian.bc.ecs.enums.MessageWayEnum;
import cec.jiutian.core.frame.constant.YesOrNoStatus;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.CascadeType;
import jakarta.persistence.ConstraintMode;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Entity
@Table(name = "ecs_message")
@Getter
@Setter
@FabosJson(name = "告警信息", dataProxy = {MessageDataProxy.class},
        orderBy = "Message.createTime desc",
        power = @Power(add = true, edit = true, delete = true, export = true, importable = false, viewDetails = true),
        colorIndicator = "colorFlag",
        formColumnElements = 6,
        rowOperation = {
//                @RowOperation(
//                        title = "处理",
//                        code = "Message@PROCESSCOMPLETED",
//                        mode = RowOperation.Mode.HEADER,
//                        type = RowOperation.Type.POPUP,
//                        operationHandler = MessageProcessCompletedHandler.class,
//                        popupType = RowOperation.PopupType.FORM,
//                        fabosJsonClass = MessageProcessDetailMto.class,
//                        submitMethod = RowOperation.SubmitMethod.HANDLER,
//                        show = @ExprBool(
//                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
//                                params = "Message@PROCESSCOMPLETED"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
//                        ),
//                        ifExpr = "selectedItems[0].processCompleteFlag == 'Y' || selectedItems[0].isProcess != 'Y'",
//                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
//                )
                @RowOperation(
                        title = "处理",
                        code = "Message@PROCESSCOMPLETED",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        menuButtonType = RowOperation.MenuButtonTypeEnum.MIX,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "Message@PROCESSCOMPLETED"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "selectedItems[0].processCompleteFlag == 'Y' || selectedItems[0].isProcess != 'Y'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                )
        }


)
@FabosJsonI18n
public class Message extends MetaModel {

    @Transient
    private String colorFlag;

    @FabosJsonField(
            views = @View(title = "是否为手动创建", show = false),
            edit = @Edit(title = "是否为手动创建", show = false)
    )
    private Boolean manualCreateFlag;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "告警分类", column = "name"),
            edit = @Edit(index = 80, title = "告警分类", notNull = true, search = @Search(vague = true),
                    type = EditType.REFERENCE_TABLE, referenceTableType = @ReferenceTableType(id = "id", label = "name")
            ))
    @JoinColumn(foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    private MessageCategory messageCategory;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "告警类型", column = "groupName"),
            edit = @Edit(index = 81, title = "告警类型", notNull = true, search = @Search(vague = true),
                    type = EditType.REFERENCE_TABLE, referenceTableType = @ReferenceTableType(id = "id", label = "groupName"),
                    filter = @Filter(value = "enableFlag = true")
                    , queryCondition = "{\"messageCategory\":\"${messageCategory}\"}"

            ),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "messageCategory", dynamicHandler = MessageCategoryChangeDynamicHandler.class)
            )
    )
    @JoinColumn(foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    private MessageGroup messageGroup;

    // 以下属性为告警处理过程更新
    /**
     * 消息内容
     */
    @FabosJsonField(
            views = @View(title = "消息内容"),
            edit = @Edit(index = 82, title = "消息内容", notNull = true, type = EditType.TEXTAREA, formColumns = 2)
    )
    private String content;

    /**
     * 表单跳转参数，为json格式的字符串"{"param1": "a", "param2": "b"}"，跳转时，将参数拼接到url后面
     */
    @FabosJsonField(
            views = @View(title = "表单跳转参数"),
            edit = @Edit(index = 83, title = "表单跳转参数", formColumns = 2,
                    type = EditType.TEXTAREA
            )
    )
    private String formUrlParameters;


    //以下属性为选择告警类型后带出
    @FabosJsonField(
            views = @View(title = "标题"),
            edit = @Edit(index = 103, title = "标题", search = @Search(vague = true), readonly = @Readonly),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "messageGroup", dynamicHandler = MessageGroupChangeDynamicHandler.class)
            )
    )
    private String title;

    @FabosJsonField(
            views = @View(title = "告警级别"),
            edit = @Edit(index = 104, title = "告警级别", search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = MessageGroupLevelEnum.ChoiceFetch.class),
                    type = EditType.CHOICE, notNull = true, readonly = @Readonly),
            dynamicField = @DynamicField(passive = true)

    )
    private String groupLevel;

    /**
     * 升级次数
     */
    @FabosJsonField(
            views = @View(title = "升级次数"),
            edit = @Edit(index = 105, title = "升级次数", search = @Search(vague = true),
                    type= EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = UpgradeTimesEnum.ChoiceFetch.class), readonly = @Readonly),
            dynamicField = @DynamicField(dependFiled = @DependFiled(changeBy = "dummy")
            )
    )
    private Long upNumber;


    @FabosJsonField(
            views = @View(title = "推送方式"),
            edit = @Edit(index = 106, title = "推送方式", notNull = true, search = @Search(vague = true),
                    type = EditType.multiple_select,
                    choiceType = @ChoiceType(fetchHandler = MessageWayEnum.ChoiceFetch.class), readonly = @Readonly
            ),
            dynamicField = @DynamicField(passive = true)
    )
    private String firstWay;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "ecs_message_first_user",
            joinColumns = @JoinColumn(name = "notice_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "user_id", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)))
    @FabosJsonField(
            views = @View(title = "推送人员", column = "name", export = false),
            edit = @Edit(title = "推送人员", index = 10, search = @Search(vague = true),
                    notNull = true,
                    type = EditType.TAB_TABLE_REFER,
                    referenceTableType = @ReferenceTableType, readonly = @Readonly),
            dynamicField = @DynamicField(passive = true)
    )
    private List<MetaUser> firstUsers;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "微信群", column = "name"),
            edit = @Edit(index = 107, title = "微信群", readonly = @Readonly,
                    type = EditType.REFERENCE_TABLE, referenceTableType = @ReferenceTableType(type = ReferenceTableType.SelectShowTypeMTO.LIST)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    private WeixinGroup firstWeixinGroup;

    /**
     * 是否需要处理
     */
    @FabosJsonField(
            views = @View(title = "是否需要处理"),
            edit = @Edit(index = 108, title = "是否需要处理", readonly = @Readonly, search = @Search(vague = true),
                    type = EditType.CHOICE,
                    defaultVal = "N",
                    choiceType = @ChoiceType(fetchHandler = YesOrNoStatus.ChoiceFetch.class)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    private String isProcess;

    /**
     * 表单跳转地址配置
     */
    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "message_id")
    @FabosJsonField(
            views = @View(title = "跳转表单详情", column = "formName", type = ViewType.TABLE_VIEW, export = false),
            edit = @Edit(title = "跳转表单详情", show = false)
    )
    List<MessageProcessFormDetail> messageProcessFormConfigs;




    @FabosJsonField(
            views = @View(title = "表单跳转地址"),
            edit = @Edit(index = 110, title = "表单跳转地址", readonly = @Readonly),
            dynamicField = @DynamicField(passive = true)
    )
    private String processFormNames;



    /**
     * 当前升级次数
     */
    @FabosJsonField(
            views = @View(title = "当前升级次数"),
            edit = @Edit(index = 112, title = "当前升级次数", search = @Search(vague = true),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = UpgradeTimesEnum.ChoiceFetch.class), readonly = @Readonly
            )
    )
    private Integer currentUpNumber;

    /**
     * 下次升级时间
     */
    @FabosJsonField(
            views = @View(title = "下次升级时间", type = ViewType.text),
            edit = @Edit(index = 113, title = "下次升级时间", readonly = @Readonly, type = EditType.DATE_TIME_RANGE)
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Asia/Shanghai")
    private LocalDateTime nextUpgradeTime;


    /**
     * 消息接收时间
     */
    @FabosJsonField(
            views = @View(title = "消息接收时间", type = ViewType.text),
            edit = @Edit(index = 114, title = "消息接收时间", readonly = @Readonly, search = @Search(vague = true), type = EditType.DATE_TIME_RANGE)
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Asia/Shanghai")
    private LocalDateTime receivedTime;

    /**
     * 是否已经升级
     */
    @FabosJsonField(
            views = @View(title = "是否已经升级"),
            edit = @Edit(index = 115, title = "是否已经升级",
                    type = EditType.CHOICE, search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = YesOrNoStatus.ChoiceFetch.class), readonly = @Readonly
            )
    )
    private String upgraded;

    @FabosJsonField(
            views = @View(title = "是否已处理"),
            edit = @Edit(index = 116, title = "是否已处理", search = @Search(vague = true),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoStatus.ChoiceFetch.class), readonly = @Readonly
            )
    )
    private String processCompleteFlag;

    @FabosJsonField(
            views = @View(title = "处理说明"),
            edit = @Edit(index = 117, title = "处理说明", type = EditType.TEXTAREA,
                    formColumns = 3, inputType = @InputType(length = 400)
                    , dependFieldDisplay = @DependFieldDisplay(showOrHide = "processCompleteFlag=='Y'")
            )
    )
    private String processDetail;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "处理人", column = "name"),
            edit = @Edit(index = 118, title = "处理人", search = @Search(vague = true),
                    type = EditType.REFERENCE_TABLE, referenceTableType = @ReferenceTableType(id = "id", label = "name")
                    , dependFieldDisplay = @DependFieldDisplay(showOrHide = "processCompleteFlag=='Y'")
            ))
    private MetaUser processUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "处理时间", type = ViewType.text),
            edit = @Edit(index = 119, title = "处理时间", readonly = @Readonly, type = EditType.DATE_TIME_RANGE, search = @Search(vague = true),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "processCompleteFlag=='Y'")
            )
    )
    private LocalDateTime processTime;

    /**
     * 抑制次数
     */
    @FabosJsonField(
            views = @View(title = "抑制次数"),
            edit = @Edit(index = 120, title = "抑制次数", readonly = @Readonly)
    )
    private Integer inhibitionCount;


    /**
     * 消息模板id
     */
    @FabosJsonField(
            views = @View(title = "消息模版id", show = false),
            edit = @Edit(title = "消息模版id", show = false)
    )
    private String messageTemplateId;

    // ---升级配置
    @FabosJsonField(
            edit = @Edit(title = "升级配置", type = EditType.DIVIDE, index = 300,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='2'")
            )
    )
    @Transient
    private String upgradeDivide;


//    // ---一次升级配置
//    @FabosJsonField(
//            edit = @Edit(title = "一次升级配置", type = EditType.DIVIDE, index = 900,
//                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='2'"))
//    )
//    @Transient
//    private String secondDivide;

    /**
     * 一次升级间隔时间(分)
     */
    @FabosJsonField(
            views = @View(title = "一次升级间隔时间"),
            edit = @Edit(title = "一次升级间隔时间", notNull = true, index = 901,
                    readonly = @Readonly,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='2'"),
                    numberType = @NumberType(min = 1, max = 60000)),
            dynamicField = @DynamicField(passive = true)
    )
    private Long secondIntervalTime;


    @FabosJsonField(
            views = @View(title = "一次升级推送方式"),
            edit = @Edit(title = "一次升级推送方式", index = 902,
                    readonly = @Readonly,
                    type = EditType.multiple_select,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='2'"),
                    choiceType = @ChoiceType(fetchHandler = MessageWayEnum.ChoiceFetch.class), search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    private String secondWay;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "ecs_message_second_user",
            joinColumns = @JoinColumn(name = "notice_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "user_id", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)
            ))
    @FabosJsonField(
            views = @View(title = "一次升级推送人员", column = "name", export = false, type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "一次升级推送人员", index = 903,
                    readonly = @Readonly, search = @Search(vague = true),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='2'"),
                    filter = @Filter(value = "MetaUser.state = 'Y'"),
                    type = EditType.TAB_TABLE_REFER,
                    referenceTableType = @ReferenceTableType),
            dynamicField = @DynamicField(passive = true)
    )
    private List<MetaUser> secondUsers;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "一次升级微信群", column = "name"),
            edit = @Edit(title = "一次升级微信群", index = 904,
                    readonly = @Readonly,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='2'"),
                    type = EditType.REFERENCE_TABLE, referenceTableType = @ReferenceTableType(type = ReferenceTableType.SelectShowTypeMTO.LIST)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    private WeixinGroup secondWeixinGroup;

//    // ---二次升级配置
//    @FabosJsonField(
//            edit = @Edit(title = "二次升级配置", type = EditType.DIVIDE,
//                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='3'"))
//    )
//    @Transient
//    private String thirdDivide;

    /**
     * 二次升级间隔时间(分)
     */
    @FabosJsonField(
            views = @View(title = "二次升级间隔时间"),
            edit = @Edit(title = "二次升级间隔时间", notNull = true, index = 905, readonly = @Readonly,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='3'"),
                    numberType = @NumberType(min = 1, max = 60000)),
            dynamicField = @DynamicField(passive = true)
    )
    private Long thirdIntervalTime;

    @FabosJsonField(
            views = @View(title = "二次升级推送方式"),
            edit = @Edit(title = "二次升级推送方式",
                    type = EditType.multiple_select, index = 906, readonly = @Readonly,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber=='3'"),
                    choiceType = @ChoiceType(fetchHandler = MessageWayEnum.ChoiceFetch.class), search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    private String thirdWay;


    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "ecs_message_third_user",
            joinColumns = @JoinColumn(name = "notice_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "user_id", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)))
    @FabosJsonField(
            views = @View(title = "二次升级推送人员", column = "name", export = false, type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "二次升级推送人员", readonly = @Readonly, search = @Search(vague = true),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber=='3'"),
                    filter = @Filter(value = "MetaUser.state = 'Y'"),
                    type = EditType.TAB_TABLE_REFER,
                    referenceTableType = @ReferenceTableType),
            dynamicField = @DynamicField(passive = true)
    )
    private List<MetaUser> thirdUsers;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "二次升级微信群", column = "name"),
            edit = @Edit(title = "二次升级微信群", index = 907, readonly = @Readonly,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='3'"),
                    type = EditType.REFERENCE_TABLE, referenceTableType = @ReferenceTableType(type = ReferenceTableType.SelectShowTypeMTO.LIST)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    private WeixinGroup thirdWeixinGroup;

//    // ---三次升级配置
//    @FabosJsonField(
//            edit = @Edit(title = "三次升级配置", type = EditType.DIVIDE,
//                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='4'"))
//    )
//    @Transient
//    private String fourthDivide;

    /**
     * 三次升级间隔时间(分)
     */
    @FabosJsonField(
            views = @View(title = "三次升级间隔时间"),
            edit = @Edit(title = "三次升级间隔时间", notNull = true,
                    index = 908, readonly = @Readonly,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='4'"),
                    numberType = @NumberType(min = 1, max = 60000)),
            dynamicField = @DynamicField(passive = true)
    )
    private Long fourthIntervalTime;

    @FabosJsonField(
            views = @View(title = "三次升级推送方式"),
            edit = @Edit(title = "三次升级推送方式",
                    type = EditType.multiple_select,
                    index = 909, readonly = @Readonly,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber=='4'"),
                    choiceType = @ChoiceType(fetchHandler = MessageWayEnum.ChoiceFetch.class), search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    private String fourthWay;


    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "ecs_message_fourth_user",
            joinColumns = @JoinColumn(name = "notice_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "user_id", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)))
    @FabosJsonField(
            views = @View(title = "三次升级推送人员", column = "name", export = false, type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "三次升级推送人员",
                    readonly = @Readonly, search = @Search(vague = true),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber=='4'"),
                    filter = @Filter(value = "MetaUser.state = 'Y'"),
                    type = EditType.TAB_TABLE_REFER,
                    referenceTableType = @ReferenceTableType),
            dynamicField = @DynamicField(passive = true)
    )
    private List<MetaUser> fourthUsers;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "三次升级微信群", column = "name"),
            edit = @Edit(title = "三次升级微信群",
                    index = 910, readonly = @Readonly,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='4'"),
                    type = EditType.REFERENCE_TABLE, referenceTableType = @ReferenceTableType(type = ReferenceTableType.SelectShowTypeMTO.LIST)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    private WeixinGroup fourthWeixinGroup;

//    // ---四次升级配置
//    @FabosJsonField(
//            edit = @Edit(title = "三次升级配置", type = EditType.DIVIDE,
//                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='4'"))
//    )
//    @Transient
//    private String fifthDivide;

    /**
     * 四次升级间隔时间(分)
     */
    @FabosJsonField(
            views = @View(title = "四次升级间隔时间"),
            edit = @Edit(title = "四次升级间隔时间", notNull = true,
                    index = 911, readonly = @Readonly,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='5'"),
                    numberType = @NumberType(min = 1, max = 60000)),
            dynamicField = @DynamicField(passive = true)
    )
    private Long fifthIntervalTime;

    @FabosJsonField(
            views = @View(title = "四次升级推送方式"),
            edit = @Edit(title = "四次升级推送方式",
                    index = 912, readonly = @Readonly,
                    type = EditType.multiple_select,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber=='5'"),
                    choiceType = @ChoiceType(fetchHandler = MessageWayEnum.ChoiceFetch.class), search = @Search(vague = true)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    private String fifthWay;


    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "ecs_message_fifth_user",
            joinColumns = @JoinColumn(name = "notice_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "user_id", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)))
    @FabosJsonField(
            views = @View(title = "四次升级推送人员", column = "name", export = false, type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "四次升级推送人员",
                    readonly = @Readonly, search = @Search(vague = true),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber=='5'"),
                    filter = @Filter(value = "MetaUser.state = 'Y'"),
                    type = EditType.TAB_TABLE_REFER,
                    referenceTableType = @ReferenceTableType),
            dynamicField = @DynamicField(passive = true)
    )
    private List<MetaUser> fifthUsers;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "四次升级微信群", column = "name"),
            edit = @Edit(title = "四次升级微信群",
                    index = 913, readonly = @Readonly,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "upNumber>='5'"),
                    type = EditType.REFERENCE_TABLE, referenceTableType = @ReferenceTableType(type = ReferenceTableType.SelectShowTypeMTO.LIST)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    private WeixinGroup fifthWeixinGroup;


    /**
     * 备注
     */
    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(index = 9999, title = "备注", type = EditType.TEXTAREA,
                    inputType = @InputType(length = 400), formColumns = 2)
    )
    @Column(name = "LST_EVNT_CMNT")
    private String lastEventComment;


}
