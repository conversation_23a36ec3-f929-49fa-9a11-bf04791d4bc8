package cec.jiutian.bc.ecs.domain.message.handler;

import cec.jiutian.bc.ecs.domain.message.entity.Message;
import cec.jiutian.bc.ecs.domain.messagegroup.entity.MessageGroup;
import cec.jiutian.view.DependFiled;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


@Component
public class MessageCategoryChangeDynamicHandler implements DependFiled.DynamicHandler<Message> {


    @Override
    public Map<String, Object> handle(Message message) {
        Map<String, Object> result = new HashMap<>();
        MessageGroup messageGroup = new MessageGroup();
        messageGroup.setId("");
        result.put("messageGroup", messageGroup);
        return result;
    }
}
