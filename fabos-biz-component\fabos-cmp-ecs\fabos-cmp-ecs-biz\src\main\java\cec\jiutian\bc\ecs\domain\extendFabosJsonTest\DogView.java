//package cec.jiutian.bc.ecs.domain.extendFabosJsonTest;
//
//
//import cec.jiutian.core.view.fabosJson.FabosJson;
//import cec.jiutian.view.FabosJsonField;
//import cec.jiutian.view.expr.ExprBool;
//import cec.jiutian.view.field.Edit;
//import cec.jiutian.view.field.View;
//import cec.jiutian.view.field.edit.Search;
//import cec.jiutian.view.type.RowOperation;
//import jakarta.persistence.Entity;
//import jakarta.persistence.Table;
//import lombok.Getter;
//import lombok.Setter;
//import lombok.experimental.Accessors;
//
//@FabosJson(name = "狗"
//)
//@Setter
//@Getter
//@Entity
//@Accessors(chain = true)
//@Table(name = "Animal")
//public class DogView extends Animal{
//
//
//    @FabosJsonField(
//            views = @View(title = "颜色"),
//            edit = @Edit(title = "颜色", search = @Search(vague = true))
//    )
//    private String color;
//}
