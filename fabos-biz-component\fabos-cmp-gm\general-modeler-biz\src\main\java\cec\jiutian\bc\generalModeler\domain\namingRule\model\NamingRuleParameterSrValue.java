package cec.jiutian.bc.generalModeler.domain.namingRule.model;


import cec.jiutian.bc.generalModeler.domain.namingRule.proxy.NamingRuleParameterSrValueDataProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.config.Comment;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

@FabosJson(
        name = "编码规则序号值", desc = "记录某个命名规则下的某个SR类型参数，当前的依赖参数值对应的当前序号",
        dataProxy = NamingRuleParameterSrValueDataProxy.class
)
@Table(name = "naming_rule_parameter_sr_value",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"sr_parameter_id", "srParameterVersion"})
        }
)
@Entity
@Getter
@Setter
public class NamingRuleParameterSrValue extends MetaModel {

    //    关系字段，与关系表中的JoinColumn中指定的字段名对应
    @Comment("引用字段，对应namingruleParamter的ID")
    @FabosJsonField(
            views = @View(title = "规则参数", column = "parameterName"), //关联表中所需显示的字段名
            edit = @Edit(title = "规则参数", show = false, readonly = @Readonly(add = true, edit = true), search = @Search(vague = true),
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "parameterName")
            )
    )
    @ManyToOne
    private NamingRuleParameter srParameter;

    @FabosJsonField(
            views = @View(title = "参数名称"),
            edit = @Edit(title = "参数名称", show = false, readonly = @Readonly(add = true, edit = true), search = @Search(vague = true)
            )
    )
    private String srParameterName;

    @FabosJsonField(
            views = @View(title = "序号值版本"),
            edit = @Edit(title = "序号值版本", readonly = @Readonly(add = false, edit = true), notNull = true, search = @Search(vague = true)
            )
    )
    private String srParameterVersion;

    @FabosJsonField(
            views = @View(title = "序号值"),
            edit = @Edit(title = "序号值", type = EditType.NUMBER, numberType = @NumberType(min = 1)
            )
    )
    @Comment("序号值")
    private Integer srNumber;

    @Comment("四位年，与后面的日/月结合使用，用于版本的定时清理")
    private String yearText;

    @Comment("2位月")
    private String monthText;

    @Comment("2位日")
    private String dayText;


}
