package cec.jiutian.bc.urm.domain.dictionary.entity;

import cec.jiutian.bc.urm.domain.dictionary.event.DictionaryDataProxy;
import cec.jiutian.bc.urm.dto.MetaDict;
import cec.jiutian.core.frame.module.FabModule;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.constant.AnnotationConst;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


@Entity
@Table(name = "fd_dict", uniqueConstraints = @UniqueConstraint(columnNames = {"code", "oid"}))
@FabosJson(
        name = "数据字典",
        power = @Power(export = true),
        dataProxy = {DictionaryDataProxy.class},
        orderBy = "Dictionary.updateTime desc"
//        drills = @Drill(
//                title = "字典项",
//                linkFiled = "dictionary",
//                link = @Link(
//                        linkFabosJson = DictionaryItem.class, joinColumn = "dictionary.id"
//                ), show = @ExprBool(
//                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
//                params = "DictionaryItem"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
//        )
//        )
)
@Getter
@Setter
@TemplateType(type = "multiTable")
public class Dictionary extends MetaModel {

    @Column(length = AnnotationConst.CODE_LENGTH)
    @FabosJsonField(
            views = @View(title = "编码", sortable = true),
            edit = @Edit(title = "编码", notNull = true, readonly = @Readonly(add = false), search = @Search(vague = true))
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "名称", sortable = true),
            edit = @Edit(title = "名称", notNull = true, search = @Search(vague = true))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注", type = EditType.TEXTAREA)
    )
    private String remark;

//    @ManyToOne
//    @FabosJsonField(
//            views = @View(title = "所属组件", column = "name"),
//            edit = @Edit(title = "所属组件",
//                    filter = @Filter(value = "FabModule.state = 'activated'"),
//                    type = EditType.REFERENCE_TABLE,
//                    notNull = true)
//    )
//    private FabModule ownComponent;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "dict_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "字典项信息", type = EditType.TAB_TABLE_ADD),
            views = @View(title = "字典项信息", type = ViewType.TABLE_VIEW)
    )
    private List<DictionaryItem> dictionaryItems;

    public Dictionary(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public Dictionary() {
    }

    public static Dictionary fromMetaDict(MetaDict dict) {
        if (null == dict) return null;
        Dictionary dictionary = new Dictionary(dict.getCode(),
                dict.getName());
        dictionary.setId(dict.getId());
        return dictionary;
    }
}
