package cec.jiutian.bc;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.boot.SpringApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.core.io.support.PropertiesLoaderUtils;

import java.io.IOException;
import java.util.Properties;

/**
 * <AUTHOR> @date
 */
@Slf4j
@EnableDiscoveryClient    // 启用Nacos服务发现
@EnableFeignClients       // 启用Feign客户端
public class CmpRemoteCallLogApp {
    private static final Logger LOGGER = LoggerFactory.getLogger(CmpRemoteCallLogApp.class);

    public static void main(String[] args) throws IOException {
        try {
            Properties properties = PropertiesLoaderUtils.loadAllProperties("application.properties");
            properties.forEach((k, v) -> MDC.put(k.toString(), v.toString()));
            ClassLoader systemClassLoader = ClassLoader.getSystemClassLoader();
            ClassLoader classLoader = CmpRemoteCallLogApp.class.getClassLoader();
            System.out.println(classLoader.equals(systemClassLoader));
            System.out.println("类加载器名称:" + classLoader.getName());
            SpringApplication.run(CmpRemoteCallLogApp.class, args);

            LOGGER.info("RemoteCallLogApplication start!");
            LOGGER.info("Spring Boot Version: "
                    + SpringApplication.class.getPackage().getImplementationVersion());
            LOGGER.info("BaseApplication classLoader: " + CmpRemoteCallLogApp.class.getClassLoader());
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }
}
