package cec.jiutian.bc.generalModeler.domain.test.model;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceAddType;
import cec.jiutian.view.config.Comment;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;


@FabosJson(
        name = "组合模型p1",
        rowOperation = @RowOperation(
                code = "SplitModelP1",
                title = "p1创建",
                mode = RowOperation.Mode.BUTTON,
                fabosJsonClass = SplitModelP1.class, //点击按钮时弹出的表单定义
                operationHandler = SplitModelp1Handler.class //按钮处理类
        )
)
@Table(name = "split_model_full"
)
@Entity
@Getter
@Setter
public class SplitModelFull extends MetaModel {
    @FabosJsonField(
            views = @View(title = "编码"),
            edit = @Edit(title = "编码", readonly = @Readonly(add = false, edit = true), notNull = true, search = @Search(vague = true))
    )
    private String namingRuleCode;

    @FabosJsonField(
            views = @View(title = "显示名称"),
            edit = @Edit(title = "显示名称", notNull = true, search = @Search(vague = true))
    )
    private String displayName;

    @FabosJsonField(
            views = @View(title = "时间日期"),
            edit = @Edit(title = "时间日期", notNull = true, search = @Search(vague = true), type = EditType.DATE, dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date datetime;

    @FabosJsonField(
            edit = @Edit(title = "数量", notNull = false,
                    type = EditType.NUMBER, search = @Search(vague = true))
    )
    private Integer qty;

    @FabosJsonField(
            edit = @Edit(title = "说明", notNull = false,
                    type = EditType.TEXTAREA)
    )
    private String description;

    @ManyToMany  //多对多
    @JoinTable(
            name = "e_source_target", // 中间表表名，如下为中间表的定义
            joinColumns = @JoinColumn(name = "target_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "source_id", referencedColumnName = "id"))
    @FabosJsonField(
            edit = @Edit(
                    title = "多对多，关联多条数据",
                    type = EditType.TAB_TABLE_REFER
            )
    )
    @Comment("将SplitModelChild作为source, 进行多对多中间表关联")
    private List<SplitModelChild> source;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = false)
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "引用添加的子项明细", type = EditType.TAB_REFER_ADD),
            referenceAddType = @ReferenceAddType(referenceClass = "SplitModelDto", editable = {"qty"}, //前端将filter拼接到查询condition中，需使用json格式
                    referenceAddHandler = SplitModelp3ReferAddHandler.class),
            views = @View(title = "引用添加的子项明细", type = ViewType.TABLE_VIEW, extraPK = "extraId") // extraPK：自身子模型用来记录外部模型ID字段值的字段.在handler方法中，需要将值存入
    )
    private List<SplitModelP3> splitModelP3List;


}
