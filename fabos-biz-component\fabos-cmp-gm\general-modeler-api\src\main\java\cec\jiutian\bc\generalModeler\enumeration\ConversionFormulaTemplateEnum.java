package cec.jiutian.bc.generalModeler.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2024/11/1 16:34
 * @description：
 */
public class ConversionFormulaTemplateEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (ConversionFormulaTemplateEnum.Enum data : ConversionFormulaTemplateEnum.Enum.values()) {
            list.add(new VLModel(data.name(), data.getValue()));
        }
        return list;
    }

    @AllArgsConstructor
    @Getter
    public enum Enum {
        OneVariableMultiplication("主计量单位*转换系数"),
        OneVariableDivision("主计量单位/转换系数"),
        TwoVariableMultiplication("主计量单位*转换系数+修正值"),
        TwoVariableDivision("主计量单位/转换系数+修正值");

        private final String value;

    }
}
