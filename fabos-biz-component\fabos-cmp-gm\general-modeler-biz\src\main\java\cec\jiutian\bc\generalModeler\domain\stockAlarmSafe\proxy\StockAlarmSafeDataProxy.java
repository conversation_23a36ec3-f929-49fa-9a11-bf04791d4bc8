package cec.jiutian.bc.generalModeler.domain.stockAlarmSafe.proxy;

import cec.jiutian.bc.generalModeler.domain.stockAlarmSafe.model.StockAlarmSafe;
import cec.jiutian.bc.generalModeler.enumeration.ConfigControlTypeEnum;
import cec.jiutian.core.frame.exception.FabosJsonWebApiRuntimeException;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class StockAlarmSafeDataProxy implements DataProxy<StockAlarmSafe> {

    @Resource
    private JpaCrud jpaCrud;

    @Override
    public void beforeAdd(StockAlarmSafe stockAlarmSafe) {
        checkStockAlarmSafe(stockAlarmSafe);
    }

    @Override
    public void beforeUpdate(StockAlarmSafe stockAlarmSafe) {
        checkStockAlarmSafe(stockAlarmSafe);

    }

    private void checkStockAlarmSafe(StockAlarmSafe stockAlarmSafe) {
        if (ConfigControlTypeEnum.Enum.Both.name().equals(stockAlarmSafe.getConfigControlType())) {
            if (Double.doubleToLongBits(stockAlarmSafe.getConfigHighValue()) < Double.doubleToLongBits(stockAlarmSafe.getConfigLowValue())) {
                throw new FabosJsonWebApiRuntimeException("库存上限值需大于库存下限值");
            }
        }
        if (ConfigControlTypeEnum.Enum.UpperLimit.name().equals(stockAlarmSafe.getConfigControlType())) {
            stockAlarmSafe.setConfigLowValue(null);
        }
        if (ConfigControlTypeEnum.Enum.LowLimit.name().equals(stockAlarmSafe.getConfigControlType())) {
            stockAlarmSafe.setConfigHighValue(null);
        }
    }

}
