package cec.jiutian.bc.urm.domain.role.entity;

import cec.jiutian.bc.urm.domain.menu.entity.Menu;
import cec.jiutian.bc.urm.domain.role.event.RoleDataProxy;
import cec.jiutian.bc.urm.domain.user.entity.UserByRoleView;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.SubTableField;
import cec.jiutian.view.constant.AnnotationConst;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.BoolType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import jakarta.persistence.Column;
import jakarta.persistence.ConstraintMode;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Entity
@Table(name = "fd_role")
@Getter
@Setter
@FabosJson(name = "角色管理", dataProxy = {RoleDataProxy.class},
        orderBy = "Role.sort asc, Role.createTime desc"
)
@FabosJsonI18n
public class Role extends MetaModel {

    public static final String CODE = "code";

    @Column(length = AnnotationConst.CODE_LENGTH)
    @FabosJsonField(
            views = @View(title = "编码"),
            edit = @Edit(title = "编码", notNull = true, search = @Search(vague = true))
    )
    @SubTableField
    private String code;

    @FabosJsonField(
            views = @View(title = "名称", toolTip = true),
            edit = @Edit(title = "名称", notNull = true, search = @Search(vague = true))
    )
    @SubTableField
    private String name;

    @FabosJsonField(
            views = @View(title = "角色说明", toolTip = true),
            edit = @Edit(title = "角色说明")
    )
    @SubTableField
    private String description;

    @FabosJsonField(
            views = @View(title = "显示顺序", sortable = true),
            edit = @Edit(title = "显示顺序", notNull = true, numberType = @NumberType(min = 1, max = 9999))
    )
    private Integer sort;

    @FabosJsonField(
            views = @View(title = "状态", sortable = true),
            edit = @Edit(
                    title = "状态",
                    type = EditType.BOOLEAN,
                    notNull = true,
                    defaultVal = "true",
                    search = @Search(vague = true, defaultVal = "true"),
                    boolType = @BoolType(trueText = "有效", falseText = "失效")
            )
    )
    private Boolean status = true;

    @JoinTable(
            name = "fd_role_menu",
            joinColumns = @JoinColumn(name = "role_id", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)),
            inverseJoinColumns = @JoinColumn(name = "menu_id", referencedColumnName = "id"))
    @FabosJsonField(
            views = @View(title = "菜单权限",show = true, export = false),
            edit = @Edit(
                    readonly = @Readonly(add = false, edit = false),
                    show= false,
                    title = "菜单权限",
                    filter = @Filter(value = "Menu.status in ('1','2')"),
                    type = EditType.TAB_TREE
            )
    )
    @ManyToMany(fetch = FetchType.EAGER)
    private List<Menu> menus;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "fd_user_role",
            joinColumns = @JoinColumn(name = "role_id", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)),
            inverseJoinColumns = @JoinColumn(name = "user_id", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    )
    @FabosJsonField(
            views = @View(title = "用户列表",show = true, export = false),
            edit = @Edit(
                    readonly = @Readonly(add = false, edit = false),
                    show = false,
                    title = "用户列表",
                    type = EditType.TAB_TABLE_REFER,
                    referenceTableType = @ReferenceTableType
            )
    )
    private List<UserByRoleView> users;

    // 为兼容自增主键的外部系统，额外增加一个自增列
    @Column(unique = true)
    private Integer autoIncrementId;

}
