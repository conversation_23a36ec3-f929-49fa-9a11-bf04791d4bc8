package cec.jiutian.bc.urm.inbound.local.context;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @time 2025-06-23 16:20
 */

@Slf4j
public class BackgroundTaskTraceContext {

    private static final ThreadLocal<String> TRACE_ID_LOCAL = new InheritableThreadLocal<>();

    public static void setTraceId(String traceId) {
        TRACE_ID_LOCAL.set(traceId);
    }

    public static String getTraceId() {
        String traceId = TRACE_ID_LOCAL.get();
        if (traceId == null) {
            log.warn("Background Task traceId is null");
            return null;
        }
        return traceId;
    }

    public static void clear() {
        TRACE_ID_LOCAL.remove();
    }
}
