package cec.jiutian.bc.job.domain.job.entity;


import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.QueryModel;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Id;

@FabosJson(
        name = "定时任务方法清单")
@QueryModel(hql = "select new map (" +
        "mf.id as id, mf.displayName as displayName, mf.method_package_name as methodCode, mf.modelCode as modelCode) " +
        "from MetadataFunction as mf where sourceType = '04'")
public class JobMethods {
    @FabosJsonField(
            views = @View(title = "id", show = false),
            edit = @Edit(title = "id", show = false),
            customHqlField = "id"
    )
    @Id
    private String id;

    @FabosJsonField(
            views = @View(title = "方法名称"),
            edit = @Edit(title = "方法名称", search = @Search(vague = true)
            ),
            customHqlField = "JobMethods.displayName"

    )
    private String displayName;

    @FabosJsonField(
            views = @View(title = "方法编码"),
            edit = @Edit(title = "方法编码", search = @Search(vague = true)
            ),
            customHqlField = "JobMethods.methodCode"

    )
    private String methodCode;

    @FabosJsonField(
            views = @View(title = "模型编码"),
            edit = @Edit(title = "模型编码", search = @Search(vague = true)
            ),
            customHqlField = "JobMethods.modelCode"

    )
    private String modelCode;
}
