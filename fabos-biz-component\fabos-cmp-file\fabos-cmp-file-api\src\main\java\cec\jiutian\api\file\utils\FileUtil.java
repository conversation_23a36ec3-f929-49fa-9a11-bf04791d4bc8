package cec.jiutian.api.file.utils;

import cec.jiutian.api.file.model.FabosJsonFileSuper;
import cec.jiutian.core.frame.exception.FabosJsonWebApiRuntimeException;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Cleanup;
import org.springframework.http.HttpStatus;
import org.springframework.util.StreamUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @time 2025-04-30 13:47
 */

public class FileUtil {
    public static final String UNDERLINE = "_";
    public static final String FS_SEP = "/";
    public static String processFileRenaming(FabosJsonFileSuper FabosJsonFileSuper) {
        String originalFilename = FabosJsonFileSuper.getOriginalFileName();
        if (originalFilename == null) {
            throw new FabosJsonWebApiRuntimeException("文件名不能为空");
        }

        // 拼接唯一后缀（防重复）
        String postfix = UNDERLINE + FabosJsonFileSuper.getFileUUID();

        // 获取文件扩展名（如有）
        String extension = "";
        int lastDotIndex = originalFilename.lastIndexOf(".");

        // 检查文件名是否以点开头或结尾
        boolean startsWithDot = originalFilename.startsWith(".");
        boolean endsWithDot = originalFilename.endsWith(".");

        if (lastDotIndex > 0) { // 确保有有效的扩展名
            extension = originalFilename.substring(lastDotIndex); // 包含点的扩展名
        }

        // 构造新的文件名
        String modifiedFilename;
        if (!extension.isEmpty()) {
            modifiedFilename = originalFilename.substring(0, lastDotIndex) + postfix + extension;
        } else {
            modifiedFilename = originalFilename + postfix; // 没有扩展名的情况
        }

        // 处理以点开头或结尾的情况
        if (startsWithDot && !modifiedFilename.startsWith(".")) {
            modifiedFilename = "." + modifiedFilename; // 确保以点开头
        }
        if (endsWithDot && !modifiedFilename.endsWith(".")) {
            modifiedFilename += "."; // 确保以点结尾
        }
        FabosJsonFileSuper.setFilenameExtension(extension);
        modifiedFilename = modifiedFilename.replaceAll("}|\\{|]|\\[|\\n|\"|,|'|&|#|\\?|\\s", "_");
        FabosJsonFileSuper.setModifiedFileName(modifiedFilename);
        return modifiedFilename;
    }

    public static void setOutputStream(HttpServletResponse response, File file, String originalFileName) throws IOException {
        @Cleanup InputStream inputStream = new FileInputStream(file);
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(originalFileName, StandardCharsets.UTF_8).replaceAll("\\+", "%20"));
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        response.getOutputStream().write(StreamUtils.copyToByteArray(inputStream));
    }

    public static void setOutputStream(HttpServletResponse response, byte[] file, String originalFileName) throws IOException {
        // 替换+为%20是因为：URLEncoder.encode会将空格转换为+，而浏览器识别+仍为+而并非空格，手工替换为%20，浏览器会识别为空格
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(originalFileName, StandardCharsets.UTF_8).replaceAll("\\+", "%20"));
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        response.getOutputStream().write(file);
    }

    public static void set404Status(HttpServletResponse response) throws IOException {
        response.setStatus(HttpStatus.NOT_FOUND.value());
        response.sendError(HttpStatus.NOT_FOUND.value());
    }

    public static void set403Status(HttpServletResponse response) throws IOException {
        response.setStatus(HttpStatus.FORBIDDEN.value());
        response.sendError(HttpStatus.FORBIDDEN.value());
    }

}
