package cec.jiutian.bc.generalModeler.service;

import cec.jiutian.bc.generalModeler.domain.measureUnit.service.UnitConversionRuleDomainService;
import cec.jiutian.view.config.Comment;
import jakarta.annotation.Resource;
import jakarta.transaction.Transactional;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2024/10/25 10:24
 * @description：
 */
@Service
public class UnitConversionRuleService {
    @Resource
    private UnitConversionRuleDomainService unitConversionRuleDomainService;

    @Transactional
    @Comment("获取换算规则试算结果")
    public String calculateResult(Map<String, Object> map) {
        return unitConversionRuleDomainService.calculateResult(String.valueOf(map.get("ruleId")), map.get("param"));
    }
}
