package cec.jiutian.bc.urm.domain.ModelColumnPermission.entity;

import cec.jiutian.bc.urm.domain.ModelColumnPermission.event.ModelColumnDataProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.meta.PermissionLevelEnum;
import cec.jiutian.meta.model.MetadataModelField;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.SubTableField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "model_column_permissions")
@Getter
@Setter
@FabosJson(
        dataProxy = ModelColumnDataProxy.class,
        name = "模型列权限配置",
        orderBy = "ModelColumnPermission.createTime desc"
)
public class ModelColumnPermission extends MetaModel {

    @FabosJsonField(
            views = @View(title = "元模型字段",show = false,column = "displayName"),
            edit = @Edit(title = "元模型字段",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(),
                    filter = @Filter(value = "MetadataModelField.required = false and MetadataModelField.primaryKey = false")
            )
    )
    @Transient
    private MetadataModelField field;

    @FabosJsonField(
            views = @View(title = "角色ID", show = false, toolTip = true),
            edit = @Edit(title = "角色ID", show = false, search = @Search(vague = true))
    )
    private String roleId;

    @FabosJsonField(
            views = @View(title = "字段ID",show = false, toolTip = true),
            edit = @Edit(title = "字段ID", show = false, search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "field",beFilledBy = "id"))
    )
    private String fieldId;

    @FabosJsonField(
            views = @View(title = "元数据ID", show = false, toolTip = true),
            edit = @Edit(title = "元数据ID", show = false, search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "field",beFilledBy = "id"))
    )
    private String metadataId;

    @FabosJsonField(
            views = @View(title = "元模型显示名称",show = false, toolTip = true),
            edit = @Edit(title = "元模型显示名称", show = false, search = @Search(vague = true))
  )
    @SubTableField
    private String modelDisplayName;

    @FabosJsonField(
            views = @View(title = "字段显示名称", toolTip = true),
            edit = @Edit(title = "字段显示名称", search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "field",beFilledBy = "displayName"))
    )
    @SubTableField
    private String filedDisplayName;

    @FabosJsonField(
            views = @View(title = "权限级别", toolTip = true),
            edit = @Edit(title = "权限级别", notNull = true, search = @Search,
                    type = EditType.CHOICE,
                    defaultVal = "1",
                    choiceType = @ChoiceType(fetchHandler = {PermissionLevelEnum.ChoiceFetch.class})
            )
    )
    @SubTableField
    private Integer permissionLevel;

}
