package cec.jiutian.bc.urm.domain.openApi.event;

import cec.jiutian.bc.urm.domain.openApi.entity.FabosJsonOpenApi;
import cec.jiutian.bc.urm.domain.openApi.entity.FabosJsonOpenApiResources;
import cec.jiutian.bc.urm.inbound.local.service.command.OpenApiService;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.frame.proxy.LambdaSee;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2025-01-13 16:35
 */

@Component
public class FabosJsonOpenApiDataProxy implements DataProxy<FabosJsonOpenApi>, OperationHandler<FabosJsonOpenApi, Void> {

    public static final String REQUEST_NEW_SECRET = "REQUEST_NEW_SECRET";
    public static final String DISPLAY_SECRET = "DISPLAY_SECRET";
    public static final String REQUEST_NEW_TOKEN = "REQUEST_NEW_TOKEN";
    public static final String DISPLAY_TOKEN = "DISPLAY_TOKEN";


    @Resource
    private OpenApiService openApiService;

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeAdd(FabosJsonOpenApi fabosJsonOpenApi) {
        for (FabosJsonOpenApiResources resource : fabosJsonOpenApi.getAvailableResources()) {
            if (!resource.getName().startsWith("/")) {
                throw new FabosJsonApiErrorTip("接口路径需要以\"/\"开头");
            }
        }
        fabosJsonOpenApi.setAppid(RandomStringUtils.randomAlphanumeric(16).toLowerCase());
        fabosJsonOpenApi.setSecret(RandomStringUtils.randomAlphanumeric(24).toUpperCase());
    }

    @Override
    public void beforeUpdate(FabosJsonOpenApi fabosJsonOpenApi) {
        for (FabosJsonOpenApiResources resource : fabosJsonOpenApi.getAvailableResources()) {
            if (!resource.getName().startsWith("/")) {
                throw new FabosJsonApiErrorTip("接口路径需要以\"/\"开头");
            }
        }
    }

    @Override
    public void afterUpdate(FabosJsonOpenApi fabosJsonOpenApi) {
        if (!fabosJsonOpenApi.getStatus()) {
            // 如果更新了状态，则移掉token
            openApiService.kickOutToken(fabosJsonOpenApi);
            fabosJsonOpenApi.setCurrentToken(null);
        } else {
            // 不好判断是否更新了子表，做强行覆盖
            FabosJsonOpenApi retrieved = fabosJsonDao.findById(FabosJsonOpenApi.class, fabosJsonOpenApi.getId());
            openApiService.createToken(retrieved.getCurrentToken(), fabosJsonOpenApi.getAvailableResources(), openApiService.getExpireTimeSeconds(retrieved.getCurrentToken()));
        }
    }

    @Override
    public void afterDelete(FabosJsonOpenApi fabosJsonOpenApi) {
        openApiService.kickOutToken(fabosJsonOpenApi);
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        String secretField = LambdaSee.field(FabosJsonOpenApi::getSecret);
        for (Map<String, Object> map : list) {
            String secret = map.get(secretField).toString();
            map.put(secretField, FabosJsonOpenApi.maskSecret(secret));
        }
    }

    @Override
    @Transactional
    public String exec(List<FabosJsonOpenApi> data, Void unused, String... param) {
        if (param.length == 1) {
            String hint = null;
            FabosJsonOpenApi fabosJsonOpenApi = fabosJsonDao.findById(FabosJsonOpenApi.class, data.get(0).getId());
            if (Objects.isNull(fabosJsonOpenApi)) {
                throw new FabosJsonApiErrorTip("数据不存在, 请检查");
            }
            switch (param[0]) {
                case REQUEST_NEW_SECRET -> {
                    openApiService.kickOutToken(fabosJsonOpenApi);
                    fabosJsonOpenApi.setSecret(RandomStringUtils.randomAlphanumeric(24).toUpperCase());
                    hint = "已生成新的密钥, 请妥善保管: " + fabosJsonOpenApi.getSecret();
                }
                case DISPLAY_SECRET -> {
                    hint = "当前密钥, 请妥善保管: " + fabosJsonOpenApi.getSecret();
                }
                case REQUEST_NEW_TOKEN -> {
                    String expire = openApiService.requestAndSetToken(fabosJsonOpenApi);
                    if (fabosJsonOpenApi.getExpire() <= 0) {
                        hint = String.format("已生成新的长期有效Token, 请妥善保管: [%s]。", fabosJsonOpenApi.getCurrentToken());
                    } else {
                        hint = String.format("已生成新的Token: [%s], 请妥善保管, 将于%s失效。", fabosJsonOpenApi.getCurrentToken(), expire);

                    }
                }
                case DISPLAY_TOKEN -> {
                    String currentToken = fabosJsonOpenApi.getCurrentToken();
                    if (StringUtils.isBlank(currentToken)) {
                        hint = "当前还没有有效Token。";
                    } else {
                        String expireTime = openApiService.getFormattedOnlineTokenExpireTime(currentToken);
                        switch (expireTime) {
                            case "-2" -> {
                                hint = String.format("当前的Token为: [%s], 但是已失效, 请重新生成。", currentToken);
                            }
                            case "-1" -> {
                                hint = String.format("当前的Token为长期有效Token, 请妥善保管: [%s]。", currentToken);
                            }
                            default -> {
                                hint = String.format("当前的Token为：[%s], 请妥善保管, 将于%s失效。"
                                        , currentToken
                                        , expireTime
                                );
                            }
                        }
                    }
                }
            }
            return String.format("alert(%s)", hint);
        }
        return null;
    }

}
