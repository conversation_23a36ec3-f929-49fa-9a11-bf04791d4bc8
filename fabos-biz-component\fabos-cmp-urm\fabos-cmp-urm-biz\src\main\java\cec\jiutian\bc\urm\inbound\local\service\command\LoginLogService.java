package cec.jiutian.bc.urm.inbound.local.service.command;

import cec.jiutian.bc.urm.domain.dictionary.entity.Dictionary;
import cec.jiutian.core.frame.annotation.FabosCustomizedService;
import cec.jiutian.fc.log.domain.loginLog.normal.entity.LoginLog;
import cec.jiutian.view.FabosJsonFunction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @time 2024-10-27 19:16
 */

@FabosCustomizedService(value = Dictionary.class)
@Service
@Slf4j
public class LoginLogService {

    @FabosJsonFunction(displayName = "记录登录日志",
            description = "描述：记录登录日志",
            returnTypeDescription = "返回值描述",
            argumentsDescription = "参数描述")
    public Date recordLoginLog(Dictionary d){
        log.info(d.getCode());
        return new Date();
    }


}
