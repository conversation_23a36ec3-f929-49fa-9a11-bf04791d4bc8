package cec.jiutian.api.remoteCallLog.dto;

import cec.jiutian.api.remoteCallLog.model.RemoteCallLog;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class RemoteCallLogRetryDto {
    // 业务数据的主键
    private RemoteCallLog remoteCallLog;

    // 是否成功 （重试成功也更新该标记）
    private Boolean successFlag;

    // 重试时间 (最近一次)
    private Date retryTime;

    // 重试响应消息体（最近一次）
    private String retryResponseBody;
}
