package cec.jiutian.bc.lcp.outbound.adapter.client;

import cec.jiutian.bc.urm.dto.MenuDTO;
import cec.jiutian.bc.urm.provider.IMenuProvider;
import com.alipay.sofa.koupleless.common.api.AutowiredFromBiz;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MenuClient {


    @AutowiredFromBiz(bizName = "fabos-cmp-urm", bizVersion = "3.2.2-SNAPSHOT")
    private IMenuProvider menuProvider;

    public String saveOrUpdateLowCodeMenu(MenuDTO dto) {
        return menuProvider.saveOrUpdateLowCodeMenu(dto);
    }
    public Boolean saveOrUpdateLowCodeButton(List<MenuDTO> dtos,String parentId) {
        return menuProvider.saveOrUpdateLowCodeButton(dtos,parentId);
    }
}
