package cec.jiutian.bc.generalModeler.domain.namingRule.service;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRule;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleParameter;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.config.Comment;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class NamingRuleParameterDomainService {
    @Resource
    private FabosJsonDao FabosJsonDao;

    @Comment("根据AK获取行")
    public NamingRuleParameter getParameterByAk(@Comment("命名规则") NamingRule namingRule, @Comment("参数名") String parameterName) {
        CriteriaBuilder criteriaBuilder = FabosJsonDao.getEntityManager().getCriteriaBuilder();
        CriteriaQuery<NamingRuleParameter> criteriaQuery = criteriaBuilder.createQuery(NamingRuleParameter.class);
        Root<NamingRuleParameter> namingRuleParameterRoot = criteriaQuery.from(NamingRuleParameter.class);
        criteriaQuery.where(
                criteriaBuilder.and(
                        criteriaBuilder.equal(namingRuleParameterRoot.get("namingRule"), namingRule),
                        criteriaBuilder.equal(namingRuleParameterRoot.get("parameterName"), parameterName)
                )
        );
        List<NamingRuleParameter> results = FabosJsonDao.getEntityManager().createQuery(criteriaQuery).getResultList();
        return results.size() > 0 ? results.get(0) : null;
    }
}
