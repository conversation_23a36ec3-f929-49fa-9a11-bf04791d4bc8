package cec.jiutian.bc.ecs.domain.userMessageRecord.event;

import cec.jiutian.bc.ecs.domain.userMessageRecord.entity.UserMessageRecord;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.query.Condition;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
public class UserMessageRecordDataProxy implements DataProxy<UserMessageRecord> {

    @Override
    public String beforeFetch(List<Condition> conditions) {
        String phoneNumber = UserContext.getPhoneNumber();
        if (StringUtils.isBlank(phoneNumber)) {
            throw new FabosJsonApiErrorTip("用户未登录");
        }
        return "UserMessageRecord.dispatchUser = '" + phoneNumber + "'";
    }
}
