package cec.jiutian.bc.generalModeler;

import cec.jiutian.core.frame.annotation.FabosJsonScan;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.core.io.support.PropertiesLoaderUtils;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.io.IOException;
import java.util.Properties;

/**
 * <AUTHOR>
 * @time 2024-10-21 15:55
 */

@Slf4j
@SpringBootApplication(scanBasePackages = {
        "cec.jiutian"
})
@ComponentScan(basePackages = {
        "cec.jiutian"
})
@FabosJsonScan(value = {
        "cec.jiutian"
})
//@EnableJpaRepositories(basePackages = "cec.jiutian")
@EntityScan(basePackages = {
        "cec.jiutian"
})
// todo 2024-10-21 需要确认是用根目录（性能差）还是指定目录（容易遗漏）
@EnableScheduling
//@EnableDubbo
public class GeneralModeler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GeneralModeler.class);

    public static void main(String[] args) throws IOException {
        Properties properties = PropertiesLoaderUtils.loadAllProperties("application.properties");
        properties.forEach((k, v) -> MDC.put(k.toString(), v.toString()));
        ClassLoader systemClassLoader = ClassLoader.getSystemClassLoader();
        ClassLoader classLoader = GeneralModeler.class.getClassLoader();
        System.out.println(classLoader.equals(systemClassLoader));
        System.out.println("类加载器名称:" + classLoader.getName());
        SpringApplication.run(GeneralModeler.class, args);

        LOGGER.info("BaseApplication start!");
        LOGGER.info("Spring Boot Version: "
                + SpringApplication.class.getPackage().getImplementationVersion());
        LOGGER.info("BaseApplication classLoader: " + GeneralModeler.class.getClassLoader());
    }


}
