package cec.jiutian.bc.infra.utils;

/**
 * <AUTHOR>
 * @time 2025-03-14 17:19
 */

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

import java.security.KeyFactory;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.RSAPublicKeySpec;
import java.util.Base64;

public class JwkUtils {

    public static RSAPublicKey parseJwkToPublicKey(String n, String e) {
        // 将Base64URL编码的n和e解码为字节数组
        byte[] modulusBytes = Base64.getUrlDecoder().decode(n);
        byte[] exponentBytes = Base64.getUrlDecoder().decode(e);

        // 构造RSA公钥规格
        RSAPublicKeySpec spec = new RSAPublicKeySpec(
                new java.math.BigInteger(1, modulusBytes),  // 模数（n）
                new java.math.BigInteger(1, exponentBytes)  // 指数（e）
        );

        // 生成公钥对象
        try {
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            return (RSAPublicKey) keyFactory.generatePublic(spec);
        } catch (Exception ex) {
            throw new RuntimeException("解析公钥失败", ex);
        }
    }

    public static RSAPublicKey parseJwkJsonToPublicKey(String jwk) {
        JSONObject jwkObj = JSON.parseObject(jwk).getJSONArray("keys").getJSONObject(0);
        return JwkUtils.parseJwkToPublicKey(
                jwkObj.getString("n"),
                jwkObj.getString("e")
        );
    }
}