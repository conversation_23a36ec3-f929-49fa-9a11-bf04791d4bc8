package cec.jiutian.bc.ecs.domain.message.repository;

import cec.jiutian.bc.ecs.domain.message.entity.Message;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface MsgRepository extends JpaRepository<Message, String> {
    @Query(value = """
            FROM Message
            where isProcess='Y' and processCompleteFlag='N' and currentUpNumber < upNumber and nextUpgradeTime<=?1 order by createTime desc
            """)
    List<Message> findToUpgradeMessage(LocalDateTime nextUpgradeTime);
}
