//package cec.jiutian.bc.infra.upgrade;
//
//import cec.jiutian.bc.ecs.domain.message.entity.Message;
//import cec.jiutian.bc.ecs.domain.message.repository.MsgRepository;
//import cec.jiutian.bc.ecs.domain.message.service.MessageService;
//import cec.jiutian.bc.ecs.domain.upgrade.entity.MessageUpgrade;
//import cec.jiutian.bc.ecs.domain.upgrade.repository.MsgUpgradeRepository;
//import cec.jiutian.core.frame.constant.YesOrNoStatus;
//import jakarta.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//import java.util.Date;
//import java.util.List;
//import java.util.Optional;
//
//@Slf4j
//@Component
//public class UpgradeSchedule {
//
//
//    @Resource
//    private MsgUpgradeRepository msgUpgradeRepository;
//
//    @Resource
//    private MsgRepository msgRepository;
//
//    @Resource
//    private MessageService messageService;
//
//    @Scheduled(fixedRate = 60000) // 每隔60s执行一次
//    public void upgrade(){
//        List<MessageUpgrade> all = msgUpgradeRepository.findAll();
//        log.info("定时任务启动：{}",all.size());
//        if (CollectionUtils.isEmpty(all)) {
//            return;
//        }
//        Date now = new Date();
//        for (MessageUpgrade messageUpgrade : all) {
//            upgradeMessage(messageUpgrade,now);
//        }
//    }
//
//    private void upgradeMessage(MessageUpgrade messageUpgrade,Date now ){
//        Optional<Message> msgOpt = msgRepository.findById(messageUpgrade.getMessageId());
//        Message message = msgOpt.get();
//        long upNumber = message.getUpNumber() == null ? 0L : message.getUpNumber();
//        if (upNumber <= 0) {
//            return;
//        }
//        int currentUpNumber = message.getCurrentUpNumber() == null ? 0 : message.getCurrentUpNumber();
//        currentUpNumber++;
//        if (currentUpNumber > upNumber) {
//            return;
//        }
//        Date lastUpDate = message.getLastUpDate();
//        Long upIntervalMinute = message.getUpIntervalMinute();
//        // 计算now和lastUpDate的差值
//        if (lastUpDate == null || upIntervalMinute == null || upIntervalMinute <= 0) {
//            return;
//        }
//        if (now.getTime() - lastUpDate.getTime() < upIntervalMinute * 60 * 1000) {
//            return;
//        }
//        message.setLastUpDate(now);
//        message.setCurrentUpNumber(currentUpNumber);
//        message.setUpgraded(YesOrNoStatus.YES.getValue());
//        messageService.sendMessage(message);
//        msgRepository.save(message);
//        if (currentUpNumber >= upNumber) {
//            msgUpgradeRepository.delete(messageUpgrade);
//        }
//    }
//
//}
