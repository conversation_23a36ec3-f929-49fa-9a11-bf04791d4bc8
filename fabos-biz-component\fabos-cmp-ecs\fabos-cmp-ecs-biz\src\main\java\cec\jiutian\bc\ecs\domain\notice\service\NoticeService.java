//package cec.jiutian.bc.ecs.domain.notice.service;
//
//import cec.jiutian.bc.ecs.domain.messagerecord.entity.MessageRecord;
//import cec.jiutian.bc.ecs.domain.messagerecord.service.MessageRecordService;
//import cec.jiutian.bc.ecs.dto.PushUserDTO;
//import cec.jiutian.common.util.BeanUtils;
//import cec.jiutian.common.util.StringUtils;
//import cec.jiutian.bc.ecs.domain.notice.entity.Notice;
//import cec.jiutian.bc.ecs.domain.notice.event.NoticeDataProxy;
//import cec.jiutian.bc.ecs.dto.MessageRecordCreateDTO;
//import cec.jiutian.bc.ecs.dto.NoticeCreateDTO;
//import cec.jiutian.bc.infra.handler.MessageHandler;
//import cec.jiutian.bc.infra.handler.MessageHandlerFactory;
//import cec.jiutian.core.frame.constant.YesOrNoStatus;
//import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.util.CollectionUtils;
//
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.List;
//
//@Slf4j
//@Service
//public class NoticeService {
//
//    private final NoticeDataProxy noticeDataProxy;
//
//    private final FabosJsonDao fabosJsonDao;
//
//    private final MessageRecordService messageRecordService;
//
//    private final MessageHandlerFactory messageHandlerFactory;
//
//    public NoticeService(NoticeDataProxy noticeDataProxy, FabosJsonDao fabosJsonDao,
//                         MessageRecordService messageRecordService, MessageHandlerFactory messageHandlerFactory) {
//        this.noticeDataProxy = noticeDataProxy;
//        this.fabosJsonDao = fabosJsonDao;
//        this.messageRecordService = messageRecordService;
//        this.messageHandlerFactory = messageHandlerFactory;
//    }
//
//    @Transactional
//    public boolean sendMsg(NoticeCreateDTO noticeCreateDTO){
//        if (!validator(noticeCreateDTO)){
//            return false;
//        }
//
//        Notice notice = new Notice();
//        BeanUtils.copyProperties(noticeCreateDTO, notice);
//        noticeDataProxy.beforeAdd(notice);
//        fabosJsonDao.persistAndFlush(notice);
//        return true;
//    }
//
//    private Boolean validator(NoticeCreateDTO noticeCreateDTO) {
//        if (noticeCreateDTO == null) {
//            log.error("发送对象不能为空");
//            return false;
//        }
//        if (StringUtils.isEmpty(noticeCreateDTO.getTitle())) {
//            log.error("标题不能为空");
//            return false;
//        }
//        if (StringUtils.isEmpty(noticeCreateDTO.getContent())) {
//            log.error("内容不能为空");
//            return false;
//        }
//        if (StringUtils.isEmpty(noticeCreateDTO.getDispatchWay())) {
//            log.error("推送方式不能为空");
//            return false;
//        }
//        if (CollectionUtils.isEmpty(noticeCreateDTO.getUsers())) {
//            log.error("推送人员不能为空");
//            return false;
//        }
//        return true;
//    }
//
//    @Transactional
//    public boolean doSend(String noticeId) {
//        Notice notice = fabosJsonDao.findById(Notice.class, noticeId);
//        if (notice == null) {
//            return false;
//        }
//        sendNotice(notice);
//        return true;
//    }
//
//    public void sendNotice(Notice notice) {
//        MessageRecord createDTO = new MessageRecordCreateDTO();
//        createDTO.setTitle(notice.getTitle());
//        createDTO.setContent(notice.getContent());
//        createDTO.setDispatchWay(notice.getDispatchWay());
//        createDTO.setDispatchUser(notice.getDispatchUserAccount());
//        createDTO.setUserDTOList(getUserInfo(notice));
//        createDTO.setMessageId(notice.getId());
//        List<PushUserDTO> users = notice.getUsers().stream().map(u -> {
//            PushUserDTO metaUserinfo = new PushUserDTO();
//            BeanUtils.copyProperties(u, metaUserinfo);
//            return metaUserinfo;
//        }).toList();
//        createDTO.setUserDTOList(users);
//        List<String> wayList = Arrays.asList(createDTO.getDispatchWay().split(","));
//
//        wayList.forEach(w -> {
//            createDTO.setDispatchWay(w);
//            notice.getUsers().forEach(user -> {
//                MessageRecord messageRecord = new MessageRecord();
//                BeanUtils.copyProperties(createDTO, messageRecord);
//                messageRecord.setDispatchWay(w);
//                messageRecord.setStatus(YesOrNoStatus.NO.getValue());
//                messageRecord.setDispatchUser(user.getPhoneNumber());
//                messageRecord.setProcessCompleteFlag(YesOrNoStatus.NO.getValue());
//                messageRecordService.create(messageRecord);
//                createDTO.setGid(messageRecord.getId());
//                MessageHandler messageHandler = messageHandlerFactory.getMessageHandler(w);
//                try {
//                    messageHandler.sendMessage(createDTO);
//                } catch (Exception e) {
//                    throw new RuntimeException(e);
//                }
//                createDTO.setGid(null);
//            });
//        });
//    }
//
//    private List<PushUserDTO> getUserInfo(Notice notice) {
//        List<PushUserDTO> list = new ArrayList<>();
//        notice.getUsers().forEach(user -> {
//            PushUserDTO userInfo = new PushUserDTO();
//            BeanUtils.copyProperties(user, userInfo);
//            list.add(userInfo);
//        });
//        return list;
//    }
//
//}
