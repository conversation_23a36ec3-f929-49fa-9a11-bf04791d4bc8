package cec.jiutian.bc.generalModeler.domain.stockAlarm.proxy;

import cec.jiutian.bc.generalModeler.domain.stockAlarm.model.UserViewForAlarm;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.core.frame.constant.YesOrNoStatus;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Map;

@Component
public class UserViewForAlarmDataProxy implements DataProxy<UserViewForAlarm> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    private static final String orgQuery = "SELECT u.org FROM User u WHERE u.id = :userId";
    private static final String positionQuery = "SELECT u.position FROM User u WHERE u.id = :userId";

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            ArrayList<Map<String, Object>> res = new ArrayList<>(list.size());
            for (Map<String, Object> map : list) {
                String id = (String) map.getOrDefault("id", null);
                if (id == null) {
                    continue;
                }
                User user = fabosJsonDao.findById(User.class, id);
                if (YesOrNoStatus.NO.getValue().equals(user.getState())) {
                    continue;
                }
                map.put("state", user.getState());
                map.put("name", user.getName());
                map.put("gender", user.getGender());
                map.put("employeeNumber", user.getEmployeeNumber());
                res.add(map);
            }
            list = res;
        }
    }
}
