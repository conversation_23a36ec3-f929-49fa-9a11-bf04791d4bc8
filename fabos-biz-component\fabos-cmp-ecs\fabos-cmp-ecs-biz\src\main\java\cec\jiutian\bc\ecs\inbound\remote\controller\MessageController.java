package cec.jiutian.bc.ecs.inbound.remote.controller;

import cec.jiutian.bc.ecs.domain.messagerecord.service.MessageRecordService;
import cec.jiutian.bc.ecs.service.MessageProcessService;
import cec.jiutian.core.frame.module.R;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description:
 */
@RestController
@RequestMapping("/fabos-cmp-ecs/message")
public class MessageController {

    private final MessageRecordService messageRecordService;
    private final MessageProcessService messageProcessService;

    public MessageController(MessageRecordService messageRecordService, MessageProcessService messageProcessService) {
        this.messageRecordService = messageRecordService;
        this.messageProcessService = messageProcessService;
    }

    @PostMapping("/getMyMessage")
    public R getMyMessage(){
       return R.ok(messageRecordService.queryMyMessage());
    }


    @PostMapping("/updateMessageStatus/{id}")
    public R updateMessageStatus(@PathVariable("id") String id) {
        messageRecordService.updateRecordStatus(id);
        return R.ok();
    }


    @PostMapping("/clearUnread")
    public R clearUnread() {
        messageRecordService.clearUnread();
        return R.ok();
    }

    /**
     * 修改当前预警消息状态为处理完成
     * 参数：消息ID
     */
    @PostMapping("/messageProcessComplete/{id}")
    public R messageProcessComplete(@PathVariable("id") String id, @RequestParam(value = "processDetail", required = false) String processDetail) {
        messageProcessService.messageProcessComplete(id, processDetail);
        return R.ok();
    }
}
