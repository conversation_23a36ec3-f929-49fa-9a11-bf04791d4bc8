package cec.jiutian.bc.urm.inbound.local.service.command;

import cec.jiutian.bc.urm.domain.menu.entity.Menu;
import cec.jiutian.bc.urm.domain.menu.service.MenuService;
import cec.jiutian.bc.urm.domain.role.entity.Role;
import cec.jiutian.bc.urm.domain.role.service.RoleService;
import cec.jiutian.bc.urm.domain.user.entity.FabosJsonUserConst;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.bc.urm.domain.user.service.UserService;
import cec.jiutian.component.utils.ReadInitMenuDataFromCSVUtil;
import cec.jiutian.core.frame.constant.SwitchStatus;
import cec.jiutian.meta.core.util.MD5Util;
import cec.jiutian.bc.urm.domain.dictionary.service.DictionaryService;
import cec.jiutian.bc.urm.domain.tenant.constant.RightManagementPolicyEnum;
import cec.jiutian.bc.urm.dto.InitDataDTO;
import cec.jiutian.bc.urm.dto.MetaDict;
import cec.jiutian.bc.urm.dto.MetaMenu;
import io.micrometer.common.util.StringUtils;
import jakarta.transaction.Transactional;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@Transactional
public class InitDataCommandService {

    private static final List<String> systemManagerValue = List.of("user", "user@add", "user@delete",
            "user@edit", "role", "role@add", "role@delete", "role@edit", "User@VIEW_DETAIL", "Role@VIEW_DETAIL",
            "LoginLog", "LoginLog@EXPORT", "LoginLog@VIEW_DETAIL", "WebApiLog", "WebApiLog@EXPORT", "WebApiLog@VIEW_DETAIL",
            "Dictionary", "Dictionary@add", "Dictionary@delete", "Dictionary@update", "Dictionary@export",
            "Dictionary@view_detail", "DictionaryItem", "DictionaryItem@add", "DictionaryItem@update",
            "DictionaryItem@view_detail", "DictionaryItem@delete");
    private static final List<String> auditManagerValue = List.of("LoginLog", "LoginLog@EXPORT", "LoginLog@VIEW_DETAIL",
            "WebApiLog", "WebApiLog@EXPORT", "WebApiLog@VIEW_DETAIL");
    private static final List<String> securityManagerValue = List.of("user", "User@EDITROLS", "User@VIEW_DETAIL",
            "Role@VIEW_DETAIL", "Menu@VIEW_DETAIL", "User@RESET", "User@UNLOCK", "role", "Role@AUTH", "menu", "menu@add",
            "menu@delete", "menu@edit", "LoginLog", "LoginLog@EXPORT", "LoginLog@VIEW_DETAIL", "WebApiLog",
            "WebApiLog@EXPORT", "WebApiLog@VIEW_DETAIL", "LowCodePageManagement", "LowCodeMenu@ADD", "LowCodeMenu@EDIT",
            "LowCodeMenu@DELETE", "LowCodeMenu@VIEW_DETAIL", "LowCodeMenu@DESIGN", "LowCodeMenu@HISTORY", "LowCodeMenu@RELEASE",
            "LowCodeMenu@PREVIEW", "LowCodeMenu@EXPORT");
    private final MenuService menuService;
    private final RoleService roleService;
    private final UserService userService;

    private final DictionaryService dictionaryService;


    public InitDataCommandService(MenuService menuService, RoleService roleService, UserService userService,
                                  DictionaryService dictionaryService) {
        this.menuService = menuService;
        this.roleService = roleService;
        this.userService = userService;
        this.dictionaryService = dictionaryService;
    }

    public List<Menu> savaMetaMenus(List<MetaMenu> metaMenus) {
        return menuService.saveMetaMenus4Init(metaMenus);
    }

    public List<Menu> initMenu(InitDataDTO dto) {
        List<MetaMenu> metaMenus = new ArrayList<>();
        metaMenus.add(MetaMenu.createRootMenu("$rightManager", "权限设置", "fa fa-cogs",
                1, dto.getOid(), dto.getComponentName()));
        Optional.ofNullable(ReadInitMenuDataFromCSVUtil.convertCsvToList(
                dto.getComponentName())).ifPresent(e -> metaMenus.addAll(e));
        List<Menu> menus = savaMetaMenus(metaMenus);
        return menus;
    }

    public void initData(InitDataDTO dto) {
        /**
         *  初始化菜单数据
         */
        List<Menu> menus = initMenu(dto);

        /**
         *  初始化角色数据,角色与菜单关联数据
         */
        List<Role> roles = new ArrayList<>();
        if (RightManagementPolicyEnum.SUPER.getValue().equals(dto.getRightManagementPolicy())) {
            assembleSuperRole(roles, menus);

        } else if (RightManagementPolicyEnum.TRIPARTITE.getValue().equals(dto.getRightManagementPolicy())) {
            assembleTripartiteRole(roles, menus);
        }
        roles = roleService.createRoles4Init(roles);

        /**
         *  初始化用户数据,用户与角色关联数据
         */
        List<User> users = new ArrayList<>();
        if (RightManagementPolicyEnum.SUPER.getValue().equals(dto.getRightManagementPolicy())) {
            assembleSuperUser(users, roles, dto);

        } else if (RightManagementPolicyEnum.TRIPARTITE.getValue().equals(dto.getRightManagementPolicy())) {
            assembleTripartiteUser(users, roles, dto);
        }
        userService.createUsers4Init(users);


    }
    private void assembleTripartiteUser(List<User> users, List<Role> roles, InitDataDTO dto) {
        User auditManagerUser = new User();
        User securityManagerUser = new User();
        User systemManagerUser = new User();

        List<Role> auditRoles = new ArrayList<>();
        List<Role> securityRoles = new ArrayList<>();
        List<Role> systemRoles = new ArrayList<>();

        Map<String, Object> codeMap = new HashMap<>();

        roles.stream().forEach(e -> codeMap.put(e.getCode(), e));
        auditRoles.add((Role) codeMap.get("auditManager"));
        securityRoles.add((Role) codeMap.get("securityManager"));
        systemRoles.add((Role) codeMap.get("systemManager"));

        auditManagerUser.setName("审计员");
        auditManagerUser.setRoles(auditRoles);
        auditManagerUser.setOid(roles.get(0).getOid());
        auditManagerUser.setCreateBy("InitData");
        auditManagerUser.setCreateTime(LocalDateTime.now());
        auditManagerUser.setAccount("auditUser");
        auditManagerUser.setPassword(MD5Util.digest(FabosJsonUserConst.DEFAULT_USER_PASSWORD));
        auditManagerUser.setState(SwitchStatus.ENABLED.getValue());
        auditManagerUser.setEmailAddress(dto.getEmailAddress());
        auditManagerUser.setPhoneNumber(dto.getAuditPhoneNumber());

        securityManagerUser.setName("安全员");
        securityManagerUser.setRoles(securityRoles);
        securityManagerUser.setOid(roles.get(0).getOid());
        securityManagerUser.setCreateBy("InitData");
        securityManagerUser.setCreateTime(LocalDateTime.now());
        securityManagerUser.setAccount("securityUser");
        securityManagerUser.setPassword(MD5Util.digest(FabosJsonUserConst.DEFAULT_USER_PASSWORD));
        securityManagerUser.setState(SwitchStatus.ENABLED.getValue());
        securityManagerUser.setEmailAddress(dto.getEmailAddress());
        securityManagerUser.setPhoneNumber(dto.getSecurityPhoneNumber());

        systemManagerUser.setName("系统员");
        systemManagerUser.setRoles(systemRoles);
        systemManagerUser.setOid(roles.get(0).getOid());
        systemManagerUser.setCreateBy("InitData");
        systemManagerUser.setCreateTime(LocalDateTime.now());
        systemManagerUser.setAccount("systemUser");
        systemManagerUser.setPassword(MD5Util.digest(FabosJsonUserConst.DEFAULT_USER_PASSWORD));
        systemManagerUser.setState(SwitchStatus.ENABLED.getValue());
        systemManagerUser.setEmailAddress(dto.getEmailAddress());
        systemManagerUser.setPhoneNumber(dto.getSystemPhoneNumber());

        users.add(auditManagerUser);
        users.add(securityManagerUser);
        users.add(systemManagerUser);
    }

    private void assembleSuperUser(List<User> users, List<Role> roles, InitDataDTO dto) {

        User superUser = new User();

        superUser.setName("超级管理员");
        superUser.setRoles(roles);
        superUser.setOid(roles.get(0).getOid());
        superUser.setCreateBy("InitData");
        superUser.setCreateTime(LocalDateTime.now());
        superUser.setAccount("superUser");
        superUser.setPassword(MD5Util.digest(FabosJsonUserConst.DEFAULT_USER_PASSWORD));
        superUser.setState(SwitchStatus.ENABLED.getValue());
        superUser.setEmailAddress(dto.getEmailAddress());
        superUser.setPhoneNumber(dto.getSuperPhoneNumber());
        users.add(superUser);

    }

    private void assembleTripartiteRole(List<Role> roles, List<Menu> menus) {
        Role auditManager = new Role();
        Role securityManager = new Role();
        Role systemManager = new Role();

        List<Menu> auditMenu = new ArrayList<>();
        List<Menu> securityMenu = new ArrayList<>();
        List<Menu> systemMenu = new ArrayList<>();

        Map<String, Object> valueMap = new HashMap<>();

        menus.stream().forEach(e -> {
            if (!StringUtils.isBlank(e.getModuleValue())) {
                valueMap.put(e.getModuleValue().toLowerCase(), e);
            }
            if ("$rightManager".equals(e.getModuleCode())) {
                systemMenu.add(e);
                securityMenu.add(e);
                auditMenu.add(e);
            }
        });
        systemManagerValue.stream().forEach(e -> systemMenu.add((Menu) valueMap.get(e.toLowerCase())));
        auditManagerValue.stream().forEach(e -> auditMenu.add((Menu) valueMap.get(e.toLowerCase())));
        securityManagerValue.stream().forEach(e -> securityMenu.add((Menu) valueMap.get(e.toLowerCase())));


        /**
         * 安全审计管理员
         */
        auditManager.setCode("auditManager");
        auditManager.setDescription("审计管理员");
        auditManager.setSort(1);
        auditManager.setName("审计管理员");
        auditManager.setCreateBy("InitData");
        auditManager.setCreateTime(LocalDateTime.now());
        auditManager.setStatus(true);
        auditManager.setMenus(auditMenu);
        auditManager.setOid(menus.get(0).getOid());


        /**
         * 安全管理员
         */
        securityManager.setCode("securityManager");
        securityManager.setDescription("安全管理员");
        securityManager.setSort(2);
        securityManager.setName("安全管理员");
        securityManager.setCreateBy("InitData");
        securityManager.setCreateTime(LocalDateTime.now());
        securityManager.setStatus(true);
        securityManager.setMenus(securityMenu);
        securityManager.setOid(menus.get(0).getOid());

        /**
         * 系统管理员
         */
        systemManager.setCode("systemManager");
        systemManager.setDescription("系统管理员");
        systemManager.setSort(3);
        systemManager.setName("系统管理员");
        systemManager.setCreateBy("InitData");
        systemManager.setCreateTime(LocalDateTime.now());
        systemManager.setStatus(true);
        systemManager.setMenus(systemMenu);
        systemManager.setOid(menus.get(0).getOid());

        roles.add(auditManager);
        roles.add(securityManager);
        roles.add(systemManager);
    }

    private void assembleSuperRole(List<Role> roles, List<Menu> menus) {
        Role role = new Role();
        role.setCode("superManager");
        role.setDescription("超级管理员");
        role.setSort(1);
        role.setName("超级管理员");
        role.setCreateBy("Init");
        role.setCreateTime(LocalDateTime.now());
        role.setStatus(true);
        role.setMenus(menus);
        role.setOid(menus.get(0).getOid());
        roles.add(role);
    }


    public void saveMetaDictionary(List<MetaDict> dicts) {
        dictionaryService.saveMetaDictionary(dicts);
    }
}
