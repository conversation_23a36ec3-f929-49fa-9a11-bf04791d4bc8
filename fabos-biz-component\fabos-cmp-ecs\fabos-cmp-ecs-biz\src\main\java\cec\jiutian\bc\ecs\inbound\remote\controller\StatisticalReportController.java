//package cec.jiutian.bc.ecs.inbound.remote.controller;
//
//import cec.jiutian.bc.ecs.inbound.remote.provider.StatisticalReportBizProvider;
//import cec.jiutian.bc.ecs.query.dto.StatisticsQueryDTO;
//import cec.jiutian.core.frame.module.R;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.HashMap;
//import java.util.Map;
//
//@RestController
//@RequestMapping("/fabos-cmp-ecs/report")
//public class StatisticalReportController {
//
//    private StatisticalReportBizProvider statisticalReportBizProvider;
//
//    public StatisticalReportController(StatisticalReportBizProvider statisticalReportBizProvider) {
//        this.statisticalReportBizProvider = statisticalReportBizProvider;
//    }
//
//    /**
//     * 人员接收统计
//     * @param queryDTO
//     * @return
//     */
//    @PostMapping("/personReceiveRate")
//    public R countPersonReceiveRate(@RequestBody StatisticsQueryDTO queryDTO) {
//        Map<String, Object> res = statisticalReportBizProvider.countPersonReceiveRate(queryDTO);
//        return R.ok(res);
//    }
//
//
//    @PostMapping("/messageCount")
//    public R countMessageBySystem(@RequestBody StatisticsQueryDTO queryDTO) {
//        Object result = statisticalReportBizProvider.countMessageBySystem(queryDTO);
//        return  R.ok(result);
//    }
//
//
//    @PostMapping("/groupReceiveRate")
//    public R countGroupReceiveRate(@RequestBody StatisticsQueryDTO queryDTO) {
//        Object result = statisticalReportBizProvider.countGroupReceiveRate(queryDTO);
//        return R.ok(result);
//    }
//
//    /**
//     * 消息组发送统计
//     * @param queryDTO
//     * @return
//     */
//    @PostMapping("/messageSendStatistical")
//    public R messageSendStatistical(@RequestBody StatisticsQueryDTO queryDTO) {
//        Object result = statisticalReportBizProvider.messageSendStatistical(queryDTO);
//        HashMap<String, Object> res = new HashMap<>();
//        res.put("items", result);
//        return R.ok(res);
//    }
//}
