//package cec.jiutian.bc.ecs.domain.extendFabosJsonTest;
//
//import cec.jiutian.common.util.BeanUtils;
//import cec.jiutian.view.fun.OperationHandler;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
//
//@Component
//@Slf4j
//public class DogOperationHandler implements OperationHandler<Dog, DogView> {
//
//
//    @Override
//    public String exec(List<Dog> data, DogView modelObject, String[] param) {
//        log.info("DogOperationHandler.exec");
//        return "msg.success()";
//    }
//
//    @Override
//    public DogView fabosJsonFormValue(List<Dog> data, DogView fabosJsonForm, String[] param) {
//        Dog dog = data.get(0);
//        BeanUtils.copyProperties(dog, fabosJsonForm);
//        return OperationHandler.super.fabosJsonFormValue(data, fabosJsonForm, param);
//    }
//}
