package cec.jiutian.bc.urm.domain.menu.service.query;

import cec.jiutian.core.base.Query;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class ColumnPermissionQueryDTO implements Serializable {

    private List<String> roleIds;

    private List<String> metadataIds;

}
