package cec.jiutian.bc.urm.domain.user.entity;

import cec.jiutian.core.data.annotation.LinkTable;
import cec.jiutian.core.frame.constant.GenderEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.SubTableField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "fd_meta_user")
@FabosJson(
        name = "用户配置"
)
@LinkTable
@FabosJsonI18n
@Getter
@Setter
public class UserByRoleView extends MetaModel {

    @FabosJsonField(
            views = @View(title = "账号"),
            edit = @Edit(title = "账号", search = @Search(vague = true), notNull = true, readonly = @Readonly(add = false))
    )
    @SubTableField
    private String account;

    @FabosJsonField(
            views = @View(title = "姓名"),
            edit = @Edit(title = "姓名", search = @Search(vague = true), notNull = true)
    )
    @SubTableField
    private String name;

    @FabosJsonField(
            views = @View(title = "性别"),
            edit = @Edit(title = "性别", search = @Search,
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {GenderEnum.ChoiceFetch.class}))
    )
    private String gender;

    @FabosJsonField(
            views = @View(title = "工号"),
            edit = @Edit(title = "工号", search = @Search(vague = true))
    )
    @SubTableField
    private String employeeNumber;

//    @FabosJsonField(
//            views = @View(title = "岗位"),
//            edit = @Edit(title = "岗位", search = @Search(vague = true))
//    )
//    private String jobPosition;


    @FabosJsonField(
            views = @View(title = "工种"),
            edit = @Edit(title = "工种", search = @Search(vague = true))
    )
    private String jobType;

    private String phoneNumber;

}
