package cec.jiutian.bc.urm.ModelRowPermission.entity;


import cec.jiutian.bc.urm.ModelRowPermission.event.ModelRowPermissionDataProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.meta.model.MetadataModel;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.SubTableField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.CodeEditorType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "model_row_permissions")
@Getter
@Setter
@FabosJson(
        dataProxy = ModelRowPermissionDataProxy.class,
        name = "模型行权限配置",
        orderBy = "ModelRowPermission.createTime desc"
)
public class ModelRowPermission extends MetaModel {

    @FabosJsonField(
            views = @View(title = "角色ID", show = false, toolTip = true),
            edit = @Edit(title = "角色ID", show = false, search = @Search(vague = true))
    )
    private String roleId;
    @FabosJsonField(
            views = @View(title = "元模型",show = false,column = "displayName"),
            edit = @Edit(title = "元模型",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(),
                    filter = @Filter(value = "MetadataModel.recordType = 'business'")
            )
    )
    @Transient
    private MetadataModel model;

    @FabosJsonField(
            views = @View(title = "元数据ID",show = false, toolTip = true),
            edit = @Edit(title = "元数据ID", show = false, search = @Search(vague = true)),
                    dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "model",beFilledBy = "id"))
    )
    @SubTableField
    private String metadataId;


    @FabosJsonField(
            views = @View(title = "元模型显示名称", toolTip = true),
            edit = @Edit(title = "元模型显示名称", notNull = true, search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "model",beFilledBy = "displayName"))
    )
    @SubTableField
    private String modelDisplayName;

    @FabosJsonField(
            views = @View(title = "元模型名称", toolTip = true),
            edit = @Edit(title = "元模型名称", notNull = true, search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "model",beFilledBy = "name"))
    )
    @SubTableField
    private String modelName;


    @FabosJsonField(
            views = @View(title = "过滤条件", toolTip = true),
            edit = @Edit(title = "过滤条件", notNull = true, search = @Search,
                    type = EditType.TEXTAREA,
                    codeEditType = @CodeEditorType(language = "java")
            )
    )
    @Column(columnDefinition = "text")
    @SubTableField
    private String filter;
}
