package cec.jiutian.bc.urm.domain.user.service;

import cec.jiutian.core.frame.exception.FabosJsonWebApiRuntimeException;
import cec.jiutian.view.config.Comment;
import cec.jiutian.core.view.fabosJson.util.FabosJsonSpringUtil;
import cec.jiutian.bc.urm.domain.user.entity.LoginModel;
import cec.jiutian.bc.urm.domain.user.entity.User;

public interface LoginProxy {

    @Comment("登录校验，如要提示校验结果请抛异常")
    @Comment("为安全考虑pwd是加密的，加密逻辑：md5(md5(pwd)+ Calendar.DAY_OF_MONTH +account)")
    @Comment("Calendar.DAY_OF_MONTH → Calendar.getInstance().get(Calendar.DAY_OF_MONTH) //今天月的哪一天")
    @Comment("如果不希望加密，请前往配置文件，将：fabos-app.pwdTransferEncrypt 设置为 false 即可")
    default User login(String account, String pwd,String verifyCode,String phone) {
        LoginModel loginModel = FabosJsonSpringUtil.getBean(UserService.class).login(account, pwd,verifyCode,phone);
        if (loginModel.isPass()) {
            return loginModel.getUser();
        } else {
            throw new FabosJsonWebApiRuntimeException(loginModel.getReason());
        }
    }

    @Comment("登录成功")
    default void loginSuccess(User user, String token) {
    }

    @Comment("注销事件")
    default void logout(String token) {

    }

    @Comment("修改密码")
    default void beforeChangePwd(User user, String newPwd) {

    }

    @Comment("完成修改密码")
    default void afterChangePwd(User user, String originPwd, String newPwd) {
    }

}
