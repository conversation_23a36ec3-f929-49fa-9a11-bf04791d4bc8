package cec.jiutian.bc.generalModeler.domain.namingRule.proxy;


import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRule;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleParameter;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleParameterSrValue;
import cec.jiutian.bc.generalModeler.domain.namingRule.service.NamingRuleDomainService;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;


@Component
public class NamingRuleParameterDataProxy implements DataProxy<NamingRuleParameter> {

    @Resource
    private NamingRuleDomainService namingRuleDomainService;

    @Resource
    private FabosJsonDao FabosJsonDao;


    @Override
    public void beforeAdd(NamingRuleParameter namingRuleParameter) {
        NamingRule namingRule = namingRuleDomainService.getParametersByCode(namingRuleParameter.getNamingRule().getNamingRuleCode());
        //      校验SR类型参数指定的依赖参数的有效性
        if (!NamingRuleDataProxy.checkSrParameterDependencies(namingRule.getNamingRuleParameterList())) {
            throw new FabosJsonApiErrorTip("依赖参数不正确");
        }
        // 设置子模型的父模型对象
        for (NamingRuleParameterSrValue namingRuleParameterSrValue : namingRuleParameter.getNamingRuleParameterSrValueSet()) {
            namingRuleParameterSrValue.setSrParameter(namingRuleParameter);
            namingRuleParameterSrValue.setSrParameterName(namingRuleParameter.getParameterName());
        }
    }

    @Override
    public void beforeUpdate(NamingRuleParameter namingRuleParameter) {

        NamingRule namingRule = FabosJsonDao.findById(NamingRule.class, FabosJsonDao.findById(NamingRuleParameter.class, namingRuleParameter.getId()).getNamingRule().getId());
//      校验SR类型参数指定的依赖参数的有效性
        if (!NamingRuleDataProxy.checkSrParameterDependencies(namingRule.getNamingRuleParameterList())) {
            throw new FabosJsonApiErrorTip("依赖参数不正确");
        }
        // 设置当前模型的父模型对象
        namingRuleParameter.setNamingRule(namingRule);
        // 设置子模型的父模型对象
        for (NamingRuleParameterSrValue namingRuleParameterSrValue : namingRuleParameter.getNamingRuleParameterSrValueSet()) {
            namingRuleParameterSrValue.setSrParameter(namingRuleParameter);
            namingRuleParameterSrValue.setSrParameterName(namingRuleParameter.getParameterName());
        }
    }


}
