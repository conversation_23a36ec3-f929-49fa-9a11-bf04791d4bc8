package cec.jiutian.bc.file.proxy;

import cec.jiutian.bc.file.model.FabosJsonFile;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.util.FabosJsonUtil;
import cec.jiutian.view.fun.AttachmentProxy;
import cec.jiutian.view.fun.DataProxy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2024-11-05 15:46
 */

@Slf4j
public class FileProcessProxy implements DataProxy<FabosJsonFile> {

    @Override
    public void afterDelete(FabosJsonFile fabosJsonFile) {
        if (Objects.isNull(fabosJsonFile)) {
            return;
        }
        String fileUUID = fabosJsonFile.getFileUUID();
        log.info("尝试删除文件：{}", fileUUID);
        String pathOnFileSystem = fabosJsonFile.getPathOnFileSystem();
        if (StringUtils.isNotBlank(pathOnFileSystem)) {
            File file = new File(pathOnFileSystem);
            if (file.exists()) {
                try(InputStream inputStream = new FileInputStream(file)) {
                    String md5 =  DigestUtils.md5Hex(inputStream);
                    if (Objects.equals(md5,fabosJsonFile.getMd5()) && file.delete()) {
                        log.info("已从本地文件系统上删除文件：{}", pathOnFileSystem);
                    }
                } catch (IOException e) {
                    log.warn("尝试从本地文件系统删除文件失败，文件可能不存在或已被删除：{}", pathOnFileSystem);
                }
            } else {
                log.warn("尝试从本地文件系统删除文件失败，文件可能不存在或已被删除：{}", pathOnFileSystem);
            }
        }

        String pathOnOSS = fabosJsonFile.getPathOnOSS();
        if (StringUtils.isNotBlank(pathOnOSS)) {
            AttachmentProxy attachmentProxy = FabosJsonUtil.findAttachmentProxy();
            if (null != attachmentProxy){
                if (attachmentProxy.remove(fabosJsonFile.getBucket(), pathOnOSS)) {
                    log.info("已从OSS上删除文件：{}", pathOnOSS);
                } else {
                    log.warn("尝试从OSS删除文件失败，文件可能不存在或已被删除：{}", pathOnFileSystem);
                }

            }
        }

    }

}
