package cec.jiutian.bc.generalModeler.domain.material.model;

import cec.jiutian.bc.generalModeler.domain.measureUnit.model.MeasureUnit;
import cec.jiutian.bc.generalModeler.enumeration.ProductEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.SubTableField;
import cec.jiutian.view.config.Comment;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@FabosJson(
        name = "产品主数据",
        filter = @Filter(value = "productMainCategory = 'FINALPRODUCT'")
)
@Table(name = "product"
)
@Entity
@Getter
@Setter
//@Accessors(chain = true)
@FabosJsonI18n
public class FinalProductDTM extends MetaModel {

    @FabosJsonField(
            views = @View(title = "产品代码"),
            edit = @Edit(title = "产品代码", search = @Search(vague = true))
    )
    @SubTableField
    private String productCode;

    @FabosJsonField(
            views = @View(title = "类别"),
            edit = @Edit(title = "类别", type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ProductEnum.class))
    )
    @SubTableField
    private String productMainCategory;


    @ManyToOne
    @FabosJsonField(
            views = @View(title = "产品分类", column = "name", type = ViewType.text),
            edit = @Edit(title = "产品分类", search = @Search())
    )
    @SubTableField
    private ProductCategory productCategory;

    @FabosJsonField(
            views = @View(title = "别名"),
            edit = @Edit(title = "别名")
    )
    @SubTableField
    @Comment("对外部展示名称（非密）")
    private String name;

    @FabosJsonField(
            views = @View(title = "内部名称"),
            edit = @Edit(title = "内部名称", search = @Search())
    )
    @SubTableField
    private String internalName;

    @FabosJsonField(
            views = @View(title = "定型名称"),
            edit = @Edit(title = "定型名称")
    )
    @SubTableField
    private String officialName;

    @FabosJsonField(
            views = @View(title = "产品代号（军方）"),
            edit = @Edit(title = "产品代号（军方）")
    )
    @SubTableField
    private String productCodeA;

    @FabosJsonField(
            views = @View(title = "产品代号（集团公司）"),
            edit = @Edit(title = "产品代号（集团公司）")
    )
    private String productCodeB;

    @FabosJsonField(
            views = @View(title = "产品代号（设计单位）"),
            edit = @Edit(title = "产品代号（设计单位）")
    )
    @SubTableField
    private String productCodeC;

    @FabosJsonField(
            views = @View(title = "内部代号"),
            edit = @Edit(title = "内部代号", search = @Search(vague = true))
    )
    @SubTableField
    private String productCodeD;

    @FabosJsonField(
            views = @View(title = "设计单位名称"),
            edit = @Edit(title = "设计单位名称")
    )
    @SubTableField
    private String designCompanyName;

    @FabosJsonField(
            views = @View(title = "设计单位代码"),
            edit = @Edit(title = "设计单位代码")
    )
    @SubTableField
    private String designCompanyCode;

    @FabosJsonField(
            views = @View(title = "图号"),
            edit = @Edit(title = "图号", search = @Search(vague = true))
    )
    @SubTableField
    private String pictureNumber;

    @FabosJsonField(
            views = @View(title = "型号"),
            edit = @Edit(title = "型号")
    )
    @SubTableField
    private String modelNumber;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格")
    )
    @SubTableField
    private String specNumber;

    @FabosJsonField(
            views = @View(title = "技术标准号/技术要求"),
            edit = @Edit(title = "技术标准号/技术要求")
    )
    @SubTableField
    private String standardNumber;

    @FabosJsonField(
            views = @View(title = "阶段信息"),
            edit = @Edit(title = "阶段信息")
    )
    @SubTableField
    private String phaseInfo;

    @FabosJsonField(
            views = @View(title = "特殊信息"),
            edit = @Edit(title = "特殊信息")
    )
    @SubTableField
    private String otherInfo;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "计量单位", column = "unitChnName"),
            edit = @Edit(title = "计量单位")
    )
    @SubTableField
    private MeasureUnit measureUnit;

    @FabosJsonField(
            views = @View(title = "项目代号"),
            edit = @Edit(title = "项目代号")
    )
    @SubTableField
    private String projectNumber;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "storeKeeper_id")
    @FabosJsonField(
            views = @View(title = "保管员", column = "name", type = ViewType.text),
            edit = @Edit(title = "保管员",
                    filter = @Filter(value = "MetaUser.state = 'Y'"),
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name", type = ReferenceTableType.SelectShowTypeMTO.DROPDOWN_TABLE),
                    search = @Search())
    )
    private MetaUser storeKeeper;
}
