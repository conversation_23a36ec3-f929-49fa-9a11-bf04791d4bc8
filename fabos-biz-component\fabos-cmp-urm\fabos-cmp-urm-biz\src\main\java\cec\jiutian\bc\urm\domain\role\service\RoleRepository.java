package cec.jiutian.bc.urm.domain.role.service;

import cec.jiutian.bc.urm.domain.role.entity.Role;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Collection;
import java.util.List;

public interface RoleRepository extends JpaRepository<Role, String> {
    @Query(value = """
            select r.*
            from FD_ROLE r
            where r.id in
                  (select ROLE_ID rm from FD_ROLE_MENU rm where rm.MENU_ID in ?1)""", nativeQuery = true)
    List<Role> findRolesUsingTheseMenus(Collection<String> ids);
}