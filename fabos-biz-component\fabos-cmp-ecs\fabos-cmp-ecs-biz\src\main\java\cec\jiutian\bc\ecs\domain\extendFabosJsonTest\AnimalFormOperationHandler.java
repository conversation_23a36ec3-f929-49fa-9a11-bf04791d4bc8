//package cec.jiutian.bc.ecs.domain.extendFabosJsonTest;
//
//import cec.jiutian.view.fun.OperationHandler;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
//
//@Component
//@Slf4j
//public class AnimalFormOperationHandler implements OperationHandler<Animal, Animal> {
//
//
//    @Override
//    public String exec(List<Animal> data, Animal modelObject, String[] param) {
//        log.info("AnimalOperationHandler.exec");
//        return "msg.success()";
//    }
//
//    @Override
//    public Animal fabosJsonFormValue(List<Animal> data, Animal fabosJsonForm, String[] param) {
//        log.warn("AnimalOperationHandler.fabosJsonFormValue");
//        return OperationHandler.super.fabosJsonFormValue(data, fabosJsonForm, param);
//    }
//}
