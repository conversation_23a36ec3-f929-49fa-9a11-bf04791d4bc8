package cec.jiutian.bc.generalModeler.domain.material.model;

import cec.jiutian.bc.generalModeler.domain.measureUnit.model.MeasureUnit;
import cec.jiutian.bc.generalModeler.enumeration.MaterialEnum;
import cec.jiutian.bc.generalModeler.enumeration.ProductEnum;
import cec.jiutian.bc.generalModeler.enumeration.StockTypeEnum;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.InputGroup;
import cec.jiutian.view.SubTableField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.field.edit.TabTableReferType;
import cec.jiutian.view.type.Power;
import jakarta.persistence.ConstraintMode;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/* 依赖的视图创建语句
CREATE
	OR REPLACE VIEW stock_view AS
SELECT
	id,
	'MATERIAL' AS stock_Type,
	material_category_id AS stock_category_id,
	MATERIAL_CODE AS CODE,
	NAME,
	null as product_codeD,
	brand_Code,
	material_Specification as specification,
	null as picture_Number,
	account_Unit_id,
	wl09 as standard_Number,
	store_Keeper_id as store_Keeper_id,
	null as stock_sub_type,
	wl27 as producer,
	max_Reinspect_Times as max_Reinspect_Times,
	CURRENT_STATUS as 	CURRENT_STATUS
FROM
	MATERIAL
UNION ALL
SELECT
	id,
	'PRODUCT' AS stock_Type,
	product_category_id AS stock_category_id,
	product_code AS CODE,
	NAME,
	product_codeD as product_codeD,
    null as brand_Code,
    spec_Number as specification,
    picture_Number,
    measure_Unit_id as account_Unit_id,
    standard_Number,
	store_Keeper_id as store_Keeper_id,
	product_Main_Category as stock_sub_type,
	null as producer,
	max_Reinspect_Times as max_Reinspect_Times,
	'Y' as CURRENT_STATUS
FROM
	PRODUCT;

CREATE
	OR REPLACE VIEW stock_usefor_view AS
select * from e_material_usefor_product
union
select * from E_SEMIPRODUCT_USEFOR_PRODUCT;
*/

@FabosJson(
        name = "物资模型视图",
        power = @Power(add = false, edit = false, delete = false, importable = false, export = false)
)
@Table(name = "stock_view"
)
@Entity
@Getter
@Setter
public class StockView {


    @Id
    @FabosJsonField(
            views = @View(title = "id", show = false),
            edit = @Edit(title = "id")
    )
    private String id;

    @FabosJsonField(
            views = @View(title = "类别"),
            edit = @Edit(title = "类别", notNull = false,
                    type = EditType.CHOICE,
                    search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = StockTypeEnum.class)
            )
    )
    @SubTableField
    private String stockType;

    @FabosJsonField(
            views = @View(title = "子类别", show = false),
            edit = @Edit(title = "子类别", notNull = false, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = ProductEnum.class),
                    readonly = @Readonly(add = true, edit = true),
                    search = @Search(vague = true))
    )
    private String stockSubType;

    @ManyToOne
    @JoinColumn(foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "分类代码", column = "name"),
            edit = @Edit(
                    title = "分类代码",
                    type = EditType.REFERENCE_TABLE,
                    notNull = false,
                    search = @Search(vague = true)
            )
    )
    @SubTableField
    private StockCategoryView stockCategory;

    @FabosJsonField(
            views = @View(title = "物资代码", show = true),
            edit = @Edit(title = "物资代码", search = @Search(vague = true))
    )
    @SubTableField
    private String code;

    @FabosJsonField(
            views = @View(title = "物资名称"),
            edit = @Edit(title = "物资名称", notNull = true, search = @Search(vague = true))
    )
    @SubTableField
    private String name;

    @FabosJsonField(
            views = @View(title = "内部代号", show = false),
            edit = @Edit(title = "内部代号", search = @Search(vague = true))
    )
    @SubTableField
    private String productCodeD;

    @FabosJsonField(
            views = @View(title = "品号", show = false),
            edit = @Edit(title = "品号", search = @Search(vague = true))
    )
    @SubTableField
    private String brandCode;

    @FabosJsonField(
            views = @View(title = "规格", show = true),
            edit = @Edit(title = "规格", search = @Search(vague = true))
    )
    @SubTableField
    private String specification;

    @FabosJsonField(
            views = @View(title = "图号", show = false),
            edit = @Edit(title = "图号", search = @Search(vague = true))
    )
    @SubTableField
    private String pictureNumber;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "计量单位", column = "unitChnName"),
            edit = @Edit(title = "计量单位",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "unitChnName",
                            type = ReferenceTableType.SelectShowTypeMTO.LIST, groupField = "unitType")
            )
    )
    @SubTableField
    @JoinColumn(foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    private MeasureUnit accountUnit;

    @FabosJsonField(
            views = @View(title = "技术标准号"),
            edit = @Edit(title = "技术标准号")
    )
    @SubTableField
    private String standardNumber;

    @FabosJsonField(
            views = @View(title = "制造商"),
            edit = @Edit(title = "制造商", notNull = false)
    )
    private String producer;

    @FabosJsonField(
            views = @View(title = "最大复验次数", show = false),
            edit = @Edit(title = "最大复验次数", notNull = false,
                    inputGroup = @InputGroup(postfix = "次"))
    )
    private Integer maxReinspectTimes;

    @ManyToOne
    @JoinColumn(foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "保管员", column = "name"),
            edit = @Edit(title = "保管员",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name", type = ReferenceTableType.SelectShowTypeMTO.DROPDOWN_TABLE),
                    allowAddMultipleRows = false
            )
    )
    private MetaUser storeKeeper;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态", notNull = true, type = EditType.CHOICE,
                    search = @Search(),
                    choiceType = @ChoiceType(fetchHandler = MaterialEnum.class)
            )
    )
    private String currentStatus;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "stock_usefor_view", // 中间表表名，如下为中间表的定义
            foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT),
            inverseForeignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT),
            joinColumns = @JoinColumn(name = "target_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "source_id", referencedColumnName = "id"))
    @FabosJsonField(
            views = @View(title = "用途", column = "name", type = ViewType.TABLE_VIEW, show = false),
            edit = @Edit(title = "用途",
                    type = EditType.TAB_TABLE_REFER,
                    tabTableReferType = @TabTableReferType(type = TabTableReferType.SelectShowTypeMTM.LIST,
                            groupField = "productCategory_name",
                            label = "productCodeD"),
                    notNull = false
//                    ,search = @Search()
            )
    )
    private List<FinalProductDTM> useForProductlist;
}
