package cec.jiutian.bc.generalModeler.domain.material.proxy;

import cec.jiutian.bc.generalModeler.domain.material.model.ProductCategory;
import cec.jiutian.bc.generalModeler.enumeration.ProductEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class ProductCategoryDataProxy implements DataProxy<ProductCategory> {
    @Resource
    private FabosJsonDao fabosJsonDao;


    @Override
    public void beforeAdd(ProductCategory productCategory) {

        //设置级别
        if (productCategory.getParentCategory() == null) {
            //判断是否符合第一级要求
            if (!productCategory.getCode().equals(ProductEnum.Enum.FINALPRODUCT.name()) && !productCategory.getCode().equals(ProductEnum.Enum.SEMIPRODUCT.name())) {
                throw new FabosJsonApiErrorTip("第一级必须为成品和零部件，请使用" + ProductEnum.Enum.FINALPRODUCT.name().toString() + "，" + ProductEnum.Enum.SEMIPRODUCT.name().toString() + "作为编码分别创建成品和零部件类别！");
            }
            productCategory.setCategoryLevel(1);
            if (productCategory.getCode().equals(ProductEnum.Enum.FINALPRODUCT.name())) {
                productCategory.setMainCategory(ProductEnum.Enum.FINALPRODUCT.name());
            }
            if (productCategory.getCode().equals(ProductEnum.Enum.SEMIPRODUCT.name())) {
                productCategory.setMainCategory(ProductEnum.Enum.SEMIPRODUCT.name());
            }
        } else {
            ProductCategory parentProductCategory = fabosJsonDao.getEntityManager().find(ProductCategory.class, productCategory.getParentCategory().getId());
            productCategory.setCategoryLevel(parentProductCategory.getCategoryLevel() + 1);
            productCategory.setMainCategory(parentProductCategory.getMainCategory());
        }
    }

    @Override
    public void beforeUpdate(ProductCategory productCategory) {
        //调整级别
        if (productCategory.getParentCategory() == null) {
            if (!productCategory.getCode().equals(ProductEnum.Enum.FINALPRODUCT.name()) && !productCategory.getCode().equals(ProductEnum.Enum.SEMIPRODUCT.name())) {
                throw new FabosJsonApiErrorTip("第一级必须为成品和零部件，请使用" + ProductEnum.Enum.FINALPRODUCT.name().toString() + "，" + ProductEnum.Enum.SEMIPRODUCT.name().toString() + "作为编码分别创建成品和零部件类别！");
            }
            productCategory.setCategoryLevel(1);
        } else {
            ProductCategory parentProductCategory = fabosJsonDao.getEntityManager().find(ProductCategory.class, productCategory.getParentCategory().getId());
            productCategory.setCategoryLevel(parentProductCategory.getCategoryLevel() + 1);
            productCategory.setMainCategory(parentProductCategory.getMainCategory());

        }
    }
}
