package cec.jiutian.bc.infra.util;


import cec.jiutian.bc.ecs.domain.modle.UserForMsg;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.common.util.SpringContextUtils;
import cec.jiutian.core.frame.constant.RegexConst;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.frame.module.MetaUser;

import java.util.HashMap;
import java.util.regex.Pattern;

public class UserSearchUtil {

    /**
     * 判断是否为手机号
     */
    private static final Pattern accountPattern = Pattern.compile(RegexConst.PHONE_REGEX);

    public static MetaUser getMetaUserByPhoneNum(String phoneNum) {

        if (!accountPattern.matcher(phoneNum).matches()){
            throw new ServiceException("手机号格式错误");
        }

        FabosJsonDao fabosJsonDao = SpringContextUtils.getBean(FabosJsonDao.class);

        return fabosJsonDao.queryEntity(MetaUser.class, "phoneNumber = :phoneNumber", new HashMap<String, Object>(4) {{
            this.put("phoneNumber", phoneNum);
        }});

    }

    public static UserForMsg getUserByPhoneNum(String phoneNum) {

        if (!accountPattern.matcher(phoneNum).matches()){
            throw new ServiceException("手机号格式错误");
        }

        FabosJsonDao fabosJsonDao = SpringContextUtils.getBean(FabosJsonDao.class);

        return fabosJsonDao.queryEntity(UserForMsg.class, "phoneNumber = :phoneNumber", new HashMap<String, Object>(4) {{
            this.put("phoneNumber", phoneNum);
        }});

    }
}
