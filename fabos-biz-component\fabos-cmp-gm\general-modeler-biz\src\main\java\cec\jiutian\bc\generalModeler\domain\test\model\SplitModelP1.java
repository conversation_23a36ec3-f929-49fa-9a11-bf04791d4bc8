package cec.jiutian.bc.generalModeler.domain.test.model;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;


@FabosJson(
        name = "组合模型p1"
)
@Table(name = "split_model_full"
)
@Entity
@Getter
@Setter
public class SplitModelP1 extends MetaModel {
    @FabosJsonField(
            views = @View(title = "编码"),
            edit = @Edit(title = "编码", readonly = @Readonly(add = false, edit = true), notNull = true, search = @Search(vague = true))
    )
    private String namingRuleCode;

    @FabosJsonField(
            views = @View(title = "显示名称"),
            edit = @Edit(title = "显示名称", notNull = true, search = @Search(vague = true))
    )
    private String displayName;

    @FabosJsonField(
            edit = @Edit(title = "说明", notNull = false,
                    type = EditType.TEXTAREA)
    )
    private String description;

}
