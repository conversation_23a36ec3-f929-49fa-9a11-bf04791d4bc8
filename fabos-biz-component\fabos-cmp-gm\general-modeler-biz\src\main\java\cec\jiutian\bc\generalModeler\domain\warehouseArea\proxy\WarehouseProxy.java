package cec.jiutian.bc.generalModeler.domain.warehouseArea.proxy;


import cec.jiutian.bc.generalModeler.domain.warehouseArea.model.Warehouse;
import cec.jiutian.bc.generalModeler.enumeration.WarehouseStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Component
public class WarehouseProxy implements DataProxy<Warehouse> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void afterAdd(Warehouse warehouse) {
        if (!CollectionUtils.isEmpty(warehouse.getWarehouseDetails())) {
            warehouse.getWarehouseDetails().forEach(d -> {
                d.setWarehouseRelationId(warehouse.getId());
                d.setWarehouse(warehouse);
                fabosJsonDao.mergeAndFlush(d);
            });
        }

    }

    private void handlerDetail(Warehouse entity) {
        if (!CollectionUtils.isEmpty(entity.getWarehouseDetails())) {
            entity.getWarehouseDetails().forEach(d -> d.setWarehouseRelationId(entity.getId()));
        }
    }

    @Override
    public void beforeAdd(Warehouse warehouse) {
        //唯一性验证友好提示
        CriteriaBuilder criteriaBuilder = fabosJsonDao.getEntityManager().getCriteriaBuilder();
        CriteriaQuery<Warehouse> criteriaQuery = criteriaBuilder.createQuery(Warehouse.class);
        Root<Warehouse> warehouseRoot = criteriaQuery.from(Warehouse.class);
        criteriaQuery.where(
                criteriaBuilder.or(
                        criteriaBuilder.equal(warehouseRoot.get("code"), warehouse.getCode()),
                        criteriaBuilder.equal(warehouseRoot.get("name"), warehouse.getName())
                )
        );
        List<Warehouse> resultList = fabosJsonDao.getEntityManager().createQuery(criteriaQuery).getResultList();
        if (!resultList.isEmpty()) {
            throw new FabosJsonApiErrorTip("编码或者名称已存在");
        }
        warehouse.setLockState(WarehouseStateEnum.Enum.Normal.name());
//        handlerDetail(warehouse);
    }

    @Override
    public void beforeUpdate(Warehouse warehouse) {
        handlerDetail(warehouse);
    }

    @Override
    public void beforeDelete(Warehouse warehouse) {
        if (warehouse.getLockState().equals(WarehouseStateEnum.Enum.Locked.name())) {
            throw new FabosJsonApiErrorTip("已锁定仓库不可删除");
        }
        // todo 校验库存是否存在
    }


}
