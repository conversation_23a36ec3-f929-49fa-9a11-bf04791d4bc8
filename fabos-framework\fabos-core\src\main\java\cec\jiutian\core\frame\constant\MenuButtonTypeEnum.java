package cec.jiutian.core.frame.constant;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
public enum MenuButtonTypeEnum {
    DEFAULT("default","默认"),
    JUMP("jump","跳转")
    // 20250721删除混入，菜单按钮上午混入的设置，混入需要设置类型为默认
//    MIX("mix","混入")

    ;

    private String type;

    private String des;

    MenuButtonTypeEnum(String type, String desc) {
        this.type = type;
        this.des = desc;
    }

    public static class ChoiceFetch implements ChoiceFetchHandler {
        @Override
        public List<VLModel> fetch(String[] params) {
            return Stream.of(MenuButtonTypeEnum.values()).map(buttonTypeEnum ->
                    new VLModel(buttonTypeEnum.getType(), buttonTypeEnum.getDes())).collect(Collectors.toList());
        }

    }
}
