package cec.jiutian.bc.generalModeler.domain.material.model;

import cec.jiutian.bc.generalModeler.enumeration.StockTypeEnum;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceTreeType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.ConstraintMode;
import jakarta.persistence.Entity;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;


/* 依赖的视图创建语句
CREATE
	OR REPLACE VIEW stock_category_view AS SELECT
	id,
	CODE,
	NAME,
	DESCIPTION,
	PARENT_CATEGORY_ID,
	'MATERIAL' AS category_type
FROM
	MATERIAL_CATEGORY UNION
SELECT
	id,
	CODE,
	NAME,
	DESCIPTION,
	PARENT_CATEGORY_ID,
	'PRODUCT' AS category_type
FROM
	PRODUCT_CATEGORY;
 */

@FabosJson(
        name = "存货类别模型视图",
        power = @Power(add = false, edit = false, delete = false, importable = false, export = false)
)
@Table(name = "stock_category_view"
)
@Entity
@Getter
@Setter
public class StockCategoryView {

    @Id
    @FabosJsonField(
            views = @View(title = "id"),
            edit = @Edit(title = "id")
    )
    private String id;

    @FabosJsonField(
            views = @View(title = "类别"),
            edit = @Edit(title = "类别", notNull = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = StockTypeEnum.class)
            )
    )
    private String categoryType;

    @FabosJsonField(
            views = @View(title = "分类代码", show = true),
            edit = @Edit(title = "分类代码", search = @Search(vague = true))
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "分类名称"),
            edit = @Edit(title = "分类名称", notNull = true, search = @Search(vague = true))
    )
    private String name;

    @ManyToOne
    @JoinColumn(foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            edit = @Edit(
                    title = "上级分类代码",
                    type = EditType.REFERENCE_TREE,
                    notNull = false,
                    referenceTreeType = @ReferenceTreeType(pid = "parentCategory.id", expandLevel = 1)
            )
    )
    private StockCategoryView parentCategory;


    @FabosJsonField(
            edit = @Edit(title = "描述", notNull = false,
                    type = EditType.TEXTAREA, inputType = @InputType(length = 400))
    )
    private String desciption;
}
