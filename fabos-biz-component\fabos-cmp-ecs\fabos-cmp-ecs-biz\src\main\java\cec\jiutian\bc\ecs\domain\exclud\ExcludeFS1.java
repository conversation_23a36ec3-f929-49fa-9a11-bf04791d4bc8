//package cec.jiutian.bc.ecs.domain.exclud;
//
//import cec.jiutian.core.view.fabosJson.FabosJson;
//import cec.jiutian.core.view.fabosJson.InheritStrategy;
//import cec.jiutian.view.FabosJsonField;
//import cec.jiutian.view.FabosJsonI18n;
//import cec.jiutian.view.field.Edit;
//import cec.jiutian.view.field.View;
//import cec.jiutian.view.field.edit.Search;
//import cec.jiutian.view.type.Power;
//import jakarta.persistence.Entity;
//import jakarta.persistence.Table;
//import lombok.Getter;
//import lombok.Setter;
//
//@Entity
//@Table(name = "ecs_exclude_fields")
//@Getter
//@Setter
//@FabosJson(name = "字段继承测试",
//        orderBy = "ExcludeFS1.createTime desc",
//        power = @Power(add = false, edit = false, delete = false, export = true, importable = false, viewDetails = false)
//)
//@FabosJsonI18n
//@InheritStrategy(
//        excludeParentFields = {"age", "name"}
//)
//public class ExcludeFS1 extends ExcludeFiledsTestP {
//
//
//    @FabosJsonField(
//            views = @View(title = "6666"),
//            edit = @Edit(title = "性别", search = @Search(vague = true))
//    )
//    private String sex;
//
//    @FabosJsonField(
//            views = @View(title = "测试"),
//            edit = @Edit(title = "测试", search = @Search(vague = true))
//    )
//    private String test;
//}
