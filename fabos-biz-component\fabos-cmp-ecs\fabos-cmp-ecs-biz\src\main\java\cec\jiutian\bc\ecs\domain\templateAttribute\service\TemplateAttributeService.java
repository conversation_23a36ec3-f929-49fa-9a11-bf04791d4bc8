package cec.jiutian.bc.ecs.domain.templateAttribute.service;

import cec.jiutian.bc.ecs.domain.templateAttribute.entity.TemplateAttribute;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Service
@AllArgsConstructor
public class TemplateAttributeService {
    private final FabosJsonDao fabosJsonDao;

    public List<TemplateAttribute> getByTemplateId(String templateId) {
        return fabosJsonDao.queryEntityList(TemplateAttribute.class, "template.id = :templateId", new HashMap<String, Object>(1) {{
            this.put("templateId", templateId);
        }});
    }
}
