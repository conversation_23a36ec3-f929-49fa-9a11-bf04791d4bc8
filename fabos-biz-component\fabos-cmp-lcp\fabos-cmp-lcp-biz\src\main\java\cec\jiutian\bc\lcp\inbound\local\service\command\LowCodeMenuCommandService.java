package cec.jiutian.bc.lcp.inbound.local.service.command;

import cec.jiutian.bc.lcp.inbound.remote.dto.ReleaseDTO;
import cec.jiutian.bc.lcp.outbound.adapter.client.MenuClient;
import cec.jiutian.bc.urm.dto.MenuDTO;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.bc.lcp.domain.lowCodeMenu.entity.LowCodeMenu;
import cec.jiutian.bc.lcp.domain.lowCodeMenu.service.LowCodeMenuService;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.service.FabosJsonSessionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;


@Service
public class LowCodeMenuCommandService {

    private final LowCodeMenuService lowCodeMenuService;

    private final FabosJsonSessionService sessionService;

    private final MenuClient menuClient;

    public LowCodeMenuCommandService(LowCodeMenuService lowCodeMenuService, FabosJsonSessionService sessionService, MenuClient menuClient) {
        this.lowCodeMenuService = lowCodeMenuService;
        this.sessionService = sessionService;
        this.menuClient = menuClient;
    }


    @Transactional
    public Object releaseLowCodeMenu(ReleaseDTO releaseDTO) {
        LowCodeMenu menu = lowCodeMenuService.getById(releaseDTO.getLowCodeId());
        lowCodeMenuService.checkReleaseLowCodeMenu(menu);
        MenuDTO dto = new MenuDTO();
        BeanUtils.copyProperties(menu, dto);
        String parentId = menuClient.saveOrUpdateLowCodeMenu(dto);
        //button
        List<MenuDTO> buttons = new ArrayList<>();
        AtomicInteger index= new AtomicInteger(1);
        releaseDTO.getButtons().forEach(d->{
            MenuDTO menuDTO = new MenuDTO();
            BeanUtils.copyProperties(menu, menuDTO);
            menuDTO.setParentId(parentId);
            menuDTO.setName(d.getName());
            menuDTO.setModuleValue(d.getModuleValue());
            menuDTO.setFabosJson(null);
            menuDTO.setModuleCode(menu.generateCode(8));
            menuDTO.setSequenceNumber(index.get());
            index.getAndIncrement();
            buttons.add(menuDTO);
        });
        try {
            menuClient.saveOrUpdateLowCodeButton(buttons,parentId);
        }catch (Exception e){
            throw new FabosJsonApiErrorTip(e.getCause().getCause().getMessage());
        }
        lowCodeMenuService.releaseLowCodeMenu(menu);
        return true;
    }
}
