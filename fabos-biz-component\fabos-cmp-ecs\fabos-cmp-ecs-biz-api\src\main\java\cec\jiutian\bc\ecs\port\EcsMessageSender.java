package cec.jiutian.bc.ecs.port;

import cec.jiutian.bc.ecs.config.EcsMessageRabbitMQConfig;
import cec.jiutian.bc.ecs.dto.SendMsgGroupDTO;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.common.util.JacksonUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import jakarta.annotation.Resource;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.stereotype.Component;

@Component
public class EcsMessageSender {
    @Resource
    private AmqpTemplate rabbitTemplate;

    @Resource
    private EcsMessageRabbitMQConfig ecsMessageRabbitMQConfig;

    public void sendGroupMessage(SendMsgGroupDTO sendMsgGroupDTO) {
        String message = null;
        try {
            message = JacksonUtil.toJson(sendMsgGroupDTO);
        } catch (JsonProcessingException e) {
            throw new ServiceException(e);
        }
        rabbitTemplate.convertAndSend(ecsMessageRabbitMQConfig.getGroupQueueName(), message);
    }
}
