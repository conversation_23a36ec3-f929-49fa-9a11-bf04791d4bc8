package cec.jiutian.bc.infra.remote;

import cec.jiutian.bc.ecs.domain.generalIp.service.GeneralIpService;
import cec.jiutian.bc.ecs.domain.message.service.MessageService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 接收外部系统消息feign接口
 * <AUTHOR>
 * @date 2023/6/12
 */
@RestController
@RequestMapping("/remote")
public class MessageRequestApi {

    private final MessageService messageService;
    private final GeneralIpService generalIpService;

    public MessageRequestApi(MessageService messageService, GeneralIpService generalIpService) {
        this.messageService = messageService;
        this.generalIpService = generalIpService;
    }

//    @PostMapping("/message/receiveMsg")
//    public Boolean receiveMsg(@RequestBody MessageCreateDTO messageCreateDTO) {
////        generalIpService.validateSystem(messageCreateDTO.getSysName(), messageCreateDTO.getAuthKey());
//        messageService.messageCreate(messageCreateDTO);
//        return true;
//    }

//    @PostMapping("/message/sendMsgByEmail")
//    public Boolean sendMsgByEmail(@RequestBody MessageCreateDTO messageCreateDTO) {
//        messageService.sendMessageByEmail(messageCreateDTO);
//        return true;
//    }
}
