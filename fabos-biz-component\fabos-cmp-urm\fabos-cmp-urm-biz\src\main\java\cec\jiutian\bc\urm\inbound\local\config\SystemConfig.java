package cec.jiutian.bc.urm.inbound.local.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Configuration
@Data
public class SystemConfig {
    /**
     * 三员或者超级管理员   superUser  3rm   RightManagementPolicyEnum
     */
//    @Value("${system.manage.mode:superUser}")
//    private String systemMode;
    //超级管理员模式下配置
    @Value("${system.manage.super-account:superUser}")
    private String superUserName;
    @Value("${system.manage.super-password:e10adc3949ba59abbe56e057f20f883e}")
    private String superUserPassword;
    @Value("${system.manage.super-phone:***********}")
    private String superPhone;
    @Value("${system.manage.super-email:<EMAIL>}")
    private String superEmail;
    //超级管理员拥有所有权限  不做配置
//    @Value("${system.manage.super-permission:<EMAIL>}")
//    private String superPermission;

    //系统管理员  三员模式下配置
//    @Value("${system.manage.system-account:systemUser}")
//    private String systemUserName;
//    @Value("${system.manage.system-password:e10adc3949ba59abbe56e057f20f883e}")
//    private String systemPassword;
//    @Value("${system.manage.system-phone:***********}")
//    private String systemPhone;
//    @Value("${system.manage.system-email:<EMAIL>}")
//    private String systemEmail;
    @Value("${system.manage.system-permission}")
    private String systemPermission;

    //安全员 三员模式下配置
//    @Value("${system.manage.security-account:securityUser}")
//    private String securityUserName;
//    @Value("${system.manage.security-password:e10adc3949ba59abbe56e057f20f883e}")
//    private String securityPassword;
//    @Value("${system.manage.security-phone:***********}")
//    private String securityPhone;
//    @Value("${system.manage.security-email:<EMAIL>}")
//    private String securityEmail;
    @Value("${system.manage.security-permission}")
    private String securityPermission;
    //审计员  三员模式下配置
//    @Value("${system.manage.audit-account:auditUser}")
//    private String auditUserName;
//    @Value("${system.manage.audit-password:e10adc3949ba59abbe56e057f20f883e}")
//    private String auditPassword;
//    @Value("${system.manage.audit-phone:***********}")
//    private String auditPhone;
//    @Value("${system.manage.audit-email:<EMAIL>}")
//    private String auditEmail;
    @Value("${system.manage.audit-permission}")
    private String auditPermission;

//    @Value("${system.platform.components:fabos-cmp-urm}")
//    private String components;
    public List<String> getAuditPermissionList() {
        return Arrays.asList(auditPermission.split(","));
    }

    public List<String> getSystemPermissionList() {
        return Arrays.asList(systemPermission.split(","));
    }

    public List<String> getSecurityPermissionList() {
        return Arrays.asList(securityPermission.split(","));
    }
}
