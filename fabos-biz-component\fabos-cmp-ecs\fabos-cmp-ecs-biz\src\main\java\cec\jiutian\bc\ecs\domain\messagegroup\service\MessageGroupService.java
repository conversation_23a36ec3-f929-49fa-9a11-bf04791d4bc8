package cec.jiutian.bc.ecs.domain.messagegroup.service;

import cec.jiutian.bc.ecs.domain.messagegroup.entity.MessageGroup;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @description:
 */
@Service
@AllArgsConstructor
public class MessageGroupService {
    private final FabosJsonDao fabosJsonDao;

    public MessageGroup getById(String id){
        return fabosJsonDao.findById(MessageGroup.class, id);
    }

    public MessageGroup getByCode(String code) {
        return fabosJsonDao.queryEntity(MessageGroup.class, "groupCode=:groupCode", new HashMap<>(4) {{
            this.put("groupCode", code);
        }});

    }

    public HashMap<String, Object> getMessageGroupList(int pageNum, int pageSize) {
        EntityManager entityManager = fabosJsonDao.getEntityManager();
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<MessageGroup> cq = cb.createQuery(MessageGroup.class);
        Root<MessageGroup> messageGroupRoot = cq.from(MessageGroup.class);
        cq.orderBy(cb.desc(messageGroupRoot.get("createTime")));
        cq.select(messageGroupRoot);
        TypedQuery<MessageGroup> query = entityManager.createQuery(cq);
        query.setFirstResult((pageNum - 1) * pageSize); // 设置起始位置
        query.setMaxResults(pageSize); // 设置每页大小

        CriteriaBuilder cbc = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> countCq = cb.createQuery(Long.class);
        Root<MessageGroup> gIpRoot = countCq.from(MessageGroup.class);
        // 如果有筛选条件，这里应该添加相应的 Predicate
        countCq.select(cbc.count(gIpRoot));
        TypedQuery<Long> countQuery = entityManager.createQuery(countCq);

        HashMap<String, Object> res = new HashMap<>();
        res.put("perPage", pageSize);
        res.put("page", pageNum);
        res.put("total", countQuery.getSingleResult());
        res.put("list", query.getResultList());

        return res;
    }

    public void sendMsgByGroup(String groupId) {
        MessageGroup messageGroup = fabosJsonDao.findById(MessageGroup.class, groupId);


    }
}
