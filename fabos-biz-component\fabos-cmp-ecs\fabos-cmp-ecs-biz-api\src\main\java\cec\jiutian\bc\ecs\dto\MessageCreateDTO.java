package cec.jiutian.bc.ecs.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/5/22
 */
@Data
@ApiModel("MessageCreateDTO")
public class MessageCreateDTO {

    /**
     * 消息类型编码
     */
    @NotNull
    @ApiModelProperty("预警类型编码")
    private String groupCode;

    /**
     * 消息内容
     */
    @NotNull
    @ApiModelProperty("预警内容")
    private String content;

    @ApiModelProperty("预警参数")
    private String formUrlParameters;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String lastEventComment;
}
