package cec.jiutian.bc.generalModeler.handler;


import cec.jiutian.bc.generalModeler.domain.warehouseArea.model.Warehouse;
import cec.jiutian.bc.generalModeler.service.WarehouseAreaService;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
public class WarehouseAreaOperationHandler implements OperationHandler<Warehouse, Void> {

    @Resource
    WarehouseAreaService warehouseAreaService;

    //返回值由前端浏览器执行
    @Override
    public String exec(List<Warehouse> data, Void modelObject, String[] param) {
        return null;
    }
}

