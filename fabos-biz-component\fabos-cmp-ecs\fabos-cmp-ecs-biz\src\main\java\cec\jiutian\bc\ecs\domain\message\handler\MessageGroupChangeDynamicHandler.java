package cec.jiutian.bc.ecs.domain.message.handler;

import cec.jiutian.bc.ecs.domain.message.entity.Message;
import cec.jiutian.bc.ecs.domain.messagegroup.entity.MessageGroup;
import cec.jiutian.bc.ecs.domain.messagegroup.entity.MessageProcessFormConfig;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;


@Component
public class MessageGroupChangeDynamicHandler implements DependFiled.DynamicHandler<Message> {

    @Resource
    public FabosJsonDao fabosJsonDao;

    @Override
    public Map<String, Object> handle(Message message) {
        Map<String, Object> result = new HashMap<>();
        if (message.getMessageGroup() == null) {
            result.put("title", null);
            result.put("groupLevel", null);
            result.put("upNumber", null);
            result.put("firstWay", null);
            result.put("secondWay", null);
            result.put("thirdWay", null);
            result.put("fourthWay", null);
            result.put("fifthWay", null);
            result.put("firstUsers", null);
            result.put("secondUsers", null);
            result.put("thirdUsers", null);
            result.put("fourthUsers", null);
            result.put("fifthUsers", null);
            result.put("firstWeixinGroup", null);
            result.put("secondWeixinGroup", null);
            result.put("thirdWeixinGroup", null);
            result.put("fourthWeixinGroup", null);
            result.put("fifthWeixinGroup", null);
            result.put("secondIntervalTime", null);
            result.put("thirdIntervalTime", null);
            result.put("fourthIntervalTime", null);
            result.put("fifthIntervalTime", null);
            result.put("isProcess", null);
            result.put("processFormNames", null);
        } else {
            MessageGroup messageGroup = fabosJsonDao.getById(MessageGroup.class, message.getMessageGroup().getId());
            result.put("title", messageGroup.getGroupTitle());
            result.put("groupLevel", messageGroup.getGroupLevel());
            result.put("upNumber", messageGroup.getUpNumber());
            result.put("firstWay", messageGroup.getFirstWay());
            result.put("secondWay", messageGroup.getSecondWay());
            result.put("thirdWay", messageGroup.getThirdWay());
            result.put("fourthWay", messageGroup.getFourthWay());
            result.put("fifthWay", messageGroup.getFifthWay());
            result.put("firstUsers", messageGroup.getFirstUsers());
            result.put("secondUsers", messageGroup.getSecondUsers());
            result.put("thirdUsers", messageGroup.getThirdUsers());
            result.put("fourthUsers", messageGroup.getFourthUsers());
            result.put("fifthUsers", messageGroup.getFifthUsers());
            result.put("firstWeixinGroup", messageGroup.getFirstWeixinGroup());
            result.put("secondWeixinGroup", messageGroup.getSecondWeixinGroup());
            result.put("thirdWeixinGroup", messageGroup.getThirdWeixinGroup());
            result.put("fourthWeixinGroup", messageGroup.getFourthWeixinGroup());
            result.put("fifthWeixinGroup", messageGroup.getFifthWeixinGroup());
            result.put("secondIntervalTime", messageGroup.getSecondIntervalTime());
            result.put("thirdIntervalTime", messageGroup.getThirdIntervalTime());
            result.put("fourthIntervalTime", messageGroup.getFourthIntervalTime());
            result.put("fifthIntervalTime", messageGroup.getFifthIntervalTime());
            result.put("isProcess", messageGroup.getIsProcess());
            result.put("processFormNames", messageGroup.getMessageProcessFormConfigs()
                    .stream()
                    .map(MessageProcessFormConfig::getName)
                    .collect(Collectors.joining(",")));
        }
        return result;
    }
}
