UserController.login(account, encryptedInputPassword, verifyCode) {
   UserCommandService.logIn(account, password, verifyCode) {
      UserService.checkVerifyCode(account, verifyCode){
         return passResult;
         if (passResult != true){
            return "验证码错误，登录失败！【RETURN】";
         }
      }
      UserService.decodePassword(encryptedInputPassword){
      return decrypedInputPassword;}
      SuperAdminProvider.checkSuperAdmin(account, decrypedInputPassword){
                SuperAdmin.findByAccount(account) {return SuperAdmin}
                if (SuperAdmin == null) {
                return "SuperAdmin = null 【BREAK】"}
                SuperAdmin.checkPassword() {return ifcorrect}
                if (ifcorrect == false) {
                return "密码错误！【RETURN】"}
                SuperAdmin.checkState() {return ifActive}
                if (ifActive == false){
                return "当前超级管理员不允许登录！【RETURN】"}
                return SuperAdmin;
      }
      if (SuperAdmin == null){
      TenantAdminProvider.checkTenantAdmin(account, decrypedInputPassword){
          TenantAdmin.findByAccount(account) {return TenantAdmin}
          if (TenantAdmin == null) {
          return "TenantAdmin = null 【BREAK】"}
          TenantAdmin.checkPassword() {return ifcorrect}
          if (ifcorrect == false) {
          return "密码错误！【RETURN】"}
          TenantAdmin.checkState() {return ifActive}
          if (ifActive == false){
          return "当前租户管理员不允许登录！【RETURN】"}
          return TenantAdmin;
      }
      }
      if (SuperAdmin != null){UserService.convertFromSuperAdmin(SuperAdmin){return User}}
      else if(TenantAdmin != null) {UserService.convertFromTenantAdmin(TenantAdmin){return User}}
      else {UserService.getUserByAccount(account){return User}}
      buildMenu(){
        if (User.isAdmin == True){
          MenuQueryService.getAllMenu(){
          return menuList
          }
        }else {RoleQuerySerivce.getMenuByUser(User){
          return menuList}
       }
       FabosJsonPowerUtil.generateCode(16){return tocken}
       buildUserSession(User, menuList, tocken){
       return savedUserSession}
       buildLoginModel(){return loginModel}
     }
return loginModel
}
return loginModel
}