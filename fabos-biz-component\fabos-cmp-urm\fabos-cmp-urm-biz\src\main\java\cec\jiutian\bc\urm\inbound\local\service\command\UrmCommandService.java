package cec.jiutian.bc.urm.inbound.local.service.command;

import cec.jiutian.bc.urm.domain.menu.entity.Menu;
import cec.jiutian.bc.urm.domain.menu.service.MenuService;
import cec.jiutian.bc.urm.domain.menu.service.query.MenuQueryDTO;
import cec.jiutian.bc.urm.domain.role.entity.Role;
import cec.jiutian.bc.urm.domain.tenant.constant.RightManagementPolicyEnum;
import cec.jiutian.bc.urm.domain.tenant.entity.Tenant;
import cec.jiutian.bc.urm.domain.tenant.service.TenantService;
import cec.jiutian.bc.urm.domain.user.entity.FabosJsonUserConst;
import cec.jiutian.bc.urm.domain.user.entity.LoginModel;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.bc.urm.domain.user.entity.UserinfoVo;
import cec.jiutian.bc.urm.domain.user.service.LoginProxy;
import cec.jiutian.bc.urm.domain.user.service.UserService;
import cec.jiutian.bc.urm.dto.MenuDTO;
import cec.jiutian.bc.urm.dto.MenuData;
import cec.jiutian.bc.urm.dto.MetaUserinfo;
import cec.jiutian.bc.urm.inbound.local.config.SystemConfig;
import cec.jiutian.bc.urm.inbound.local.context.SecurityConfigContext;
import cec.jiutian.common.constant.SessionKey;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.util.JacksonUtil;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.constant.MenuStatus;
import cec.jiutian.core.frame.constant.SwitchStatus;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.exception.FabosJsonWebApiRuntimeException;
import cec.jiutian.core.frame.util.FabosJsonPowerUtil;
import cec.jiutian.core.frame.util.PassWordUtil;
import cec.jiutian.core.prop.FabosJsonAppProp;
import cec.jiutian.core.prop.FabosJsonProp;
import cec.jiutian.core.prop.FabosJsonUpmsProp;
import cec.jiutian.core.service.FabosJsonSessionService;
import cec.jiutian.core.view.fabosJson.view.FabosJsonApiModel;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.meta.core.util.MD5Util;
import com.fasterxml.jackson.core.JsonProcessingException;
import jakarta.servlet.http.HttpServletRequest;
import kong.unirest.HttpResponse;
import kong.unirest.JsonNode;
import kong.unirest.Unirest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cec.jiutian.common.constant.SessionKey.FIELD_PERMISSIONS;
import static cec.jiutian.common.constant.SessionKey.ROW_PERMISSIONS;

@Service
@Slf4j
public class UrmCommandService {

    private final MenuService menuService;

    private final FabosJsonSessionService sessionService;

    private final UserService userService;
    private final HttpServletRequest request;
    private final FabosJsonUpmsProp fabosJsonUpmsProp;
    private final FabosJsonProp fabosJsonProp;
    private final FabosJsonAppProp fabosJsonAppProp;
    private final FabosJsonContextService contextService;
    private final TenantService tenantService;
    private final JpaCrud jpaCrud;
    private final RowColumnPermissionService rowColumnPermissionService;
    @Value("${spring.application.name}")
    private String currentService = "";


    public UrmCommandService(MenuService menuService, FabosJsonSessionService sessionService, UserService userService, HttpServletRequest request, FabosJsonUpmsProp fabosJsonUpmsProp, FabosJsonProp fabosJsonProp, FabosJsonAppProp fabosJsonAppProp, FabosJsonContextService contextService, TenantService tenantService, JpaCrud jpaCrud, RowColumnPermissionService columnPermissionService, SystemConfig systemConfig) {
        this.menuService = menuService;
        this.sessionService = sessionService;
        this.userService = userService;
        this.request = request;
        this.fabosJsonUpmsProp = fabosJsonUpmsProp;
        this.fabosJsonProp = fabosJsonProp;
        this.fabosJsonAppProp = fabosJsonAppProp;
        this.contextService = contextService;
        this.tenantService = tenantService;
        this.jpaCrud = jpaCrud;
        this.rowColumnPermissionService = columnPermissionService;
    }

    public String getModuleUrlById(MenuQueryDTO queryDTO) {
        return menuService.getModuleUrlById(queryDTO);
    }

    public FabosJsonApiModel getAllMenuList() {
        MetaUserinfo userinfo = sessionService.getSimpleUserInfo();
        if (userinfo == null) {
            return FabosJsonApiModel.errorApi("当前用户未登录！");
        }
        List<MenuData> menus = sessionService.fromJsonToList(SessionKey.MENU_VIEW + sessionService.getCurrentToken(), MenuData.class);
//        menus = filterHiddenMenus(menus);
        return FabosJsonApiModel.successApi(menus);
    }

    public static List<MenuData> filterHiddenMenus(List<MenuData> menus) {
        return menus.stream()
                .filter(menu -> !menu.getHide())
                .map(menu -> new MenuData(
                        menu.getId(), menu.getCode(),menu.getIcon(),
                        menu.getType(), menu.getOid(), menu.getFabosJson(),
                        menu.getPath(), menu.getValue(), menu.getName(),
                        menu.getHide(), menu.getSort(),
                        filterHiddenMenus(menu.getChildren()),
                        menu.getPageId(), menu.getJumpType(),menu.getHref(),
                        menu.getButtonType(), menu.getRouterType(),menu.getShowType(),
                        menu.getSubType(),menu.getTerminalType(),menu.getButtonStyle(),
                        menu.getOnlyShowIcon(),menu.getPageType()
                ))
                .collect(Collectors.toList());
    }

    public List<Menu> getModuleListByParentId(MenuQueryDTO queryDTO) {
        return menuService.getModuleListByParentId(queryDTO.getId());
    }

    public List<MenuData> getUserAllMenu(MetaUserinfo user) {
        //查询激活的组件 （激活组件正常情况下不可能为空）
//        List<String> activeBizs = BaseRedisCacheUtil.range(RedisKeyEnum.ACTIVE_BIZ.getKey());
//        if (null == user || CollectionUtils.isEmpty(activeBizs)) {
//            return new ArrayList<>();
//        }

//        List<String> components = user.getComponents();
//        if (CollectionUtils.isEmpty(components)) {
//            return new ArrayList<>();
//        }
//        HashSet<String> actived = activeComponentFilter(activeBizs, components);

//        if (user.isAdmin()) {
//            return filterModuleMenu(menuService.getAllMenuList());
//        } else {
//            return filterMenu(user.getMenus(), menuService.getAllMenuList());
//        }

        return filterMenu(user.getMenus(), menuService.getAllMenuList());
    }

    private HashSet<String> activeComponentFilter(List<String> activeBizs, List<String> userComponents) {
//        HashSet<String> bizs = new HashSet<>(activeBizs);
//        HashSet<String> ownActiveComponents = new HashSet<>();
//        for (String component : userComponents) {
//            if (bizs.contains(component)) {
//                ownActiveComponents.add(component);
//            }
//        }
//        return ownActiveComponents;
        //TODO:暂时不进行过滤 测试环境混乱，过滤会导致菜单不显示
        return new HashSet<>(userComponents);
    }

    public List<String> getUserAllMenuId(User user) {
        List<Menu> menuList = null;
        if (null != user && user.getIsAdmin()) {
            menuList = menuService.getAdminMenuList();
        } else {
            List<Role> roles = user.getRoles().stream().filter(Role::getStatus).collect(Collectors.toList());
            List<Menu> finalMenuList = new ArrayList<>();
            roles.forEach(it -> finalMenuList.addAll(it.getMenus()));
            Integer notDisable = Integer.valueOf(MenuStatus.DISABLE.getValue());
            menuList = finalMenuList.stream().filter(it -> (!notDisable.equals(it.getStatus()))).collect(Collectors.toList());
        }
        return menuList.stream().map(Menu::getId).distinct().collect(Collectors.toList());
    }

    private List<MenuData> filterMenu(List<String> userMenuIds, List<MenuData> menuDataList) {
        List<MenuData> list = new ArrayList<>();
        for (MenuData menuData : menuDataList) {
            if (userMenuIds.contains(menuData.getId())) {
                buildMenuTree(userMenuIds, menuData);
                list.add(menuData);
            }
        }
        return list;
    }

    private List<MenuData> filterModuleMenu(List<MenuData> menuDataList) {
        List<MenuData> list = new ArrayList<>();
        for (MenuData menuData : menuDataList) {
            buildModuleMenuTree(menuData);
            list.add(menuData);
        }
        return list;
    }

    public void buildMenuTree(List<String> userMenuIds, MenuData parentMenuData) {
        List<MenuData> menuDataList = parentMenuData.getChildren();
        if (CollectionUtils.isEmpty(menuDataList)) {
            return;
        }
        ArrayList<MenuData> childList = new ArrayList<>();
        for (MenuData menuData : menuDataList) {
            if (userMenuIds.contains(menuData.getId())) {
                buildMenuTree(userMenuIds, menuData);
                childList.add(menuData);
            }
        }
        parentMenuData.setChildren(childList);
    }


    public void buildModuleMenuTree(MenuData parentMenuData) {
        List<MenuData> menuDataList = parentMenuData.getChildren();
        if (CollectionUtils.isEmpty(menuDataList)) {
            return;
        }
        ArrayList<MenuData> childList = new ArrayList<>();
        for (MenuData menuData : menuDataList) {
            buildModuleMenuTree(menuData);
            childList.add(menuData);
        }
        parentMenuData.setChildren(childList);
    }

    public List<Menu> getUserMenu(User user, MetaUserinfo metaUserinfo) {
        if (metaUserinfo.isAdmin()) {
            return menuService.getAdminMenuList().stream()
                    .filter(it -> it.getApplicationName() == null || it.getApplicationName().trim().isEmpty() || it.getApplicationName().equals(currentService))
                    .collect(Collectors.toList());
        } else {
            Set<Menu> menuSet = new HashSet<>();
            user.getRoles().stream().filter(Role::getStatus).map(Role::getMenus).forEach(menuSet::addAll);
            return menuSet.stream().filter(it -> it.getStatus() != MenuStatus.DISABLE.getValue()).filter(it -> it.getApplicationName() == null || it.getApplicationName().trim().isEmpty() || it.getApplicationName().equals(currentService)).sorted(Comparator.comparing(Menu::getSequenceNumber, Comparator.nullsFirst(Integer::compareTo))).collect(Collectors.toList());
        }
    }

    public List<MenuData> convertMenuListToMenuDataListOptimized(List<Menu> menuList) {
        long startTime = System.currentTimeMillis();
        // 创建一个Map来存储每个Menu的id和对应的MenuData
        Map<String, MenuData> idToMenuDataMap = new HashMap<>(menuList.size()*4/3);

        // 第一次遍历：创建所有MenuData对象，但暂时不设置children
        log.error("MenuSize: {}", menuList.size());
        long time2 = System.currentTimeMillis();
        for (Menu menu : menuList) {
//            long t1 = System.currentTimeMillis();
            idToMenuDataMap.put(menu.getId(), menuService.convertMenuData(menu));
//            long t2 = System.currentTimeMillis();
//            log.info("创建MenuData耗时：{}ms", (t2-t1));
        }
        long time3 = System.currentTimeMillis();
        log.info("菜单转换耗时：{}ms", (time3-time2));

        // 用于跟踪有父节点的Menu
        Set<String> childrenIds = new HashSet<>();

        // 第二次遍历：设置children关系
        for (Menu menu : menuList) {
            if (menu.getParent() != null) {
                MenuData parentMenuData = idToMenuDataMap.get(menu.getParent().getId());
                if (parentMenuData != null) {
                    parentMenuData.getChildren().add(idToMenuDataMap.get(menu.getId()));
                    childrenIds.add(menu.getId());
                }
            }
        }
        long time4 = System.currentTimeMillis();
        log.info("子菜单生成耗时：{}ms", (time4-time3));
        List<MenuData> collect = menuList.stream()
                .filter(menu -> menu.getParent() == null || !childrenIds.contains(menu.getId()))
                .map(menu -> idToMenuDataMap.get(menu.getId()))
                .collect(Collectors.toList());
        // 返回所有顶级菜单和没有父子关系的独立菜单
        long endTime = System.currentTimeMillis();
        log.info("返回有效菜单过滤耗时：{}ms",(endTime-time4));
        log.info("菜单树生成耗时：{}s",(endTime-startTime)/1000);
        return collect;

    }

    public String saveOrUpdateLowCodeMenu(MenuDTO dto) {
        return menuService.saveOrUpdateLowCodeMenu(dto);
    }

    public Boolean saveOrUpdateLowCodeButton(List<MenuDTO> dtos, String parentId) {
        menuService.saveOrUpdateLowCodeButton(dtos, parentId);
        this.flushCache();
        return true;
    }

    public boolean checkToLogin(String account) {
        return userService.checkToLogin(account);
    }

    public LoginModel login(String account, String password, String verifyCode, String phone) {
        StopWatch stopWatch = new StopWatch("登录");
        stopWatch.start("校验凭证、检查账户权限及可用性");
//        boolean isMobile = checkRequestIsFromMobile();
        LoginModel loginModel = userService.login(account, password, verifyCode, phone);
        stopWatch.stop();
        if (loginModel.isPass()) {
            stopWatch.start("刷新用户信息缓存");
            request.getSession().invalidate();
            loginModel.setToken(FabosJsonPowerUtil.generateCode(16));
            loginModel.setExpire(userService.getExpireTime());
            loginModel.setDefaultPwd(MD5Util.digest(FabosJsonUserConst.DEFAULT_USER_PASSWORD).equals(loginModel.getUser().getPassword()));
//            if (null != loginProxy) loginProxy.loginSuccess(user, loginModel.getToken());
            // 设置token，有效期为当前配置的超时登出（分钟）时间
            sessionService.put(SessionKey.TOKEN_OLINE + loginModel.getToken(), account, SecurityConfigContext.getTimeoutLogoutMinutes(), TimeUnit.MINUTES);
            // 记录当前用户和登录平台，如果开启了「单端登录」，则需要校验其是否在同一平台只有一次登录，其他的token将被迫下线
//            if (SecurityConfigContext.getSingleLoginEnabled()) {
//                // 寻找上次在同一平台登录的token, 并将其下线
//                String lastToken = sessionService.getAsString((isMobile? SessionKey.MOBILE_APP : SessionKey.DESKTOP_BROWSER) + account);
//                if (StringUtils.isNotBlank(lastToken)) {
//                    removeAssociatedKeys(account, isMobile, lastToken);
//                    log.info("于{}检测到用户{}重复登录，已移除之前的会话[token:{}]", isMobile? "移动端" : "PC端", account, lastToken);
//                }
//            }
//            sessionService.put((isMobile ? SessionKey.MOBILE_APP : SessionKey.DESKTOP_BROWSER) + account, loginModel.getToken(), SystemSecrecyConfigEnum.get().getConnectTime(), TimeUnit.MINUTES);
            cacheUserInfo(loginModel.getMetaUserinfo(), loginModel.getUser(), loginModel.getToken());
            loginModel.setResetPwd(!loginModel.getMetaUserinfo().isAdmin() && PassWordUtil.isPasswordExpired(loginModel.getMetaUserinfo().getPasswordUpdateTime()));

            if (fabosJsonProp.isUseOauthLogin()) {
                stopWatch.stop();
                stopWatch.start("请求认证信息");
                Map<String, Object> authMap = requestAuthorizationInfoFromUbp(account, password);
                loginModel.setAuthorizationInfo(authMap);
                stopWatch.stop();
                stopWatch.start("维护token缓存");
                cacheTokenInfoAcrossSystems(loginModel.getMetaUserinfo().getAccount(), loginModel.getToken());
            }
        } else {
            stopWatch.start("锁定用户");
            if (Objects.nonNull(UserContext.get()) && UserContext.get().needForceLock()) {
                User user = loginModel.getUser();
                user.setState(SwitchStatus.DISABLE.getValue());
                user.setLockDate(LocalDateTime.now());
                user.setLockReason("由于身份鉴别失败次数过多，系统自动锁定");
                log.info("用户{}身份鉴别失败次数过多，锁定该用户", user.getAccount());
                jpaCrud.update(user);
            }
        }
        stopWatch.stop();
        stopWatch.start("刷新权限缓存");
        refreshCacheForRoles(loginModel);
        stopWatch.stop();
        log.info(stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        return loginModel;
    }

    /**
     * 向UBP请求认证信息
     *
     * @return 包含access token, refresh token, expire的map
     */
    private Map<String, Object> requestAuthorizationInfoFromUbp(String account, String password) {
        HttpResponse<String> loginResponse = Unirest.post("http://127.0.0.1:8086/login")
                .field("username", account)
                .field("password", password)
                .asString();
        if (Objects.nonNull(loginResponse) && loginResponse.getStatus() == 302) {
            HttpResponse<JsonNode> authResponse = Unirest.get("http://127.0.0.1:8086/oauth2/authorize")
                    .queryString("client_id", "d95771a1-5b4c-4921-bede-4ec86c16d346")
                    .queryString("response_type", "code")
                    .queryString("scope", "all")
                    .queryString("redirect_uri", "http://127.0.0.1:8086/fabos-api/getOAuth2Params")
                    .asJson();
            if (Objects.nonNull(authResponse)
                    && authResponse.isSuccess()) {
                return (Map<String, Object>) authResponse.getBody().getObject().toMap().get("data");
            }
            log.error(authResponse.getBody().toString());
        }
        throw new FabosJsonApiErrorTip("获取授权失败，请稍后重试");
//

//        Unirest.post("http://127.0.0.1:8086/login")
//                .field("username", "superUser")
//                .field("password", "********************************")
//                .asString();
//
//        HttpResponse<String> response = Unirest.get("http://127.0.0.1:8086/oauth2/authorize?client_id=d95771a1-5b4c-4921-bede-4ec86c16d346&response_type=code&scope=all%20openid&redirect_uri=http%3A%2F%2Flocalhost%3A8086%2Ffabos-api%2FgetOAuth2Params")
//                .asString();
//
//        HttpResponse<String> response = Unirest.post("http://*************:9902/oauth2/token")
//                .header("Authorization", "Basic ZDk1NzcxYTEtNWI0Yy00OTIxLWJlZGUtNGVjODZjMTZkMzQ2OjViMjM2NDAzLWIyNmItNDA2Mi1iYTgxLTAxMmJkMzkxZjNmOQ==")
//                .field("code", "Z2wOYGMwH9Fj110v2amPi4FdpcM7qLj1qkM2wbupb2lMhmYUKiXDk6UdC8esilyR7VOfx0LbXQoM-y86Z0Y8MNBmit6XyiulpI7bxzwI4bXGy4yKWlEAH-Hmk1GadNo5")
//                .field("grant_type", "authorization_code")
//                .field("redirect_uri", "http://localhost:8086/fabos-api/getOAuth2Params")
//                .field("scope", "all openid")
//                .field("client_id", "d95771a1-5b4c-4921-bede-4ec86c16d346")
//                .asString();
    }

    /**
     * 维护用户-系统-bizToken的关系（在redis中）
     * 如果已有，则会覆盖
     */
    private void cacheTokenInfoAcrossSystems(String user, String token) {
        String redisKey = SessionKey.ONLINE_USERS + user;
        HashMap<String, Object> map = new HashMap<>();
        sessionService.putMap(redisKey, map, 7 * 24 * 60);
        sessionService.putValueToMap(redisKey, currentService, token);
    }

    /**
     * 执行内部登录逻辑，即由外部系统校验该账户的可用性，此逻辑只做redis刷新相关逻辑
     *
     * @param account 账号
     * @return 登录模型
     */
    public LoginModel performInternalLogin(String account) {
        LoginModel loginModel = userService.getSuccessLoginModel(account);
        request.getSession().invalidate();
        // 这里视情况可以换
        loginModel.setToken("int" + FabosJsonPowerUtil.generateCode(13));
        sessionService.put(SessionKey.TOKEN_OLINE + loginModel.getToken(), account, SecurityConfigContext.getTimeoutLogoutMinutes(), TimeUnit.MINUTES);
        cacheUserInfo(loginModel.getMetaUserinfo(), loginModel.getUser(), loginModel.getToken());
        refreshCacheForRoles(loginModel);
        return loginModel;
    }

    /**
     * 刷新当前用户相关的所有角色的列权限缓存
     *
     * @param model 登录模型
     */
    private void refreshCacheForRoles(LoginModel model) {
        if (Objects.isNull(model)
                || Objects.isNull(model.getMetaUserinfo())
                || CollectionUtils.isEmpty(model.getMetaUserinfo().getRoleIds())) {
            return;
        }
        List<String> roleIds = model.getMetaUserinfo().getRoleIds();
        for (String roleId : roleIds) {
            sessionService.remove(FIELD_PERMISSIONS + roleId);
            sessionService.remove(ROW_PERMISSIONS + roleId);
        }

        Map<String, Map<String, Integer>> retrieved = rowColumnPermissionService.getColumnPermissionFromDb(roleIds);
        retrieved.forEach((roleId, columnPermission) -> {
            try {
                sessionService.put(FIELD_PERMISSIONS + roleId, JacksonUtil.toJson(columnPermission), -1);
            } catch (JsonProcessingException e) {
                log.error(e.getMessage());
                throw new FabosJsonWebApiRuntimeException("权限配置出现错误，请联系管理员");
            }
        });

        // <roleId, <(model) metadataId, filter>>>
        Map<String, Map<String, String>> retrievedRowPermissions = rowColumnPermissionService.getRowPermissionFromDb(roleIds);
        retrievedRowPermissions.forEach((roleId, rowPermission) -> {
            try {
                sessionService.put(ROW_PERMISSIONS + roleId, JacksonUtil.toJson(rowPermission), -1);
            } catch (JsonProcessingException e) {
                log.error(e.getMessage());
                throw new FabosJsonWebApiRuntimeException("权限配置出现错误，请联系管理员");
            }
        });

    }

    private boolean checkRequestIsFromMobile() {
        return request.getHeader("User-Agent").indexOf(fabosJsonProp.getAppLabel()) > 0;
    }

    private LoginModel tenantLogin(MetaUserinfo metaUserinfo, String password) {
        // 校验密码和状态
        if (!userService.checkPassword(metaUserinfo, password)) {
            return new LoginModel(false, UserService.LOGIN_ERROR_HINT, userService.loginErrorCountPlus(metaUserinfo.getAccount()));
        }
        if (!userService.checkState(metaUserinfo.getState())) {
            return new LoginModel(false, "fabosUser_User_status_not_activated", userService.loginErrorCountPlus(metaUserinfo.getAccount()));
        }
        request.getSession().invalidate();
        sessionService.remove(SessionKey.LOGIN_ERROR + metaUserinfo.getAccount());
        User user = new User();
        BeanUtils.copyProperties(metaUserinfo, user);
        user.setOid(metaUserinfo.getTenantId());
        return new LoginModel(true, user, metaUserinfo);
    }

    public void cacheUserInfo(MetaUserinfo metaUserinfo, User user, String token) {
        //此处不能对实体进行修改，修改后会同步到数据库
//        user.setPassword(null);
//        metaUserinfo.setPassword(null);
        List<Menu> menus = getUserMenu(user, metaUserinfo);
        Map<String, Object> valueMap = new HashMap<>();
        for (Menu menu : menus) {
            if (StringUtils.isNotBlank(menu.getModuleValue())) {
                //根据URL规则?后的均是参数如：eruptTest?code=test，把参数 ?code=test 去除
                valueMap.put(menu.getModuleValue().toLowerCase().split("\\?")[0], menu);
            }
        }

        sessionService.putMap(SessionKey.MENU_VALUE_MAP + token, valueMap, SecurityConfigContext.getTimeoutLogoutMinutes() + 1);
        try {
            sessionService.put(SessionKey.MENU_VIEW + token, JacksonUtil.toJson(convertMenuListToMenuDataListOptimized(menus)), SecurityConfigContext.getTimeoutLogoutMinutes() + 1, TimeUnit.MINUTES);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        Tenant tenant = tenantService.getTenant(user.getOid());
        if(tenant != null){
            metaUserinfo.setTenantName(tenant.getName());
            metaUserinfo.setRightManagementPolicy(RightManagementPolicyEnum.SUPER.getValue().equals(tenant.getRightManagementPolicy()) ? RightManagementPolicyEnum.SUPER.getLabel() : RightManagementPolicyEnum.TRIPARTITE.getLabel());
            metaUserinfo.setLogo(tenant.getLogo());
        }
        metaUserinfo.setComponents(tenantService.findModuleNameListById(user.getOid()));
        metaUserinfo.setMenus(getUserAllMenuId(user));
        try {
            sessionService.put(SessionKey.USER_INFO + token, JacksonUtil.toJson(metaUserinfo), SecurityConfigContext.getTimeoutLogoutMinutes() + 1, TimeUnit.MINUTES);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public void flushCache() {
        this.cacheUserInfo(sessionService.getSimpleUserInfo(), userService.getCurrentUser(), sessionService.getCurrentToken());
    }

    public FabosJsonApiModel logout(HttpServletRequest request) {
        String token = contextService.getCurrentToken();
        LoginProxy loginProxy = UserService.findFabosJsonLogin();
        Optional.ofNullable(loginProxy).ifPresent(it -> it.logout(token));
        request.getSession().invalidate();
        String currentAccount = getCurrentAccount();
        removeAssociatedKeys(currentAccount, checkRequestIsFromMobile(), token);
        return FabosJsonApiModel.successApi();
    }

    /**
     * 在退出登录时，删除所有与此账户、token相关的缓存
     *
     * @param currentAccount 用户名
     * @param isMobile       平台是否为移动端，如果为空则表示全部删除
     * @param token          偷啃
     */
    private void removeAssociatedKeys(String currentAccount, Boolean isMobile, String token) {
        if (StringUtils.isNotBlank(currentAccount)) {
            if (Objects.isNull(isMobile)) {
                sessionService.remove(SessionKey.DESKTOP_BROWSER + currentAccount);
                sessionService.remove(SessionKey.MOBILE_APP + currentAccount);
            } else {
                sessionService.remove((isMobile ? SessionKey.MOBILE_APP : SessionKey.DESKTOP_BROWSER) + currentAccount);
            }
        }
        String redisKey = SessionKey.ONLINE_USERS + currentAccount;
        if (fabosJsonProp.isUseOauthLogin()) {
            Map<Object, Object> map = sessionService.getMap(redisKey);
            if (MapUtils.isNotEmpty(map)) {
                sessionService.remove(redisKey);
                List<String> tokensNeedDelete = map.values().stream().map(Object::toString).toList();
                Set<String> keysNeedDelete = new HashSet<>();
                tokensNeedDelete.forEach(t -> {
                    for (String uk : SessionKey.USER_KEY_GROUP) {
                        keysNeedDelete.add(redisKey + uk);
                    }
                });
                sessionService.remove(keysNeedDelete);
            }

        } else {
            for (String uk : SessionKey.USER_KEY_GROUP) {
                sessionService.remove(uk + token);
            }
        }

    }

    public FabosJsonApiModel changePassword(String account, String oldPassword, String newPassword, String confirmPassword) {
//        MetaUserinfo metaUserinfo = tenantClient.getTenantUser(account);
//        if (null != metaUserinfo) {
//            // 租户管理员修改密码
//            tenantClient.changePassword(account,newPassword);
//        } else {
        // 非租户修改密码
//        userService.changePassword(account, oldPassword, newPassword, confirmPassword);
//        }
        return userService.changePassword(account, oldPassword, newPassword, confirmPassword);
    }


    public Boolean resetPassword(String account) {
        MetaUserinfo curr = userService.getSimpleUserInfo();
        if (curr == null) {
            throw new FabosJsonWebApiRuntimeException("current user is not login!");
        }
//        if (!(curr.getRoles().contains(UserManagerTypeEnum.securityManager.getCode())||curr.getRoles().contains(UserManagerTypeEnum.superManager.getCode()))) {
//            throw new FabosJsonWebApiRuntimeException(FabosI18nTranslate.$translate("fabosJson.user.no.auth"));
//        }
        return userService.resetPassword(account);
    }


    public String getCurrentAccount() {
        Object account = sessionService.get(SessionKey.TOKEN_OLINE + contextService.getCurrentToken());
        return null == account ? null : account.toString();
    }

    public UserinfoVo getUserInfo() {
        User user = userService.getCurrentUser();
        UserinfoVo userinfoVo = new UserinfoVo();
        userinfoVo.setNickname(user.getName());
//        userinfoVo.setResetPwd(null == user.getResetPwdTime());
//        Optional.ofNullable(user.getEruptOrg()).ifPresent(it -> userinfoVo.setOrg(it.getCode()));
//        Optional.ofNullable(user.getEruptPost()).ifPresent(it -> userinfoVo.setPost(it.getCode()));
        Optional.ofNullable(user.getRoles()).ifPresent(it -> userinfoVo.setRoles(it.stream().map(Role::getCode).collect(Collectors.toList())));
//        Optional.ofNullable(user.getEruptMenu()).ifPresent(it -> {
//            userinfoVo.setIndexMenuType(it.getType());
//            userinfoVo.setIndexMenuValue(it.getValue());
//        });
        return userinfoVo;
    }


    public MetaUserinfo getMetaUserByAccount(String account) {
        MetaUserinfo metaUserinfo = userService.getMetaUserByAccount(account);
//        if(metaUserinfo==null){
//            metaUserinfo= tenantClient.getTenantUser(account);
//        }
        return metaUserinfo;
    }

    public MetaUserinfo getMetaUserByPhoneNumber(String phoneNumber) {
        return userService.getMetaUserByPhoneNumber(phoneNumber);
    }


    public List<MetaUserinfo> getMetaUserByPhoneNumbers(List<String> phoneNumbers) {
        return userService.getMetaUserByPhoneNumbers(phoneNumbers);
    }

    public List<MetaUserinfo> getUserByRoleIds(List<String> items) {
        return userService.getUserByRoleIds(items);
    }

//    public AuthorizationCheckDTO checkToken(String token, String account) {
//        AuthorizationCheckDTO authorizationCheckDTO = new AuthorizationCheckDTO();
//        String key = SessionKey.TOKEN_OLINE + token;
//        authorizationCheckDTO.setCheckTokenExpire(sessionService.get(key) == null);
//        authorizationCheckDTO.setCheckPassWordExpire(false);
//        return authorizationCheckDTO;
//    }

    /**
     * 检查此token是仍在线
     *
     * @param token 偷啃
     * @return 如果在线，则返回true
     */
    public boolean checkTokenOnline(String token) {
        String key = SessionKey.TOKEN_OLINE + token;
        return Objects.nonNull(sessionService.get(key));
    }

    public List<MenuData> getRemoveButtonMenuList(){
        return menuService.getRemoveButtonMenuList();
    }

    public void removeAllTokens() {
//        sessionService.removeAll(SessionKey.ONLINE_USERS);
//        for (String uk : SessionKey.USER_KEY_GROUP) {
//            sessionService.removeAll(uk);
//        }
    }

    public Menu getByModuleValue(MenuQueryDTO queryDTO) {
        if (StringUtils.isBlank(queryDTO.getModuleValue())) {
            return null;
        }
        return menuService.getByModuleValue(queryDTO.getModuleValue());
    }
}
