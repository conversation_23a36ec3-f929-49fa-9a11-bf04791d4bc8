package cec.jiutian.bc.lcp.inbound.remote.controller;

import cec.jiutian.bc.lcp.inbound.local.service.command.LowCodeMenuCommandService;
import cec.jiutian.bc.lcp.inbound.remote.dto.ReleaseDTO;
import cec.jiutian.bc.lcp.domain.lowCodeMenu.entity.LowCodeMenu;
import cec.jiutian.bc.lcp.domain.lowCodeMenu.service.query.LowCodeMenuQueryDTO;
import cec.jiutian.bc.lcp.inbound.local.service.query.LowCodeMenuQueryService;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import cec.jiutian.core.view.fabosJson.view.FabosJsonApiModel;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(FabosJsonRestPath.FABOS_API)
public class LowCodeMenuController {

    private final LowCodeMenuCommandService lowCodeMenuCommandService;
    private final LowCodeMenuQueryService lowCodeMenuQueryService;

    public LowCodeMenuController(LowCodeMenuCommandService lowCodeMenuCommandService, LowCodeMenuQueryService lowCodeMenuQueryService) {
        this.lowCodeMenuCommandService = lowCodeMenuCommandService;
        this.lowCodeMenuQueryService = lowCodeMenuQueryService;
    }

    @PostMapping("/getLowCodeMenuList")
    public FabosJsonApiModel getLowCodeMenuList(@RequestBody LowCodeMenuQueryDTO queryDTO){
        Page<LowCodeMenu> data =  lowCodeMenuQueryService.getLowCodeMenuList(queryDTO);
        return FabosJsonApiModel.successApi(data);
    }

    @PostMapping("/lowCode/release")
    public FabosJsonApiModel releaseLowCode(@RequestBody ReleaseDTO dto){
        Object object =  lowCodeMenuCommandService.releaseLowCodeMenu(dto);
        return FabosJsonApiModel.successApi(object);
    }

}
