//package cec.jiutian.bc.ecs.domain.extendFabosJsonTest;
//
//import cec.jiutian.common.util.BeanUtils;
//import cec.jiutian.view.fun.OperationHandler;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
//
//@Component
//@Slf4j
//public class AnimalOperationHandler implements OperationHandler<Animal, AnimalView> {
//
//
//    @Override
//    public String exec(List<Animal> data, AnimalView modelObject, String[] param) {
//        log.info("AnimalOperationHandler.exec");
//        return "msg.success()";
//    }
//
//    @Override
//    public AnimalView fabosJsonFormValue(List<Animal> data, AnimalView fabosJsonForm, String[] param) {
//        Animal animal = data.get(0);
//        BeanUtils.copyProperties(animal, fabosJsonForm);
//        return OperationHandler.super.fabosJsonFormValue(data, fabosJsonForm, param);
//    }
//}
