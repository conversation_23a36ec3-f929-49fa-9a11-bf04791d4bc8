package cec.jiutian.bc.ecs.domain.messagerecord.event;

import cec.jiutian.bc.ecs.domain.messagerecord.entity.MessageRecord;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.constant.YesOrNoStatus;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.fun.DataProxy;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 */
public class MessageRecordDataProxy implements DataProxy<MessageRecord> {

    @Override
    public void beforeAdd(MessageRecord messageRecord) {
        messageRecord.setCreateTime(LocalDateTime.now());
        messageRecord.setCreateBy(UserContext.getAccount());
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        DataProxy.super.afterFetch(list);
        // 设置消息的颜色标志, 规则为，消息需要处理，则展示颜色，且颜色根据消息级别来设置
        list.forEach(map -> {
            Object groupLevel = map.getOrDefault("groupLevel", "");
            Object isProcess = map.getOrDefault("isProcess", "");
            Object processCompleteFlag = map.getOrDefault("processCompleteFlag", "N");
            if (groupLevel != null && isProcess != null && processCompleteFlag != null && isProcess.toString().equals(YesOrNoStatus.YES.getValue()) && processCompleteFlag.toString().equals(YesOrNoStatus.NO.getValue())) {
                if ("LEVEL1".equals(groupLevel.toString())) {
                    map.put("colorFlag", FabosJson.BackgroundColor.BLUE.getValue());
                } else if ("LEVEL2".equals(groupLevel.toString())) {
                    map.put("colorFlag", FabosJson.BackgroundColor.YELLOW.getValue());
                } else if ("LEVEL3".equals(groupLevel.toString())) {
                    map.put("colorFlag", FabosJson.BackgroundColor.RED.getValue());
                }
            }

        });
    }
}
