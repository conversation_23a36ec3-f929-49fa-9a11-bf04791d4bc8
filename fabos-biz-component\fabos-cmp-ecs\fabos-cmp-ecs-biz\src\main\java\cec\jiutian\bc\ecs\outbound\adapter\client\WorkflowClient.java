package cec.jiutian.bc.ecs.outbound.adapter.client;

import cec.jiutian.bc.flow.message.command.FabosWorkflowCallback;
import cec.jiutian.bc.flow.message.command.FlowStartOrCompleteDTO;
import cec.jiutian.bc.flow.provider.IFlowProvider;
import cec.jiutian.core.data.factory.SpringBeanUtils;
import com.alibaba.fastjson2.JSONObject;
import com.alipay.sofa.koupleless.common.api.SpringServiceFinder;
import org.springframework.stereotype.Component;

@Component
public class WorkflowClient {

    private Object lock = new Object();
    private static volatile IFlowProvider flowProvider;

    public boolean startOrCompleteTask(String processDefinitionKey, String businessCategory, String businessKey, JSONObject formData){
        FlowStartOrCompleteDTO flowStartOrCompleteDTO=new FlowStartOrCompleteDTO();
        flowStartOrCompleteDTO.setProcessDefinitionKey(processDefinitionKey);
        flowStartOrCompleteDTO.setBusinessCategory(businessCategory);
        flowStartOrCompleteDTO.setBusinessKey(businessKey);
        flowStartOrCompleteDTO.setFormData(formData);
        flowStartOrCompleteDTO.setCallback(new FabosWorkflowCallback("fabos-cmp-ecs","3.2.2-SNAPSHOT"));
        acquireFlowProvider().startOrCompleteTask(flowStartOrCompleteDTO);
        return true;
    }

    private IFlowProvider acquireFlowProvider(){
        if (flowProvider != null) {
            return flowProvider;
        }
        synchronized(lock){
            if (flowProvider!=null){
                return flowProvider;
            }
            flowProvider = SpringBeanUtils.getBean(IFlowProvider.class);
            if (flowProvider == null) {
                flowProvider = SpringServiceFinder.getModuleService("fabos-cmp-workflow", "3.2.2-SNAPSHOT", IFlowProvider.class);
            }
        }
        return flowProvider;
    }

}
