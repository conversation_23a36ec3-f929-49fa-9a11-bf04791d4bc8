package cec.jiutian.bc.urm.domain.menu.entity;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "model_jump_arg")
@Getter
@Setter
@NoArgsConstructor
@FabosJson(
        name = "模型跳转参数",
        orderBy = "ModelJumpArg.createTime desc"
)
public class ModelJumpArg extends MetaModel {

    @FabosJsonField(
            views = @View(title = "当前模型", show = false, column = "displayName"),
            edit = @Edit(title = "当前模型",
                    type = EditType.REFERENCE_TABLE,
                    queryConditionChild = "{\"currentModelFiled.name\": \"${routerModule.name}\"}",
                    referenceTableType = @ReferenceTableType()
            )
    )
    @Transient
    private ModelFieldMTO currentModelFiled;

    @FabosJsonField(
            views = @View(title = "目标模型",show = false,column = "displayName"),
            edit = @Edit(title = "目标模型",
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType()
            )
    )
    @Transient
    private ModelFieldMTO targetModelFiled;

    // 字段简短名
    @FabosJsonField(
            views = @View(title = "当前字段简短名"),
            edit = @Edit(title = "当前字段简短名",search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "currentModelFiled", beFilledBy = "name"))

    )
    private String currentFiledName;

    // 字段简短名
    @FabosJsonField(
            views = @View(title = "目标字段简短名"),
            edit = @Edit(title = "目标字段简短名",search = @Search(vague = true)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "targetModelFiled", beFilledBy = "name"))

    )
    private String targetFiledName;

}
