package cec.jiutian.bc.generalModeler.domain.material.model;

import cec.jiutian.bc.generalModeler.port.dto.MetadataModelFieldMTO;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.field.edit.TabTableReferType;
import cec.jiutian.view.type.Filter;
import jakarta.persistence.ConstraintMode;
import jakarta.persistence.Entity;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "物料属性模板"
)
@Table(name = "material_attribute_template",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"code"})
        }
)
@Entity
@Getter
@Setter
public class MaterialAttributeTemplate extends MetaModel {
    @FabosJsonField(
            views = @View(title = "编码"),
            edit = @Edit(title = "编码", notNull = true, search = @Search)
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "显示名称"),
            edit = @Edit(title = "显示名称", notNull = true, search = @Search)
    )
    private String displayName;

    @ManyToMany  //多对多
    @JoinTable(
            name = "e_materialattrtemplate_materialfield", //中间表表名，如下为中间表的定义
            inverseForeignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT),
            joinColumns = @JoinColumn(name = "template_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "field_id", referencedColumnName = "id"))
    @FabosJsonField(
            views = @View(title = "属性字段", type = ViewType.TABLE_NESTED, column = "displayName"),
            edit = @Edit(
                    title = "属性字段",
                    type = EditType.TAB_TABLE_REFER,
                    tabTableReferType = @TabTableReferType(),
                    filter = @Filter(value = "model = 'cec.jiutian.bc.generalModeler.domain.material.model.Material' and columnName like 'wl%'")
            )
    )
    private List<MetadataModelFieldMTO> fields;

}
