package cec.jiutian.bc.generalModeler.domain.namingRule.service;

import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRule;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.config.Comment;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import org.springframework.stereotype.Service;


@Service
public class NamingRuleDomainService {
    @Resource
    private FabosJsonDao fabosJsonDao;


    @Comment("根据命名规则编码获取命名规则")
    public NamingRule getParametersByCode(String ruleCode) {
        EntityManager em = fabosJsonDao.getEntityManager();
        CriteriaBuilder criteriaBuilder = em.getCriteriaBuilder();
        CriteriaQuery<NamingRule> criteriaQuery = criteriaBuilder.createQuery(NamingRule.class);
        Root<NamingRule> namingRuleRoot = criteriaQuery.from(NamingRule.class);
        criteriaQuery.where(
                criteriaBuilder.and(
                        criteriaBuilder.equal(namingRuleRoot.get("namingRuleCode"), ruleCode)
                )
        );
//        em.flush();
//        em.clear();
        return em.createQuery(criteriaQuery).getSingleResult();
    }
}
