package cec.jiutian.bc.infra.util;

import cec.jiutian.common.exception.ServiceException;
import jakarta.annotation.Resource;
import jakarta.mail.*;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Properties;

/**
 * 邮件发送模板
 *
 * <AUTHOR>
 * @date 2022/10/12
 */
@Component
@Slf4j
public class EmailTemplate {

    @Value("${spring.mail.sendFrom}")
    private String sendFrom;

    @Value("${spring.mail.host}")
    private String host;

    @Value("${spring.mail.port}")
    private String port;

    @Value("${spring.mail.username}")
    private String username;

    @Value("${spring.mail.password}")
    private String password;

    @Resource
    private JavaMailSender javaMailSender;


    /**
     * 发送Html邮件
     * @param to      接受者邮箱，可传入数组String[]
     * @param subject 邮件主题
     * @param html    邮件内容 带html标签
     */
    public void sendHtmlMail(String to, String subject, String html) {
        try {
            MimeMessage message = javaMailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            helper.setFrom(sendFrom);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(html, true);
            javaMailSender.send(message);
            log.info("邮件已发送：{}", to);
        } catch (Exception e){
            throw new ServiceException(to + "邮件发送失败:" + e.getMessage());
        }
    }

    public void sendMail587(String to, String title, String message) {
        try {
            //设置邮件会话参数
            Properties props = new Properties();
            //邮箱的发送服务器地址
            props.put("mail.smtp.host", host);
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.port", port);
            props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.smtp.ssl.trust", "smtp.cecjiutian.com");  // 指定使用新的证书库
            props.put("mail.smtp.ssl.checkserveridentity", "false");  // 关闭SSL验证

            //获取到邮箱会话,利用匿名内部类的方式,将发送者邮箱用户名和密码授权给jvm
            Session session = Session.getDefaultInstance(props, new Authenticator() {
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(username, password);
                }
            });
            //通过会话,得到一个邮件,用于发送
            Message msg = new MimeMessage(session);
            //设置发件人
            msg.setFrom(new InternetAddress(username));
            //设置收件人,to为收件人,cc为抄送,bcc为密送
            msg.setRecipients(Message.RecipientType.TO, InternetAddress.parse(to, false));
//            msg.setRecipients(Message.RecipientType.CC, InternetAddress.parse(to, false));
//            msg.setRecipients(Message.RecipientType.BCC, InternetAddress.parse(to, false));

            // 设置邮件标题
            msg.setSubject(title);
            //设置发送的日期
            msg.setSentDate(new Date());
            /*//设置邮件消息
            msg.setText(message);*/
            // 设置邮件的内容体
            msg.setContent(message, "text/html;charset=UTF-8");
            //调用Transport的send方法去发送邮件
            log.info("邮件正在发送===============" + to);
            Transport.send(msg);

        } catch (Exception e) {
            log.error("邮件发送失败： ", e);
        }

    }

}
