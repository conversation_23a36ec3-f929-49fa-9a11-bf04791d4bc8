package cec.jiutian.bc.urm.domain.user.entity;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.ReferenceTreeType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.config.Comment;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.type.Tree;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.JoinColumn;


@Entity
@Table(name = "material_category",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"code"})
        }
)
@FabosJson(
        name = "物料类别",
        orderBy = "MaterialCategory.sort",
        tree = @Tree(pid = "parentCategory.id", expandLevel = 4)
)
@Getter
@Setter
@TemplateType(type = "treeForm")
public class MaterialCategory1 extends MetaModel {

    @FabosJsonField(
            views = @View(title = "编码"),
            edit = @Edit(title = "编码", readonly = @Readonly(add = false, edit = true), notNull = true)
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "显示名称"),
            edit = @Edit(title = "显示名称", notNull = true, readonly = @Readonly(add = false, edit = true))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "显示顺序"),
            edit = @Edit(title = "显示顺序", numberType = @NumberType(min = 1))
    )
    private Integer sort;

    @ManyToOne
    @FabosJsonField(
            edit = @Edit(
                    title = "上级物料类别",
                    type = EditType.REFERENCE_TREE,
                    referenceTreeType = @ReferenceTreeType(pid = "parentCategory.id", expandLevel = 4)
            )
    )
    private MaterialCategory1 parentCategory;

    @FabosJsonField(
            edit = @Edit(title = "级别", show = false),
            views = @View(title = "级别", show = true)
    )
    @Comment("逻辑字段，生成的当前类别的级别")
    private Integer categoryLevel;

    @FabosJsonField(
            views = @View(title = "保管员"),
            edit = @Edit(title = "保管员", notNull = false)
    )
    private String storeKeeper;

    @FabosJsonField(
            edit = @Edit(title = "描述", notNull = false,
                    type = EditType.TEXTAREA, inputType = @InputType(length = 400))
    )
    private String desciption;



}
