package cec.jiutian.bc.lcp.inbound.local.service.query;

import cec.jiutian.bc.lcp.domain.lowCodeMenu.entity.LowCodeMenu;
import cec.jiutian.bc.lcp.domain.lowCodeMenu.service.LowCodeMenuService;
import cec.jiutian.bc.lcp.domain.lowCodeMenu.service.query.LowCodeMenuQueryDTO;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;


@Service
public class LowCodeMenuQueryService {

    private final LowCodeMenuService lowCodeMenuService;

    public LowCodeMenuQueryService(LowCodeMenuService lowCodeMenuService) {
        this.lowCodeMenuService = lowCodeMenuService;
    }


    public Page<LowCodeMenu> getLowCodeMenuList(LowCodeMenuQueryDTO queryDTO) {
        return lowCodeMenuService.getAllLowCodeMenuList(queryDTO);
    }

}
