package cec.jiutian.bc.urm.domain.dictionary.entity;

import cec.jiutian.bc.urm.domain.dictionary.event.DictionaryItemDataProxy;
import cec.jiutian.bc.urm.dto.MetaDictItem;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.constant.AnnotationConst;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.BoolType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;


@Entity
@Table(name = "fd_dict_item", uniqueConstraints = @UniqueConstraint(columnNames = {"code", "dict_id", "oid"}))
@FabosJson(
        name = "字典项",
        orderBy = "sort",
        dataProxy = {DictionaryItemDataProxy.class},
        power = @Power(export = true, importable = true)
)
@Getter
@Setter
public class DictionaryItem extends MetaModel {

    @Column(length = AnnotationConst.CODE_LENGTH)
    @FabosJsonField(
            views = @View(title = "字典项编码", sortable = true),
            edit = @Edit(title = "字典项编码", notNull = true)
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "字典项名称", sortable = true),
            edit = @Edit(title = "字典项名称", notNull = true)
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "英文名称"),
            edit = @Edit(title = "英文名称")
    )
    private String eName;

    @FabosJsonField(
            views = @View(title = "显示顺序", sortable = true),
            edit = @Edit(title = "显示顺序")
    )
    private Integer sort;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(
                    title = "备注",
                    type = EditType.TEXTAREA
            )
    )
    private String remark;

    @FabosJsonField(
            views = @View(title = "状态", sortable = true),
            edit = @Edit(
                    title = "状态",
                    type = EditType.BOOLEAN,
                    notNull = true,
                    defaultVal = "true",
                    search = @Search(vague = true, defaultVal = "true"),
                    boolType = @BoolType(trueText = "有效", falseText = "失效")
            )
    )
    private Boolean status;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "所属字典", column = "name", show = false),
            edit = @Edit(title = "所属字典", show = false,
                    type = EditType.REFERENCE_TABLE)
    )
    @JoinColumn(name = "dict_id")
    @JsonIgnoreProperties("dictionaryItems")
    private Dictionary dictionary;

    public DictionaryItem(String code, String name, Integer sort) {
        this.code = code;
        this.name = name;
        this.sort = sort;
    }

    public DictionaryItem() {
    }

    public static DictionaryItem fromMetaDictItem(MetaDictItem item) {
        if (null == item) return null;
        DictionaryItem dictionaryItem = new DictionaryItem(item.getCode(),
                item.getName(), item.getSort());
        return dictionaryItem;
    }
}
