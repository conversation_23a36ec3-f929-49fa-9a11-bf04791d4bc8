package cec.jiutian.bc.generalModeler.domain.stockAlarm.proxy;

import cec.jiutian.bc.generalModeler.domain.stockAlarm.model.StockAlarm;
import cec.jiutian.bc.generalModeler.port.dto.PositionMTO;
import cec.jiutian.bc.generalModeler.util.StringUtils;
import cec.jiutian.bc.urm.domain.position.entity.Position;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

@Component
public class StockAlarmDataProxy implements DataProxy<StockAlarm> {

    @Resource
    private JpaCrud jpaCrud;

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void beforeUpdate(StockAlarm stockAlarm) {

        PositionMTO keeper = stockAlarm.getKeeper();
        if (keeper != null && StringUtils.isNotBlank(keeper.getId())) {
            PositionMTO user = fabosJsonDao.findById(PositionMTO.class, keeper.getId());
            stockAlarm.setKeeper(user);
        } else {
            stockAlarm.setKeeper(null);
        }
        PositionMTO purchaser = stockAlarm.getPurchaser();
        if (purchaser != null && StringUtils.isNotBlank(purchaser.getId())) {
            PositionMTO user = fabosJsonDao.findById(PositionMTO.class, purchaser.getId());
            stockAlarm.setPurchaser(user);
        } else {
            stockAlarm.setPurchaser(null);
        }
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        for (Map<String, Object> map : list) {
            Object o = map.get("purchaser");
            if (o != null) {
                Object id = ((Map<String, Object>) o).get("id");
                if (id != null) {
                    Position purchaser = fabosJsonDao.findById(Position.class, id);
                    map.put("purchaser_name", purchaser.getName());
                    map.put("purchaser", purchaser);
                }
            }
            Object o1 = map.get("keeper");
            if (o1 != null) {
                Object id = ((Map<String, Object>) o1).get("id");
                if (id != null) {
                    Position keeper = fabosJsonDao.findById(Position.class, id);
                    map.put("keeper_name", keeper.getName());
                    map.put("keeper", keeper);
                }
            }
        }
    }
}
