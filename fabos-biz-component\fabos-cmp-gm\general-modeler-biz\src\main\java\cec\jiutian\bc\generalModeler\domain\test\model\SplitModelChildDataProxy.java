package cec.jiutian.bc.generalModeler.domain.test.model;

import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.config.Comment;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@Service
public class SplitModelChildDataProxy implements DataProxy<SplitModelChild> {
    @Resource
    private FabosJsonDao fabosJsonDao;


    @Override
    public void afterFetch(@Comment("查询结果") Collection<Map<String, Object>> list) {
        //对查询结果，额外的为模型中定义为Transient类型的字段赋值，以用于前端展示
        if (list == null || list.size() <= 0) {
            return;
        }
        // 获取查询条件（以当前模型返回结果集中的id作为查询条件）
        List<String> ids = new ArrayList<>();
        for (Map<String, Object> map : list) {
            if (map.get("id") != null) {
                String id = map.get("id").toString();
                ids.add(id);
            }
        }
        // 查询关联模型中的附加数据
        String jpql = "SELECT c.id, c.splitModelFull.displayName as displayName FROM SplitModelChild c where id in :ids";
        List<Object[]> result = fabosJsonDao.getEntityManager().createQuery(jpql).setParameter("ids", ids).getResultList();
        // 为Transient模型字段赋值（p1）
        for (Map<String, Object> map : list) {
            map.put("p1", result.stream().filter(objects -> objects[0].equals(map.get("id"))).map(objects -> objects[1]).findFirst().orElse(null));
        }
    }

}
