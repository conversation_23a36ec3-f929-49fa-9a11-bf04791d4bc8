package cec.jiutian.bc.lcp.outbound.adapter.client;

import cec.jiutian.bc.urm.dto.MetaDictItem;
import cec.jiutian.bc.urm.provider.IDictProvider;
import com.alipay.sofa.koupleless.common.api.AutowiredFromBiz;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class DictClient {

    @AutowiredFromBiz(bizName = "fabos-cmp-urm", bizVersion = "3.2.2-SNAPSHOT")
    private IDictProvider iDictProvider ;

    public List<MetaDictItem> getDictItems(String code) {
        return iDictProvider.getDictItems(code);
    }
}
