package cec.jiutian.api.remoteCallLog.config;


import cec.jiutian.api.remoteCallLog.dto.RemoteCallResult;
import feign.FeignException;
import feign.Response;
import feign.codec.Decoder;

import java.io.IOException;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

public class ResultDecoder implements Decoder {
    private Decoder decoder;

    public ResultDecoder(Decoder decoder) {
        this.decoder = decoder;
    }

//    @Override
//    public RemoteCallResult<Object> decode(Response response, Type type) throws IOException, FeignException {
//        try {
//            RemoteCallResult<Object> result = (RemoteCallResult<Object>) this.decoder.decode(response, type);
//            return result;
//        } catch (Exception e) {
//            return remoteCallResult.error("500", "调用失败");
//        }
//    }

    public Object decode(Response response, Type type) throws IOException, FeignException {
        try {
            // 判断方法的返回类型是否被定义为 ResultObject
            if (isResultObjectType(type)) {
                // 将原始响应值封装到 ResultObject 中
                RemoteCallResult<?> result = (RemoteCallResult<?>) decoder.decode(response, type);
                return result;
            } else {
                // 其他情况，直接返回原始数据
                return decoder.decode(response, type);
            }
        } catch (Exception e) {
            return RemoteCallResult.error("500", "调用失败。" + e.getMessage());
        }

    }

    private boolean isResultObjectType(Type type) {
        return type instanceof ParameterizedType &&
                ((ParameterizedType) type).getRawType().equals(RemoteCallResult.class);
    }


}
