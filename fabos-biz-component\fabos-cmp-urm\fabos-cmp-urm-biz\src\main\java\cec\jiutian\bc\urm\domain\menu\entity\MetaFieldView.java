package cec.jiutian.bc.urm.domain.menu.entity;

import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.meta.model.MetadataModel;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.Search;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "metadata_model_field")
@Getter
@Setter
@NoArgsConstructor
@FabosJson(
        name = "模型元数据字段",
        orderBy = "MetaFieldView.id asc"
)
public class MetaFieldView {
    @Id
    @FabosJsonField(
            edit = @Edit(title = "id"),
            views = @View(title = "id",show = false)
    )
    private String id;

    // 字段编码, 限定名
   @FabosJsonField(
           views = @View(title = "字段编码"),
           edit = @Edit(title = "字段编码")
   )
    private String code;

    // 字段简短名
    @FabosJsonField(
            views = @View(title = "字段简短名"),
            edit = @Edit(title = "字段简短名",search = @Search(vague = true))
    )
    private String name;

    // 显示名称
    @FabosJsonField(
            views = @View(title = "显示名称"),
            edit = @Edit(title = "显示名称",search = @Search(vague = true))
    )
    private String displayName;


 @ManyToOne
 @JsonIgnoreProperties({"fields"})
 @FabosJsonField(
         views = @View(title = "模型名称", column = "displayName", type = ViewType.TABLE_FORM),
         edit = @Edit(title = "模型名称", type = EditType.REFERENCE_TABLE)
 )
 private MetadataModel referenceModel;
}
