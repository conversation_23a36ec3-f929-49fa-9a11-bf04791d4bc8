package cec.jiutian.bc.ecs.domain.messagerecord.service;

import cec.jiutian.bc.ecs.domain.messagerecord.entity.MessageRecord;
import cec.jiutian.bc.ecs.domain.messagerecord.entity.MessageRecordDto;
import cec.jiutian.bc.ecs.domain.messagerecord.repository.MessageRecordRepository;
import cec.jiutian.bc.ecs.domain.upgrade.repository.MsgUpgradeRepository;
import cec.jiutian.bc.infra.remote.controller.WebSocketServer;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.constant.YesOrNoStatus;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.frame.module.MetaUser;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Service
@AllArgsConstructor
@Transactional
@Slf4j
public class MessageRecordService {
    private final FabosJsonDao fabosJsonDao;
    private final MessageRecordRepository messageRecordRepository;

    @Transactional
    public void create(MessageRecord messageRecord) {
        messageRecord.setCreateTime(LocalDateTime.now());
        fabosJsonDao.persistAndFlush(messageRecord);
        if (messageRecord.getDispatchMetaUser() != null) {
            try {
                WebSocketServer.sendMessage(messageRecord.getDispatchMetaUser().getId());
                if (messageRecord.getImmediateAlarmFlag()) {
                    WebSocketServer.sendImmediateAlarm(messageRecord.getContent(), messageRecord.getDispatchMetaUser().getId());
                }
            } catch (Exception e) {
                log.error("消息通知失败：{}", e.getMessage());
            }
        } else {
            log.error("用户不存在，消息通知失败");
        }
    }

    public List<MessageRecordDto> queryMyMessage() {
        String phoneNumber = UserContext.getPhoneNumber();
        if (StringUtils.isBlank(phoneNumber)) {
            throw new FabosJsonApiErrorTip("用户未登录");
        }

//        EntityManager entityManager = fabosJsonDao.getEntityManager();
//
//        // 创建 CriteriaBuilder 和 CriteriaQuery 对象
//        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
//        CriteriaQuery<MessageRecord> criteriaQuery = criteriaBuilder.createQuery(MessageRecord.class);
//
//        // 设置查询条件
//        Root<MessageRecord> root = criteriaQuery.from(MessageRecord.class);
//        criteriaQuery.where(
//                criteriaBuilder.and(
//                        criteriaBuilder.equal(root.get("dispatchUser"), phoneNumber),
//                        criteriaBuilder.equal(root.get("dispatchWay"), "App"),
//                        criteriaBuilder.equal(root.get("status"), YesOrNoStatus.NO.getValue())
//                )
//        ).orderBy(criteriaBuilder.desc(root.get("createTime")));
//
//        // 创建分页查询
//        TypedQuery<MessageRecord> typedQuery = entityManager.createQuery(criteriaQuery);
//        typedQuery.setFirstResult(0); // 设置起始位置
//        typedQuery.setMaxResults(10); // 设置每页大小
//
//        // 执行查询并获取结果
//        List<MessageRecord> records = typedQuery.getResultList();


        // 使用DTO返回，减少数据量，减少不必要连表查询
        Pageable pageable = PageRequest.of(0, 10);
        return messageRecordRepository.findByDispatchUserAndDispatchWayAndStatusOrderByCreateTimeDesc(
                phoneNumber, "App", YesOrNoStatus.NO.getValue(), pageable
        );
    }

    @Resource
    private MsgUpgradeRepository msgUpdateService;

    public void updateRecordStatus(String id) {
        String account = UserContext.getAccount();
        if (StringUtils.isBlank(account)) {
            throw new FabosJsonApiErrorTip("用户未登录");
        }
        MessageRecord messageRecord = fabosJsonDao.findById(MessageRecord.class, id);
        messageRecord.setUpdateTime(LocalDateTime.now());
        messageRecord.setStatus(YesOrNoStatus.YES.getValue());
        fabosJsonDao.mergeAndFlush(messageRecord);
    }

    @Transactional
    public void clearUnread() {
        String phoneNumber = UserContext.getPhoneNumber();
        String hql = "UPDATE MessageRecord m SET m.status = 'Y', m.updateTime = :updateTime " +
                "WHERE m.dispatchUser = :phoneNumber AND m.status = 'N'";
        EntityManager entityManager = fabosJsonDao.getEntityManager();
        entityManager.createQuery(hql)
                .setParameter("updateTime", LocalDateTime.now())
                .setParameter("phoneNumber", phoneNumber)
                .executeUpdate();
    }

    /**
     * 根据消息ID，查找所有的消息发送记录，并关闭
     * 参数：消息ID
     */
    @Transactional
    public void closeMessageRecord(String messageId, String processDetail) {
        String hql = "UPDATE MessageRecord m SET m.processCompleteFlag = 'Y', m.processTime = :processTime, " +
                "m.processUser = :processUser, m.processDetail = :processDetail " +
                "WHERE m.message.id = :id AND m.processCompleteFlag = 'N'";
        EntityManager entityManager = fabosJsonDao.getEntityManager();
        MetaUser processUser = fabosJsonDao.findById(MetaUser.class, UserContext.getUserId());
        entityManager.createQuery(hql)
                .setParameter("processTime", LocalDateTime.now())
                .setParameter("processUser", processUser)
                .setParameter("processDetail", processDetail)
                .setParameter("id", messageId)
                .executeUpdate();
    }
}
