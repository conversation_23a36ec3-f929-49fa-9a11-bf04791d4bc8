package cec.jiutian.bc.generalModeler.domain.warehouseArea.handler;

import cec.jiutian.bc.generalModeler.domain.warehouseArea.model.Warehouse;
import cec.jiutian.bc.generalModeler.enumeration.WarehouseStateEnum;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class WarehouseUnHoldHandler implements OperationHandler<Warehouse, Void> {

    @Resource
    private JpaCrud jpaCrud;

    @Override
    public String exec(List<Warehouse> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            Warehouse warehouse = data.get(0);
            warehouse.setLockState(WarehouseStateEnum.Enum.Normal.name());
            jpaCrud.update(warehouse);
        }
        return "msg.success('操作成功')";
    }
}
