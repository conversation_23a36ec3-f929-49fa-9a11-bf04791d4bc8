package cec.jiutian.bc.lcp.inbound.remote.provider;

import cec.jiutian.bc.job.provider.IJobProvider;
import cec.jiutian.bc.lcp.domain.lowCodeMenu.entity.LowCodeMenu;
import cec.jiutian.core.frame.annotation.FabosCustomizedService;
import cec.jiutian.meta.FabosJob;
import org.springframework.stereotype.Component;

@FabosCustomizedService(value = LowCodeMenu.class)
@Component
public class LcpScheduleProvider implements IJobProvider {
    @FabosJob(comment = "低代码ScheduleTest")
    @Override
    public String exec(String code, String param) {
        return "执行成功 ，哈哈";
    }
}
