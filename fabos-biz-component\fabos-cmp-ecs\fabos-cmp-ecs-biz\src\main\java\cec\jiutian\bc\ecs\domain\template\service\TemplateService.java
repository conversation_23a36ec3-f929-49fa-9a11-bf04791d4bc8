package cec.jiutian.bc.ecs.domain.template.service;

import cec.jiutian.bc.ecs.domain.template.entity.Template;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import org.springframework.stereotype.Service;

@Service
public class TemplateService {

    private FabosJsonDao fabosJsonDao;

    public TemplateService(FabosJsonDao fabosJsonDao) {
        this.fabosJsonDao = fabosJsonDao;
    }
    public Template getById(String id) {
        return fabosJsonDao.findById(Template.class,id);
    }
//    public String getTemplatePreview(String templateId) {
//        Template template = fabosJsonDao.findById(Template.class, templateId);
//        if (template == null || CollectionUtils.isEmpty(template.getTemplateAttributes())) {
//            return null;
//        }
//        StringBuilder result = new StringBuilder();
//        for (TemplateAttribute attribute : template.getTemplateAttributes()) {
//            if (StringUtils.isNotEmpty(attribute.getConnectAttribute())) {
//                result.append("${").append(attribute.getConnectAttribute()).append("}");
//            } else {
//                result.append(attribute.getDefaultValue());
//            }
//        }
//        return result.toString();
//    }
}
