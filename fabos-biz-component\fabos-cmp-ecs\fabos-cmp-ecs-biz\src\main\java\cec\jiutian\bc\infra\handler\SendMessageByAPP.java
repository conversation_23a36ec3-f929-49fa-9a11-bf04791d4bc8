package cec.jiutian.bc.infra.handler;

import cec.jiutian.bc.ecs.domain.messagerecord.entity.MessageRecord;
import cec.jiutian.bc.ecs.inbound.local.enums.DispatchWayEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date ：2023/5/22 17:48
 * @description：
 */
@Component(DispatchWayEnum.APP_CODE)
public class SendMessageByAPP implements MessageHandler {
    private static final Logger log = LoggerFactory.getLogger(SendMessageByAPP.class);

    @Override
    public void sendMessage(MessageRecord createDTO) {
        //todo 单独记录一张表

        log.info("发送站内信");
    }
}
