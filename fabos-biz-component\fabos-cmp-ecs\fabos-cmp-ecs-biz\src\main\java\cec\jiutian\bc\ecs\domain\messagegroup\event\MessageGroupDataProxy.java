package cec.jiutian.bc.ecs.domain.messagegroup.event;

import cec.jiutian.bc.ecs.inbound.local.enums.DispatchWayEnum;
import cec.jiutian.bc.ecs.domain.messagegroup.entity.MessageGroup;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.view.fun.DataProxy;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Map;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @description:
 */
public class MessageGroupDataProxy implements DataProxy<MessageGroup> {
    @Override
    public void beforeAdd(MessageGroup messageGroup) {
        if (CollectionUtils.isEmpty(messageGroup.getFirstUsers())) {
            throw new FabosJsonApiErrorTip("需选择推送人员");
        }
    }
    @Override
    public void beforeUpdate(MessageGroup messageGroup) {
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.forEach(map -> {
            String firstWay = (String) map.get("firstWay");
            String secondWay = (String) map.get("secondWay");
            String thirdWay = (String) map.get("thirdWay");
            map.put("firstWay", wayTransform(firstWay));
            map.put("secondWay", wayTransform(secondWay));
            map.put("thirdWay", wayTransform(thirdWay));
        });
    }

    private String wayTransform(String way) {
        if (StringUtils.isBlank(way)){
            return "";
        }
        StringJoiner stringJoiner = new StringJoiner(",");
        String[] split = way.split(",");
        for (String s : split) {
            stringJoiner.add(DispatchWayEnum.getWayByCode(s));
        }
        return stringJoiner.toString();
    }
}
