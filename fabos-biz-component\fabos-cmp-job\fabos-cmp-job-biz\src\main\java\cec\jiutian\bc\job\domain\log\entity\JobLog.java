package cec.jiutian.bc.job.domain.log.entity;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.constant.AnnotationConst;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.BoolType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Date;

@FabosJson(
        orderBy = "endTime desc",
        name = "任务日志",
        power = @Power(export = true, add = false, edit = false, viewDetails = false)
)
@Entity
@Table(name = "fd_job_log")
@Getter
@Setter
public class JobLog extends MetaModel {

    @FabosJsonField(
            views = @View(title = "任务ID"),
            edit = @Edit(title = "任务ID", search = @Search)
    )
    private String jobId;

    @FabosJsonField(
            views = @View(title = "任务名称"),
            edit = @Edit(title = "任务名称", search = @Search)
    )
    private String jobName;

    @Column(length = AnnotationConst.REMARK_LENGTH)
    @FabosJsonField(
            views = @View(title = "任务参数")
    )
    private String handlerParam;

    @FabosJsonField(
            views = @View(title = "任务状态"),
            edit = @Edit(title = "任务状态", boolType = @BoolType(trueText = "成功", falseText = "失败"), search = @Search)
    )
    private Boolean status;

    @FabosJsonField(
            views = @View(title = "任务执行时间", type = ViewType.text),
            edit = @Edit(title = "任务执行时间", notNull = true,
                    search = @Search(vague = true),
                    type = EditType.DATE_TIME_RANGE)
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @FabosJsonField(
            views = @View(title = "结束时间", type = ViewType.text),
            edit = @Edit(title = "结束时间", notNull = true,
                    search = @Search(vague = true),
                    type = EditType.DATE_TIME_RANGE)
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Column(length = 2000)
    @FabosJsonField(
            views = @View(title = "执行结果"),
            edit = @Edit(title = "执行结果")
    )
    private String resultInfo;

    @Column(columnDefinition = "TEXT")
    @FabosJsonField(
            views = @View(title = "错误信息", toolTip = true),
            edit = @Edit(title = "错误信息",type = EditType.TEXTAREA)
    )
    private String errorInfo;

}
