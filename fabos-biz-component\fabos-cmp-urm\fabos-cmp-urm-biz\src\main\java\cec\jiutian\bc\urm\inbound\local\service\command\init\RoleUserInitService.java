package cec.jiutian.bc.urm.inbound.local.service.command.init;

import cec.jiutian.bc.urm.domain.menu.entity.Menu;
import cec.jiutian.bc.urm.domain.role.entity.Role;
import cec.jiutian.bc.urm.domain.role.service.RoleService;
import cec.jiutian.bc.urm.domain.systemInit.entity.SystemInit;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.bc.urm.domain.user.service.UserService;
import cec.jiutian.bc.urm.enums.UserManagerTypeEnum;
import cec.jiutian.bc.urm.inbound.local.config.SystemConfig;
import cec.jiutian.core.frame.constant.GenderEnum;
import cec.jiutian.core.frame.constant.SwitchStatus;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.data.jpa.JpaCrud;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * 初始化超级管理员
 */
@Slf4j
@Service
@Transactional
public class RoleUserInitService {

    @Resource
    private SystemConfig systemConfig;

    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private RoleService roleService;
    @Resource
    private UserService userService;
    @Resource
    private JpaCrud jpaCrud;

    /**
     * 初始化超级管理员账号
     */
    public User initSuperUser(Role role) {
        User user = isSuperUserExist();
        if (user != null) {
            log.info("超级管理员已存在，无需初始化");
            return user;
        }
        //初始化超级管理员用户
        user = new User();
        user.setRoles(Collections.singletonList(role));
        user.setAccount(systemConfig.getSuperUserName());
        user.setPhoneNumber(systemConfig.getSuperPhone());
        user.setEmailAddress(systemConfig.getSuperEmail());
        user.setEmployeeNumber("*********");
        user.setPassword(systemConfig.getSuperUserPassword());
        user.setName("超级管理员");
        user.setOid("0");
        user.setCreateBy("system");
        user.setGender(GenderEnum.MALE.getEn());
        user.setAdminFlag("true");
        user.setState(SwitchStatus.ENABLED.getValue());
        user.setCreateTime(LocalDateTime.now());
        userService.createUsers4Init(Collections.singletonList(user));
        return user;
    }

    private User isSuperUserExist() {
        List<User> users = fabosJsonDao.queryEntityList(User.class,
                "account = '" + systemConfig.getSuperUserName() + "'");
        if (users.isEmpty()) {
            return null;
        }
        if (users.size() > 1) {
            throw new FabosJsonApiErrorTip("存在多个超级管理员，请检查数据库");
        }
        return users.get(0);
    }

    /**
     * 初始化超级管理员角色
     */
    private Role initSuperUserRole(List<Menu> menus) {
        //创建superUser角色
        Role superUserRole = new Role();
        superUserRole.setName("超级管理员");
        superUserRole.setDescription("超级管理员角色，拥有所有权限");
        superUserRole.setCode(UserManagerTypeEnum.superManager.getCode());
        superUserRole.setSort(0);
        superUserRole.setStatus(true);
        superUserRole.setOid("0");
        superUserRole.setCreateBy("system");
        superUserRole.setCreateTime(LocalDateTime.now());
        superUserRole.setMenus(menus);
        roleService.createRoles4Init(Collections.singletonList(superUserRole));
        return superUserRole;
    }

    /**
     * 初始化超级管理员和角色以及权限
     * @param menus   超级管理员的初始化菜单
     */
    public void initSuper(List<Menu> menus) {
        log.info("开始初始化用户角色权限");
        SystemInit systemInit = new SystemInit();
        systemInit.setInitItem("superUser");
        List<SystemInit> select = jpaCrud.select(systemInit);
        if(CollectionUtils.isEmpty(select)){
            //权限初始化(创建角色分配菜单权限)
            Role role = initSuperUserRole(menus);
            //初始化超级管理员账号
            initSuperUser(role);
            systemInit.setInitTime(LocalDateTime.now());
            jpaCrud.insert(systemInit);
        }else {
            log.info("超级管理员用户角色权限等已初始化，本次跳过初始化流程");
        }
    }

    public void init3Rm(List<Menu> systemMenus,List<Menu> auditMenus,List<Menu> securityMenus){
        log.info("开始初始化用户角色权限");
        SystemInit systemInit = new SystemInit();
        systemInit.setInitItem("3rm");
        List<SystemInit> select = jpaCrud.select(systemInit);
        if(CollectionUtils.isEmpty(select)){
            //系统管理员
            Role systemRole = initSystemRole(systemMenus);
            //initSystemUser(systemRole); 只创建角色不创建用户
            //安全审计员
            Role auditRole = initAuditRole(auditMenus);
            //initAuditUser(auditRole);只创建角色不创建用户
            //安全保密员
            Role securityRole = initSecurityRole(securityMenus);
            //initSecurityUser(securityRole);只创建角色不创建用户
            systemInit.setInitTime(LocalDateTime.now());
            jpaCrud.insert(systemInit);
        }else {
            log.info("三员用户角色权限等已初始化，本次跳过初始化流程");
        }
    }
    private Role initSystemRole(List<Menu> menus) {
        //创建superUser角色
        Role superUserRole = new Role();
        superUserRole.setName("系统管理员");
        superUserRole.setDescription("系统管理员角色");
        superUserRole.setCode(UserManagerTypeEnum.systemManager.getCode());
        superUserRole.setSort(0);
        superUserRole.setStatus(true);
        superUserRole.setOid("0");
        superUserRole.setCreateBy("system");
        superUserRole.setCreateTime(LocalDateTime.now());
        superUserRole.setMenus(menus);
        roleService.createRoles4Init(Collections.singletonList(superUserRole));
        return superUserRole;
    }
//    private User initSystemUser(Role role){
//        User user = new User();
//        user.setRoles(Collections.singletonList(role));
//        user.setAccount(systemConfig.getSystemUserName());
//        user.setPhoneNumber(systemConfig.getSystemPhone());
//        user.setEmailAddress(systemConfig.getSystemEmail());
//        user.setEmployeeNumber("*********");
//        user.setPassword(systemConfig.getSystemPassword());
//        user.setName("系统管理员");
//        user.setOid("0");
//        user.setCreateBy("system");
//        user.setGender(GenderEnum.MALE.getEn());
//        user.setAdminFlag("true");
//        user.setState(SwitchStatus.ENABLED.getValue());
//        user.setCreateTime(LocalDateTime.now());
//        userService.createUsers4Init(Collections.singletonList(user));
//        return user;
//    }
    private Role initAuditRole(List<Menu> menus) {
        //创建superUser角色
        Role superUserRole = new Role();
        superUserRole.setName("安全审计员");
        superUserRole.setDescription("安全审计员角色");
        superUserRole.setCode(UserManagerTypeEnum.auditManager.getCode());
        superUserRole.setSort(0);
        superUserRole.setStatus(true);
        superUserRole.setOid("0");
        superUserRole.setCreateBy("system");
        superUserRole.setCreateTime(LocalDateTime.now());
        superUserRole.setMenus(menus);
        roleService.createRoles4Init(Collections.singletonList(superUserRole));
        return superUserRole;
    }
//    private User initAuditUser(Role role){
//        User user = new User();
//        user.setRoles(Collections.singletonList(role));
//        user.setAccount(systemConfig.getAuditUserName());
//        user.setPhoneNumber(systemConfig.getAuditPhone());
//        user.setEmailAddress(systemConfig.getAuditEmail());
//        user.setEmployeeNumber("*********");
//        user.setPassword(systemConfig.getAuditPassword());
//        user.setName("安全审计员");
//        user.setOid("0");
//        user.setCreateBy("system");
//        user.setGender(GenderEnum.MALE.getEn());
//        user.setAdminFlag("true");
//        user.setState(SwitchStatus.ENABLED.getValue());
//        user.setCreateTime(LocalDateTime.now());
//        userService.createUsers4Init(Collections.singletonList(user));
//        return user;
//    }
    private Role initSecurityRole(List<Menu> menus) {
        //创建superUser角色
        Role superUserRole = new Role();
        superUserRole.setName("安全保密员");
        superUserRole.setDescription("安全保密员角色");
        superUserRole.setCode(UserManagerTypeEnum.securityManager.getCode());
        superUserRole.setSort(0);
        superUserRole.setStatus(true);
        superUserRole.setOid("0");
        superUserRole.setCreateBy("system");
        superUserRole.setCreateTime(LocalDateTime.now());
        superUserRole.setMenus(menus);
        roleService.createRoles4Init(Collections.singletonList(superUserRole));
        return superUserRole;
    }
//    private User initSecurityUser(Role role){
//        User user = new User();
//        user.setRoles(Collections.singletonList(role));
//        user.setAccount(systemConfig.getSecurityUserName());
//        user.setPhoneNumber(systemConfig.getSecurityPhone());
//        user.setEmailAddress(systemConfig.getSecurityEmail());
//        user.setEmployeeNumber("*********");
//        user.setPassword(systemConfig.getSecurityPassword());
//        user.setName("安全保密员");
//        user.setOid("0");
//        user.setCreateBy("system");
//        user.setGender(GenderEnum.MALE.getEn());
//        user.setAdminFlag("true");
//        user.setState(SwitchStatus.ENABLED.getValue());
//        user.setCreateTime(LocalDateTime.now());
//        userService.createUsers4Init(Collections.singletonList(user));
//        return user;
//    }



}
