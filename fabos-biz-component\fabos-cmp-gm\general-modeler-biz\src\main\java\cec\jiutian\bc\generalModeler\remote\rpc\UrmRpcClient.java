//package cec.jiutian.bc.generalModeler.remote.rpc;
//
//import cec.jiutian.functionexecutor.consumer.DubboGenericAPI;
//import cec.jiutian.functionexecutor.dto.InvokeDTO;
//import io.seata.spring.annotation.GlobalTransactional;
//import jakarta.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//
//
///**
// * <AUTHOR>
// * @description:
// */
//@Service
//@Slf4j
//public class UrmRpcClient {
//    @Resource
//    private DubboGenericAPI dubboGenericAPI;
//
//    @GlobalTransactional
//    public Boolean resetPassword(String account) {
//        InvokeDTO invokeDTO = GmRpcExecutorConfigEnum.RESET_PASSWORD.buildInvokeDTO(new Object[]{account});
//        Object invoke = dubboGenericAPI.doInvoke(invokeDTO);
//        log.info(invoke.toString());
//        return Boolean.valueOf(invoke.toString());
//    }
//}
