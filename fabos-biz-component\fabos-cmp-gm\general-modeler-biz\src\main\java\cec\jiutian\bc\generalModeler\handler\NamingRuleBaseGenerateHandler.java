package cec.jiutian.bc.generalModeler.handler;


import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleBaseModel;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleModel;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


@Component
public class NamingRuleBaseGenerateHandler implements DependFiled.DynamicHandler<NamingRuleBaseModel> {

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    @SneakyThrows
    public Map<String, Object> handle(NamingRuleBaseModel namingRuleBaseModel) {
        Map<String, Object> map = new HashMap<>();
        map.put("generalCode", String.valueOf(namingRuleService.getNameCode(namingRuleBaseModel.getNamingCode(), 1, namingRuleBaseModel.getParameters()).get(0)));
        return map;
    }

}