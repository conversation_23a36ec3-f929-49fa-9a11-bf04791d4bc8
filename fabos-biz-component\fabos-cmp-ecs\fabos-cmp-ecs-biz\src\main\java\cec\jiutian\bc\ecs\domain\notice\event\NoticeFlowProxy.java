//package cec.jiutian.bc.ecs.domain.notice.event;
//
////import cec.jiutian.bc.ecs.domain.notice.service.NoticeService;
//import cec.jiutian.bc.ecs.domain.notice.entity.Notice;
//import cec.jiutian.bc.flow.enums.ExamineStatusEnum;
//import cec.jiutian.bc.flow.event.FabosWorkflowEvent;
//import cec.jiutian.view.fun.FlowProxy;
//import jakarta.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//@Component
//@Slf4j
//public class NoticeFlowProxy extends FlowProxy {
//
//
//    @Resource
//    private NoticeService noticeService;
//
//    @Override
//    public void onEvent(Object event, Object data) {
//
//        if (event instanceof FabosWorkflowEvent) {
//            log.info("**********");
//        }
//        if ( !(data instanceof Notice)) {
//            return;
//        }
//        Notice notice = (Notice) data;
//        if (!ExamineStatusEnum.AUDITED.getCode().equals(notice.getExamineStatus())) {
//            return;
//        }
//        noticeService.sendNotice(notice);
//        log.info("回调成功");
//    }
//}
