package cec.jiutian.bc.urm.domain.org.entity;

import cec.jiutian.bc.urm.enums.OrgTypeEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.ReferenceTreeType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.constant.AnnotationConst;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.BoolType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Tree;
import jakarta.persistence.Column;
import jakarta.persistence.ConstraintMode;
import jakarta.persistence.Entity;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Entity
@Table(name = "fd_org")
@FabosJson(
        name = "组织管理",
        tree = @Tree(pid = "parentOrg.id", expandLevel = 5),
        dataProxy = OrgDataProxy.class,
        orderBy = "Org.sort asc"
)
@FabosJsonI18n
@Getter
@Setter
@NoArgsConstructor
@TemplateType(type = "treeForm")
public class Org extends MetaModel {

    @Column(length = AnnotationConst.CODE_LENGTH, unique = true)
    @FabosJsonField(
            views = @View(title = "组织编码", sortable = true),
            edit = @Edit(title = "组织编码", notNull = true, search = @Search(vague = true), readonly = @Readonly(add = false, edit = true))
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "组织名称", sortable = true),
            edit = @Edit(title = "组织名称", notNull = true, search = @Search(vague = true))
    )
    private String name;

    /*是否启用UTU*/
    @FabosJsonField(
            views = @View(title = "启用UTU", sortable = true),
            edit = @Edit(title = "启用UTU", notNull = false, type = EditType.BOOLEAN, boolType = @BoolType(trueText = "是", falseText = "否")
            )
    )
    private Boolean enableUtu;

    /*是否适用零售*/
    @FabosJsonField(
            views = @View(title = "适用零售", sortable = true),
            edit = @Edit(title = "适用零售", notNull = false, type = EditType.BOOLEAN, boolType = @BoolType(trueText = "是", falseText = "否")
            )
    )
    private Boolean enableRetail;

    /*成立日期*/
    @FabosJsonField(
            views = @View(title = "成立日期", sortable = true),
            edit = @Edit(title = "成立日期", notNull = false, type = EditType.DATE)
    )
    private String establishDate;

    @FabosJsonField(
            views = @View(title = "组织类型", sortable = true),
            edit = @Edit(title = "组织类型", notNull = true, choiceType = @ChoiceType(fetchHandler = OrgTypeEnum.ChoiceFetch.class),
                    type = EditType.CHOICE)
    )
    private String orgType;

    @FabosJsonField(
            views = @View(title = "组织职能", sortable = true),
            edit = @Edit(title = "组织职能", notNull = false, type = EditType.TEXTAREA, inputType = @InputType(length = 400))
    )
    private String departmentFunction;



    @ManyToOne
    @FabosJsonField(
            edit = @Edit(
                    title = "上级组织",
                    type = EditType.REFERENCE_TREE,
                    referenceTreeType = @ReferenceTreeType(pid = "parentOrg.id", expandLevel = 3)
            )
    )
    @JoinColumn(foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    private Org parentOrg;


    @FabosJsonField(
            edit = @Edit(
                    title = "显示顺序"
            )
    )
    private Integer sort;

    @FabosJsonField(
            edit = @Edit(title = "备注", notNull = false,
                    type = EditType.TEXTAREA, inputType = @InputType(length = 400))
    )
    private String remark;

    // 为兼容自增主键的外部系统，额外增加一个自增列
    @Column(unique = true)
    private Integer autoIncrementId;


}