//package cec.jiutian.bc.ecs.inbound.remote.provider;
//
//import cec.jiutian.bc.ecs.domain.generalIp.service.GeneralIpService;
//import cec.jiutian.bc.ecs.domain.message.service.MessageService;
//import cec.jiutian.bc.ecs.domain.notice.service.NoticeService;
//import cec.jiutian.bc.ecs.dto.MessageCreateDTO;
//import cec.jiutian.bc.ecs.dto.NoticeCreateDTO;
//import cec.jiutian.bc.ecs.provider.IMessageRequestProvider;
//import org.springframework.stereotype.Component;
//
//@Component
//public class MessageRequestProvider implements IMessageRequestProvider {
//
//    private final NoticeService noticeService;
//    private final MessageService messageService;
//    private final GeneralIpService generalIpService;
//
//    public MessageRequestProvider(NoticeService noticeService, MessageService messageService, GeneralIpService generalIpService) {
//        this.noticeService = noticeService;
//        this.messageService = messageService;
//        this.generalIpService = generalIpService;
//    }
//
//    @Override
//    public Boolean receiveMsg(MessageCreateDTO messageCreateDTO) {
////        generalIpService.validateSystem(messageCreateDTO.getSysName(), messageCreateDTO.getAuthKey());
//        messageService.messageCreate(messageCreateDTO);
//        return true;
//    }
//
//    @Override
//    public Boolean sendMsgByEmail(MessageCreateDTO messageCreateDTO) {
//        messageService.sendMessageByEmail(messageCreateDTO);
//        return true;
//    }
//
//    @Override
//    public Boolean innerSendMsg(NoticeCreateDTO noticeCreateDTO) {
//        return noticeService.sendMsg(noticeCreateDTO);
//    }
//
//    @Override
//    public boolean callBack(String noticeId) {
//        noticeService.doSend(noticeId);
//        return true;
//    }
//}
