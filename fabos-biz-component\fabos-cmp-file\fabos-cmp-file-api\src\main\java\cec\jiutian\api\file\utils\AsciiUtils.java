package cec.jiutian.api.file.utils;

/**
 * <AUTHOR>
 * @time 2025-05-13 15:58
 */

public class AsciiUtils {
    public static String stringToAscii(String value) {
        char[] chars = value.toCharArray();
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < chars.length; i++) {
            if (i != chars.length - 1) {
                stringBuilder.append((int) chars[i]).append(",");
            } else {
                stringBuilder.append((int) chars[i]);
            }
        }
        return stringBuilder.toString();
    }
}
