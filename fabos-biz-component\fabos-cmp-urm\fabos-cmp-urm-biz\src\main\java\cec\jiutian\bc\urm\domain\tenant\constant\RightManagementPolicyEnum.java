package cec.jiutian.bc.urm.domain.tenant.constant;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 */
@Getter
public enum RightManagementPolicyEnum {
    TRIPARTITE("3rm", "三员管理策略"),
    SUPER("superUser", "超级管理策略");

    private final String value;
    private final String label;

    RightManagementPolicyEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static class ChoiceFetch implements ChoiceFetchHandler {
        @Override
        public List<VLModel> fetch(String[] params) {
            return Stream.of(RightManagementPolicyEnum.values()).map(SwitchStatus ->
                    new VLModel(SwitchStatus.getValue() + "", SwitchStatus.getLabel())).collect(Collectors.toList());
        }

    }
}
