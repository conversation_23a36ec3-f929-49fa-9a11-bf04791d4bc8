<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
    <Appenders>
        <Console name="STDOUT-APPENDER" target="SYSTEM_OUT">
            <PatternLayout pattern="%-5p %c{2} - %m%n%throwable" charset="UTF-8"/>
        </Console>

        <Console name="STDERR-APPENDER" target="SYSTEM_ERR">
            <PatternLayout pattern="%-5p %c{2} - %m%n%throwable" charset="UTF-8"/>
        </Console>

        <RollingFile name="ERROR-APPENDER"
                     fileName="${ctx:logging.file.path}/${ctx:spring.application.name}/common-error.log"
                     filePattern="${ctx:logging.file.path}/${ctx:spring.application.name}/common-error.log.%d{yyyy-MM-dd}"
                     append="true">
            <PatternLayout
                    pattern="%d [%X{traceId} %X{rpcId} - %X{loginUserEmail}/%X{loginUserID}/%X{remoteAddr}/%X{clientId} - %X{requestURIWithQueryString}] %-5p %c{2} - %m%n%throwable"
                    charset="UTF-8"/>
            <TimeBasedTriggeringPolicy/>
            <DefaultRolloverStrategy/>
            <ThresholdFilter level="ERROR"/>
        </RollingFile>

        <RollingFile name="WARN-APPENDER"
                     fileName="${ctx:logging.file.path}/${ctx:spring.application.name}/common-warn.log"
                     filePattern="${ctx:logging.file.path}/${ctx:spring.application.name}/common-warn.log.%d{yyyy-MM-dd}"
                     append="true">
            <PatternLayout
                    pattern="%d [%X{traceId} %X{rpcId} - %X{loginUserEmail}/%X{loginUserID}/%X{remoteAddr}/%X{clientId} - %X{requestURIWithQueryString}] %-5p %c{2} - %m%n%throwable"
                    charset="UTF-8"/>
            <TimeBasedTriggeringPolicy/>
            <DefaultRolloverStrategy/>
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="WARN" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </RollingFile>

        <RollingFile name="APP-DEFAULT-APPENDER"
                     fileName="${ctx:logging.file.path}/${ctx:spring.application.name}/app-default.log"
                     filePattern="${ctx:logging.file.path}/${ctx:spring.application.name}/app-default.log.%d{yyyy-MM-dd-HH}"
                     append="true">
            <PatternLayout
                    pattern="%d [%X{traceId} %X{rpcId} - %X{loginUserEmail}/%X{loginUserID}/%X{remoteAddr}/%X{clientId} - %X{requestURIWithQueryString}] %-5p %c{2} - %m%n%throwable"
                    charset="UTF-8"/>
            <TimeBasedTriggeringPolicy/>
            <DefaultRolloverStrategy/>
        </RollingFile>

        <RollingFile name="SPRING-APPENDER" fileName="${ctx:logging.file.path}/spring/spring.log"
                     filePattern="${ctx:logging.file.path}/spring/spring.log.%d{yyyy-MM-dd}"
                     append="true">
            <PatternLayout
                    pattern="%d [%X{traceId} %X{rpcId} - %X{loginUserEmail}/%X{loginUserID}/%X{remoteAddr}/%X{clientId} - %X{requestURIWithQueryString}] %-5p %c{2} - %m%n%throwable"
                    charset="UTF-8"/>
            <TimeBasedTriggeringPolicy/>
            <DefaultRolloverStrategy/>
        </RollingFile>

        <RollingFile name="NO-USAGE-APPENDER" fileName="${ctx:logging.file.path}/no-usage/no-usage.log"
                     filePattern="${ctx:logging.file.path}/no-usage/no-usage.log.%d{yyyy-MM-dd}"
                     append="true">
            <PatternLayout
                    pattern="%d [%X{traceId} %X{rpcId} - %X{loginUserEmail}/%X{loginUserID}/%X{remoteAddr}/%X{clientId} - %X{requestURIWithQueryString}] %-5p %c{2} - %m%n%throwable"
                    charset="UTF-8"/>
            <TimeBasedTriggeringPolicy/>
            <DefaultRolloverStrategy/>
        </RollingFile>
    </Appenders>

    <Loggers>
        <Logger name="STDOUT" additivity="false" level="info">
            <AppenderRef ref="STDOUT-APPENDER"/>
        </Logger>

        <Logger name="STDERR" additivity="false" level="${ctx:logging.level.com.alipay.sofa}">
            <AppenderRef ref="STDERR-APPENDER"/>
        </Logger>

        <Logger name="com.alipay.sofa" additivity="false" level="${ctx:logging.level.com.alipay.sofa}">
            <AppenderRef ref="APP-DEFAULT-APPENDER"/>
            <AppenderRef ref="WARN-APPENDER"/>
            <AppenderRef ref="ERROR-APPENDER"/>
            <AppenderRef ref="STDOUT-APPENDER"/>
        </Logger>

        <Logger name="org.springframework" additivity="false"
                level="${ctx:logging.level.com.alipay.sofa}">
            <AppenderRef ref="ERROR-APPENDER"/>
            <AppenderRef ref="WARN-APPENDER"/>
            <AppenderRef ref="SPRING-APPENDER"/>
            <AppenderRef ref="STDOUT-APPENDER"/>
        </Logger>

        <Logger name="net.sf.ehcache" additivity="false"
                level="${ctx:logging.level.net.sf.ehcache}">
            <AppenderRef ref="APP-DEFAULT-APPENDER"/>
            <AppenderRef ref="WARN-APPENDER"/>
            <AppenderRef ref="ERROR-APPENDER"/>
            <AppenderRef ref="STDOUT-APPENDER"/>
        </Logger>

        <Root level="${ctx:logging.level.com.alipay.sofa}">
            <AppenderRef ref="APP-DEFAULT-APPENDER"/>
            <AppenderRef ref="WARN-APPENDER"/>
            <AppenderRef ref="ERROR-APPENDER"/>
            <AppenderRef ref="STDOUT-APPENDER"/>
        </Root>
    </Loggers>
</Configuration>
