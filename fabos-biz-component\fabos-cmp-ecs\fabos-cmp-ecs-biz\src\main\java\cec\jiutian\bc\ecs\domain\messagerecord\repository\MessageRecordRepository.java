package cec.jiutian.bc.ecs.domain.messagerecord.repository;

import cec.jiutian.bc.ecs.domain.messagerecord.entity.MessageRecord;
import cec.jiutian.bc.ecs.domain.messagerecord.entity.MessageRecordDto;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface MessageRecordRepository extends JpaRepository<MessageRecord, String>, JpaSpecificationExecutor<MessageRecord> {
    @Query("""
            select new cec.jiutian.bc.ecs.domain.messagerecord.entity.MessageRecordDto (
                                   m.id,
                                   m.createTime,
                                   m.updateTime,
                                   m.title,
                                   m.dispatchWay,
                                   m.immediateAlarmFlag,
                                   m.dispatchTime,
                                   m.content,
                                   m.status,
                                   m.isProcess,
                                   m.processCompleteFlag,
                                   m.processDetail,
                                   m.processTime
                               )
                      from MessageRecord m
            where m.dispatchUser = ?1 and m.dispatchWay = ?2 and m.status = ?3
            order by m.createTime DESC""")
    List<MessageRecordDto> findByDispatchUserAndDispatchWayAndStatusOrderByCreateTimeDesc(String dispatchUser, String dispatchWay, String status, Pageable pageable);
}