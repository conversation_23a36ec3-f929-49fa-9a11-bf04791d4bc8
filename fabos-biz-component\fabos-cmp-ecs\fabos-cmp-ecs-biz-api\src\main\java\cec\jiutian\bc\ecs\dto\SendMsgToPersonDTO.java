package cec.jiutian.bc.ecs.dto;

import cec.jiutian.bc.ecs.enums.MessageWayEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Set;

@Data
@Slf4j
public class SendMsgToPersonDTO implements Serializable {

    private String title;

    private String content;

    private String sendBy;

    /**
     * 消息类型
     */
    private MessageWayEnum way;

    /**
     * 电话号码
     */
    private Set<String> receivers;

    public static boolean argsCheck(SendMsgToPersonDTO sendMsgToPersonDTO) {
        if (sendMsgToPersonDTO == null) {
            log.error("参数不能为空");
            return false;
        }
        if (sendMsgToPersonDTO.getWay() == null) {
            log.error("消息类型不能为空");
            return false;
        }
        if (sendMsgToPersonDTO.getReceivers() == null || sendMsgToPersonDTO.getReceivers().size() == 0) {
            log.error("接收人列表不能为空");
            return false;
        }
        if (StringUtils.isEmpty(sendMsgToPersonDTO.getTitle())) {
            log.error("标题不能为空");
            return false;
        }
        if (StringUtils.isEmpty(sendMsgToPersonDTO.getContent())) {
            log.error("内容不能为空");
            return false;
        }
        return true;
    }

}
