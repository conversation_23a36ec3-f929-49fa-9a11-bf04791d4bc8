package cec.jiutian.bc.ecs.dto;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Set;

@Data
@Slf4j
public class SendAlarmMessageDTO implements Serializable {

    private String title;

    private String content;

    private String sendBy;

    /**
     * 用户ID
     */
    private Set<String> receivers;

    public static boolean argsCheck(SendAlarmMessageDTO sendAlarmMessageDTO) {
        if (sendAlarmMessageDTO == null) {
            log.error("参数不能为空");
            return false;
        }
        if (sendAlarmMessageDTO.getReceivers() == null || sendAlarmMessageDTO.getReceivers().size() == 0) {
            log.error("接收人列表不能为空");
            return false;
        }
        if (StringUtils.isEmpty(sendAlarmMessageDTO.getContent())) {
            log.error("内容不能为空");
            return false;
        }
        return true;
    }

}
