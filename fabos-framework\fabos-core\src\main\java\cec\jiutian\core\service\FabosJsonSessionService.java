package cec.jiutian.core.service;

import cec.jiutian.bc.urm.dto.MetaUserinfo;
import cec.jiutian.common.constant.AuthorizationConstant;
import cec.jiutian.common.constant.SessionKey;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.util.JacksonUtil;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.prop.FabosJsonProp;
import com.fasterxml.jackson.core.JsonProcessingException;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class FabosJsonSessionService {

    @Resource
    private FabosJsonProp fabosJsonProp;

    @Resource
    private HttpServletRequest request;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    public Long getExpire(String key) {
        return stringRedisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    public void put(String key, String str) {
        stringRedisTemplate.opsForValue().set(key, str);
    }

    /**
     * 存储redis
     *
     * @param key     键
     * @param str     值
     * @param timeout 超时时间，秒
     */
    public void put(String key, String str, long timeout) {
        this.put(key, str, timeout, TimeUnit.SECONDS);
    }

    public void put(String key, String str, long timeout, TimeUnit timeUnit) {
        if (fabosJsonProp.isRedisSession()) {
            if (timeout <= 0) {
                stringRedisTemplate.opsForValue().set(key, str);
            } else {
                stringRedisTemplate.opsForValue().set(key, str, timeout, timeUnit);
            }
        } else {
            request.getSession().setAttribute(key, str);
        }
    }

    public void remove(String key) {
        if (fabosJsonProp.isRedisSession()) {
            stringRedisTemplate.delete(key);
        } else {
            request.getSession().removeAttribute(key);
        }
    }

    public void remove(Collection<String> keys) {
        stringRedisTemplate.delete(keys);
    }

    public void removeAll(String prefix) {
        Set<String> keys = stringRedisTemplate.keys(prefix + SessionKey.ASTERISK);
        stringRedisTemplate.delete(keys);
    }

    //延长key过期时间
    public void expire(String key, long timeout, final TimeUnit unit) {
        if (fabosJsonProp.isRedisSession()) {
            stringRedisTemplate.expire(key, timeout, unit);
        }
    }

    public Object get(String key) {
        if (fabosJsonProp.isRedisSession()) {
            return stringRedisTemplate.opsForValue().get(key);
        } else {
            return request.getSession().getAttribute(key);
        }
    }

    public String getAsString(String key) {
        Object o = null;
        if (fabosJsonProp.isRedisSession()) {
            o = stringRedisTemplate.opsForValue().get(key);
        } else {
            o = request.getSession().getAttribute(key);
        }
        return (Objects.isNull(o) ? null : o.toString());
    }

    public <T> T get(String key, Class<T> clazz) {
        if (fabosJsonProp.isRedisSession()) {
            if (null == this.get(key)) {
                return null;
            } else {
                try {
                    return JacksonUtil.fromJson(this.get(key).toString(), clazz);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        } else {
            try {
                return JacksonUtil.fromJson(request.getSession().getAttribute(key).toString(), clazz);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }
    public <T> List<T> fromJsonToList(String key, Class<T> clazz) {
        if (fabosJsonProp.isRedisSession()) {
            if (null == this.get(key)) {
                return null;
            } else {
                try {
                    return JacksonUtil.fromJsonToList(this.get(key).toString(), clazz);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        } else {
            try {
                return JacksonUtil.fromJsonToList(request.getSession().getAttribute(key).toString(), clazz);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    public void putMap(String key, Map<String, Object> map, long expire) {
        if (fabosJsonProp.isRedisSession()) {
            BoundHashOperations<?, String, Object> boundHashOperations = stringRedisTemplate.boundHashOps(key);
            map.replaceAll((k, v) -> {
                try {
                    return JacksonUtil.toJson(v);
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            });
            boundHashOperations.putAll(map);
            boundHashOperations.expire(expire, TimeUnit.MINUTES);
        } else {
            request.getSession().setAttribute(key, map);
        }
    }

    public Map<Object, Object> getMap(String key) {
        return this.stringRedisTemplate.opsForHash().entries(key);
    }

    public void putValueToMap(String key, String mapKey, Object mapValue) {
        this.stringRedisTemplate.opsForHash().put(key, mapKey, mapValue);
    }

    public Object getValueFromMap(String key, String mapKey) {
        return this.stringRedisTemplate.opsForHash().get(key, mapKey);
    }

    //获取map的所有key
    public List<String> getMapKeys(String key) {
        if (fabosJsonProp.isRedisSession()) {
            Set<Object> set = stringRedisTemplate.opsForHash().keys(key);
            return set.stream().map(Object::toString).collect(Collectors.toList());
        } else {
            Map<String, Object> map = (Map<String, Object>) request.getSession().getAttribute(key);
            if (null == map) {
                return null;
            }
            return new ArrayList<>(map.keySet());
        }
    }

    public <T> T getMapValue(String key, String mapKey, Class<T> type) {
        if (fabosJsonProp.isRedisSession()) {
            Object obj = stringRedisTemplate.boundHashOps(key).get(mapKey);
            if (null == obj) {
                return null;
            }
            try {
                return JacksonUtil.fromJson(obj.toString(), type);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        } else {
            Map<String, T> map = (Map<String, T>) request.getSession().getAttribute(key);
            if (null == map) {
                return null;
            }
            return map.get(mapKey);
        }
    }

    //获取当前请求token
    public String getCurrentToken() {
        return UserContext.getToken();
    }

    /**
     * 获取此次请求的Authorization（如附带Bearer前缀，会移除）
     *
     * @return 此次请求的Authorization
     */
    public String getAuthorization() {
        String tokenFromHeader = request.getHeader(AuthorizationConstant.AUTHORIZATION);
        String tokenFromParam = request.getParameter(AuthorizationConstant.AUTHORIZATION);
        String token = StringUtils.isNotBlank(tokenFromHeader) ? tokenFromHeader : tokenFromParam;
        return StringUtils.isBlank(token) ? token : token.replace(AuthorizationConstant.AUTHORIZATION_PREFIX, "");
    }

    /**
     * 获取此次请求之客户端的IP地址
     *
     * @return IP地址
     */
    public String getClientIpAddress() {
        return request.getRemoteAddr().replace(SessionKey.COLON,  SessionKey.UNDERLINE);
    }

    public MetaUserinfo getSimpleUserInfo() {
        String token = getCurrentToken();
        if (StringUtils.isBlank(token)) {
            log.warn("Token is empty in UserContext, send re-login");
            return null;
        }
        Object info = get(SessionKey.USER_INFO + token);
        try {
            if (null == info) {
                log.warn("Token is empty in Redis, deleting all user keys");
                for (String uk : SessionKey.USER_KEY_GROUP) {
                    remove(uk + token);
                }
                return null;
            }
            return JacksonUtil.fromJson(info.toString(), MetaUserinfo.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public void refreshTokenTime(String token,Integer times,TimeUnit unit){
        String key = SessionKey.TOKEN_OLINE + token;
        Boolean expire = stringRedisTemplate.expire(key, times, unit);
    }
}
