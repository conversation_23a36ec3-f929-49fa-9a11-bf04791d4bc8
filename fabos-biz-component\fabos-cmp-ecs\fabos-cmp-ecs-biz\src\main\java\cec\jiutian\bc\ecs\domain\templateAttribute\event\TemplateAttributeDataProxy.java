package cec.jiutian.bc.ecs.domain.templateAttribute.event;

import cec.jiutian.bc.ecs.domain.templateAttribute.entity.TemplateAttribute;
import cec.jiutian.view.fun.DataProxy;

public class TemplateAttributeDataProxy implements DataProxy<TemplateAttribute> {
    @Override
    public void beforeAdd(TemplateAttribute templateAttribute) {
//        templateAttribute.setTemplateName(templateAttribute.getTemplate().getTemplateName());
    }
}
