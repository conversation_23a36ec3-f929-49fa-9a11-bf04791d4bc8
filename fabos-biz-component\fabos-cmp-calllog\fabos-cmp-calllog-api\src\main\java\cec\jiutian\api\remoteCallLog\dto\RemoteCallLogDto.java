package cec.jiutian.api.remoteCallLog.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class RemoteCallLogDto {
    // 业务数据的主键
    private String bussinessDataId;

    // 业务数据的类名（cec.jiutian.bc.keepSample.domain.stocktakingOrder.model.StocktakingOrderDetail）
    private Class bussinessModelClass;

    // 调用方式（rest、feign、dubbo）
    private String callMethod;

    // 接口名称
    private String interfaceName;

    // 发起方系统编码
    private String issuerCode;

    // 远程目标系统编码(ERP、MES、WMS)
    private String remoteTargetCode;

    // 请求地址
    private String requestUrl;

    // 请求时间
    private Date requestTime;

    // 请求消息体
    private String requestBody;

    // 响应消息体
    private String responseBody;

    // 是否成功 （重试成功也更新该标记）
    private Boolean successFlag;

}
