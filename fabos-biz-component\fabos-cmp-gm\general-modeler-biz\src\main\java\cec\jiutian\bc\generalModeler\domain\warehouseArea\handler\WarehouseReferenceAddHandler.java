package cec.jiutian.bc.generalModeler.domain.warehouseArea.handler;

import cec.jiutian.bc.generalModeler.domain.material.model.StockCategoryView;
import cec.jiutian.bc.generalModeler.domain.warehouseArea.model.Warehouse;
import cec.jiutian.bc.generalModeler.domain.warehouseArea.model.WarehouseDetail;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.ReferenceAddType;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2024/11/18 17:35
 * @description：
 */
@Component
public class WarehouseReferenceAddHandler implements ReferenceAddType.ReferenceAddHandler<Warehouse, StockCategoryView> {

    @Resource
    private JpaCrud jpaCrud;

    @Override
    public Map<String, Object> handle(Warehouse warehouse, List<StockCategoryView> stockCategoryViewList) {
        Map<String, Object> result = new HashMap<>();
        List<WarehouseDetail> warehouseDetails = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(stockCategoryViewList)) {
            stockCategoryViewList.forEach(c -> {
                WarehouseDetail warehouseDetail = new WarehouseDetail();
                warehouseDetail.setMaterialCategoryId(c.getId());
                warehouseDetail.setCode(c.getCode());
                warehouseDetail.setName(c.getName());
                warehouseDetails.add(warehouseDetail);
            });
            result.put("warehouseDetails", warehouseDetails);
        }
        return result;
    }
}
