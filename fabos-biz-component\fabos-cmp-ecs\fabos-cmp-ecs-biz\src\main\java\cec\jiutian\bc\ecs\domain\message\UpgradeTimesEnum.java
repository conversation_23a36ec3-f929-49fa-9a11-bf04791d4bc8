package cec.jiutian.bc.ecs.domain.message;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
public enum UpgradeTimesEnum {

    ONE(1L, "0次"),
    TWO(2L, "1次"),
    THREE(3L, "2次"),
    FOUR(4L, "3次"),
    FIVE(5L, "4次"),
    ;
    private Long value;

    private String label;

    UpgradeTimesEnum(Long value, String label) {
        this.value = value;
        this.label = label;
    }

    public static class ChoiceFetch implements ChoiceFetchHandler {
        @Override
        public List<VLModel> fetch(String[] params) {
            return Stream.of(UpgradeTimesEnum.values())
                    .map(messageWayEnum -> new VLModel(messageWayEnum.getValue(), messageWayEnum.getLabel()))
                    .collect(Collectors.toList());

        }

    }
}
