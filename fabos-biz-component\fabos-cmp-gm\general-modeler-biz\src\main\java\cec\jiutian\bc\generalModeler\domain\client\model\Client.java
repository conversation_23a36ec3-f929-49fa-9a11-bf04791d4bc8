package cec.jiutian.bc.generalModeler.domain.client.model;

import cec.jiutian.bc.generalModeler.domain.client.proxy.ClientDataProxy;
import cec.jiutian.bc.generalModeler.enumeration.SupplierIndustryTypeEnum;
import cec.jiutian.core.frame.constant.RegexConst;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.Search;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Table(name = "client")
@FabosJson(
        name = "客户管理",
        dataProxy = ClientDataProxy.class
)
@Entity
@Getter
@Setter
public class Client extends MetaModel {

    @FabosJsonField(
            views = @View(title = "客户分类"),
            edit = @Edit(title = "客户分类",
                    notNull = true, search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    private String category;

    @FabosJsonField(
            views = @View(title = "客户编码"),
            edit = @Edit(title = "客户编码",
                    readonly = @Readonly(add = false),
                    notNull = true, search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "客户外部代号"),
            edit = @Edit(title = "客户外部代号",
                    notNull = true, search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    private String externalCode;

    @FabosJsonField(
            views = @View(title = "客户全称"),
            edit = @Edit(title = "客户全称", notNull = true, search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "统一社会信用代码"),
            edit = @Edit(title = "统一社会信用代码", notNull = true, search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    private String unifiedSocialCreditIdentifier;

    @FabosJsonField(
            views = @View(title = "经济类型代码"),
            edit = @Edit(title = "经济类型代码", notNull = true, search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    private String economyCategoryCode;

    @FabosJsonField(
            views = @View(title = "注册国家/地区"),
            edit = @Edit(title = "注册国家/地区", notNull = true, search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    private String registryCountry;

    @FabosJsonField(
            views = @View(title = "法人姓名"),
            edit = @Edit(title = "法人姓名", notNull = true, search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    private String legalPersonName;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "成立日期", type = ViewType.DATE),
            edit = @Edit(title = "成立日期", notNull = true, search = @Search(vague = true),
                    type = EditType.DATE,
                    dateType = @DateType(type = DateType.Type.DATE))
    )
    private Date establishmentDate;

    @FabosJsonField(
            views = @View(title = "注册地址"),
            edit = @Edit(title = "注册地址", notNull = true,
                    inputType = @InputType(length = 40))
    )
    private String address;

    @FabosJsonField(
            views = @View(title = "注册地区代码"),
            edit = @Edit(title = "注册地区代码", notNull = true,
                    inputType = @InputType(length = 20))
    )
    private String registeredRegionCode;

    @FabosJsonField(
            views = @View(title = "注册地区邮编"),
            edit = @Edit(title = "注册地区邮编", notNull = true,
                    inputType = @InputType(length = 20))
    )
    private String registeredRegionPostCode;

    @FabosJsonField(
            views = @View(title = "客户单位网址"),
            edit = @Edit(title = "客户单位网址",
                    inputType = @InputType(length = 40))
    )
    private String websiteAddress;

    @FabosJsonField(
            views = @View(title = "联系人姓名"),
            edit = @Edit(title = "联系人姓名",
                    inputType = @InputType(length = 20))
    )
    private String contactName;

    @FabosJsonField(
            views = @View(title = "联系电话"),
            edit = @Edit(title = "联系电话", notNull = true,
                    inputType = @InputType(length = 20, regex = RegexConst.PHONE_EXT_REGEX))
    )
    private String phoneNumber;

    @FabosJsonField(
            views = @View(title = "电子邮箱"),
            edit = @Edit(title = "电子邮箱", notNull = true,
                    inputType = @InputType(regex = RegexConst.EMAIL_REGEX))
    )
    private String email;

    @FabosJsonField(
            views = @View(title = "开户银行名称"),
            edit = @Edit(title = "开户银行名称",
                    inputType = @InputType(length = 20))
    )
    private String depositBank;

    @FabosJsonField(
            views = @View(title = "开户银行账号"),
            edit = @Edit(title = "开户银行账号",
                    inputType = @InputType(length = 40))
    )
    private String depositBankNumber;

    @FabosJsonField(
            views = @View(title = "所属行业"),
            edit = @Edit(title = "所属行业", notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = SupplierIndustryTypeEnum.class))
    )
    private String industryType;


    @FabosJsonField(
            views = @View(title = "注册资本", rowEdit = true),
            edit = @Edit(title = "注册资本", notNull = true,
                    numberType = @NumberType(min = 0))
    )
    private Double registeredAssets;

    @FabosJsonField(
            views = @View(title = "实缴资本", rowEdit = true),
            edit = @Edit(title = "实缴资本", notNull = true, numberType = @NumberType(min = 0))
    )
    private Double contributedAssets;

    @FabosJsonField(
            views = @View(title = "经营范围"),
            edit = @Edit(title = "经营范围",
                    inputType = @InputType(length = 20))
    )
    private String businessScope;

    @FabosJsonField(
            views = @View(title = "客户性质"),
            edit = @Edit(title = "客户性质",
                    inputType = @InputType(length = 20))
    )
    private String clientCharacter;

    @FabosJsonField(
            views = @View(title = "客户考核评级"),
            edit = @Edit(title = "客户考核评级",
                    inputType = @InputType(length = 20))
    )
    private String clientGrade;

    @FabosJsonField(
            views = @View(title = "客户状态"),
            edit = @Edit(title = "客户状态",
                    inputType = @InputType(length = 20))
    )
    private String clientState;

    @FabosJsonField(
            views = @View(title = "客户类型"),
            edit = @Edit(title = "客户类型",
                    inputType = @InputType(length = 20))
    )
    private String clientType;

    @FabosJsonField(
            views = @View(title = "客户规模"),
            edit = @Edit(title = "客户规模",
                    inputType = @InputType(length = 20))
    )
    private String clientScale;

    @FabosJsonField(
            views = @View(title = "客户级别"),
            edit = @Edit(title = "客户级别",
                    inputType = @InputType(length = 20))
    )
    private String clientRank;

}
