package cec.jiutian.bc.file.remote.feign;


import cec.jiutian.api.file.dto.FeignString;
import cec.jiutian.api.file.utils.FileUtil;
import cec.jiutian.api.remoteCallLog.dto.RemoteCallResult;
import cec.jiutian.bc.file.model.FabosJsonFile;
import cec.jiutian.bc.file.service.FileService;
import cec.jiutian.common.exception.MesErrorCodeException;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import com.alibaba.fastjson2.JSON;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.Serializable;
import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2025-03-19 15:58
 */

@RestController
@RequestMapping(FabosJsonRestPath.FABOS_REMOTE_API_OLD)
@Slf4j
@AllArgsConstructor
public class FileFeign {

    private final FileService fileService;

    @ApiOperation(value = "上传文件", notes = "上传文件的远程调用接口，此接口将进行数据库操作，且将文件上传至服务器，并返回文件对应的UUID")
    @PostMapping(value = "/fileUploadAndSaveData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public FeignString fileUpload(@RequestPart MultipartFile file,
                                  @RequestParam(required = false) String filePath,
                                  @RequestParam(required = false) String sourceCode,
                                  @RequestParam(required = false) String lastEventUser,
                                  @RequestParam(required = false) String functionFolder) throws IOException {
        FabosJsonFile fabosJsonFile = new FabosJsonFile(file);
        String modifiedFileName = FileUtil.processFileRenaming(fabosJsonFile);
        String errorMessage = null;
        try {
            errorMessage = fileService.createFile(file, null, modifiedFileName, fabosJsonFile);
        } catch (Exception e) {
            log.error("fabosJson upload error", e);
            throw e;
        }
        FeignString feignString = new FeignString();
        feignString.setFeignString(StringUtils.isBlank(errorMessage) ? fabosJsonFile.getFileUUID() : errorMessage);
        return feignString;
    }

    @ApiOperation(value = "上传文件", notes = "上传文件的远程调用接口，此接口将进行数据库操作，且将文件上传至服务器，并返回文件对应的UUID")
    @PostMapping(value = "/fileUpload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public RemoteCallResult<String> fileUpload(@RequestPart(value = "file") MultipartFile file,
                                               @RequestParam(value = "fabosJsonFileSuper") String fabosJsonFileSuperStr) throws IOException {
        String errorMessage = null;
        FabosJsonFile fabosJsonFile = null;
        try {
            fabosJsonFile = JSON.parseObject(fabosJsonFileSuperStr, FabosJsonFile.class);
            errorMessage = fileService.createFile(file, null, fabosJsonFile.getModifiedFileName(), fabosJsonFile);
        } catch (Exception e) {
            log.error("fabosJson upload error", e);
            throw e;
        }
        RemoteCallResult<String> result = new RemoteCallResult<>();
        result.setMessage(errorMessage);
        result.setData(StringUtils.isNotBlank(errorMessage) ? null : fabosJsonFile.getModifiedFileName());
        return result;
    }

    /**
     * 大文件上传可能出现超时现象
     */
    @PostMapping(value = "/multiFileUploadAndSaveData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public FeignString multiFileUploadAndSaveData(@RequestPart MultipartFile[] files, @RequestParam(required = false)  String filePath,
                                                  @RequestParam(required = false) String sourceCode, @RequestParam(required = false) String lastEventUser,
                                                  @RequestParam(required = false) String functionFolder) throws IOException {
        if (files.length > 0) {
            StringBuilder stringBuilder = new StringBuilder();
            for (MultipartFile file : files) {
                try {
                    FeignString feignString = fileUpload(file, null, null, null, null);
                    stringBuilder.append(feignString.getFeignString()).append(",");
                } catch (Exception e) {
                    log.error("fabosJson upload error", e);
                    throw e;
                }
            }
            return new FeignString(StringUtils.removeEnd(stringBuilder.toString(), ","));
        } else {
            return null;
        }
    }

    @ApiOperation(value = "修改文件名", notes = "修改文件名称，不对文件本身做修改，仅修改数据库对应数据的文件名字段")
    @PostMapping("/changeFilename")
    public void changeFilename(@RequestBody FileOperation fileOperation) {
        if (Objects.isNull(fileOperation) || StringUtils.isBlank(fileOperation.getId()) || StringUtils.isBlank(fileOperation.getName())) {
            throw new MesErrorCodeException("请确保文件ID、新名称不为空");
        }
        fileService.changeFilename(fileOperation.getId(), fileOperation.getName());
    }

    //    @ApiOperation(value = "根据uuid查询文件信息", notes = "查询文件的远程调用接口，各服务调用此接口时需要传入文件的uuid，该操作会查询数据库数据和对应的文件。相关表：USR_UPLD_TRNSCN_FL")
//    @PostMapping("/getFileInfoByUUIDs")
//    public List<FileInfoFeignDTO> getFileInfoByUUIDs(@RequestParam List<String> uuids) {
//        BizParamUtils.setOperationName(BusinessDefinition.FileOperation.FILE_OPEN);
//        List<FrUploadTransactionFilePO> result = frUploadTransactionFileService.getFileInfoByUUIDs(uuids);
//        if (CollectionUtils.isEmpty(result)) {
//            return Collections.emptyList();
//        }
//        return result.stream().map(iv -> new FileInfoFeignDTO(iv)).collect(Collectors.toList());
//    }
//
    @ApiOperation(value = "根据uuid删除文件", notes = "删除文件的远程调用接口，各服务调用此接口时需要传入文件的uuid，该操作会删除数据库数据和对应的文件。相关表：USR_UPLD_TRNSCN_FL")
    @PostMapping("/deleteFileById")
    public void deleteFileById(@RequestParam String uuid) {
        fileService.deleteSingle(uuid);
    }

    @ApiOperation(value = "根据uuid列表删除文件", notes = "删除文件的远程调用接口，使用逗号隔开的uuid列表的字符串进行文件的批量删除，，该操作会删除数据库数据和对应的文件。相关表：USR_UPLD_TRNSCN_FL")
    @PostMapping("/deleteBatchFile")
    public void deleteBatchFile(@RequestParam String uuids) {
        if (StringUtils.isBlank(uuids)) {
            return;
        }
        fileService.deleteMultiple(Arrays.asList(uuids.split(",")));
    }

//    /**
//     * 大文件上传可能出现超时现象
//     */
//    @PostMapping(value = "/multiFileUploadAndSaveDataForFront", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
//    public String multiFileUploadAndSaveDataForFront(@RequestPart MultipartFile[] files, @RequestParam String filePath,
//                                                     @RequestParam String functionFolder, @RequestParam String fileOperation) {
//        BizParamUtils.setOperationName(BusinessDefinition.FileOperation.FILE_CREATE);
//        return fileBizService.multiFileUploadAndSaveData(files, filePath, functionFolder, fileOperation);
//    }

    @PostMapping(value = "/downloadById")
    public void downloadById(@RequestParam String uuid, HttpServletResponse response) {
        fileService.download(uuid, response);
    }

    @Data
    public static class FileOperation implements Serializable {
        @ApiModelProperty("文件的UUID")
        private String id;
        @ApiModelProperty("目前支持更新和删除")
        private Operation operation;
        @ApiModelProperty("如果operation为UPDATE，name的值表示更新该文件的名称")
        private String name;

        public enum Operation {
            RENAME,
            DELETE,
        }
    }


}

