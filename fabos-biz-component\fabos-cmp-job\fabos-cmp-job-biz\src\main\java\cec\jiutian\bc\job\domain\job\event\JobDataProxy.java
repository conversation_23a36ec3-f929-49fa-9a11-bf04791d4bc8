package cec.jiutian.bc.job.domain.job.event;

import cec.jiutian.core.frame.exception.FabosJsonWebApiRuntimeException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.frame.util.FabosJsonPowerUtil;
import cec.jiutian.bc.job.domain.job.entity.JobExecDialog;
import cec.jiutian.bc.job.domain.job.entity.JobInfo;
import cec.jiutian.bc.job.domain.job.service.JobService;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.persistence.Transient;
import org.quartz.SchedulerException;
import org.quartz.impl.triggers.CronTriggerImpl;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.List;

/**
 *
 */
@Service
public class JobDataProxy implements DataProxy<JobInfo>, OperationHandler<JobInfo, JobExecDialog> {

    @Transient
    private JobService jobService;

    private FabosJsonDao dao;

    public JobDataProxy(JobService jobService, FabosJsonDao dao) {
        this.jobService = jobService;
        this.dao = dao;
    }

    @Override
    public void beforeAdd(JobInfo job) {
        CronTriggerImpl trigger = new CronTriggerImpl();
        try {
            trigger.setCronExpression(job.getCron());
        } catch (Exception e) {
            throw new FabosJsonWebApiRuntimeException("Cron error " + e.getMessage(), e);
        }
    }

    @Override
    public void beforeUpdate(JobInfo job) {
        beforeAdd(job);
    }

    @Override
    public void afterAdd(JobInfo job) {
        try {
            jobService.addJob(job);
        } catch (SchedulerException | ParseException e) {
            throw new FabosJsonWebApiRuntimeException(e.getMessage());
        }
    }

    @Override
    public void afterUpdate(JobInfo job) {
        try {
            jobService.modifyJob(job);
        } catch (SchedulerException | ParseException e) {
            throw new FabosJsonWebApiRuntimeException(e.getMessage());
        }
    }

    @Override
    public void afterDelete(JobInfo job) {
        try {
            jobService.deleteJob(job);
        } catch (SchedulerException e) {
            throw new FabosJsonWebApiRuntimeException(e.getMessage());
        }
    }

    @Override
    public String exec(List<JobInfo> jobs, JobExecDialog param, String[] operationParam) {
        try {
            for (JobInfo job : jobs) {
                dao.detach(job);
                if (param !=null){
                    job.setHandlerParam(param.getParam());
                }
                jobService.triggerJob(job);
            }
            return null;
        } catch (Exception e) {
            throw new FabosJsonWebApiRuntimeException(e.getMessage(), e);
        }
    }

    @Override
    public void addBehavior(JobInfo job) {
        job.setCode(FabosJsonPowerUtil.generateCode(8));
    }
}
