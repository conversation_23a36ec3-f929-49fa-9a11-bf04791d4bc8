//package cec.jiutian.bc.ecs.domain.userMessageRecord.event;
//
//import cec.jiutian.bc.ecs.domain.message.entity.Message;
//import cec.jiutian.bc.ecs.domain.message.repository.MsgRepository;
//import cec.jiutian.bc.ecs.domain.upgrade.repository.MsgUpgradeRepository;
//import cec.jiutian.bc.ecs.domain.userMessageRecord.entity.UserMessageRecord;
//import cec.jiutian.bc.infra.business.definition.BusinessDefinition;
//import cec.jiutian.core.frame.constant.YesOrNoStatus;
//import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
//import cec.jiutian.view.fun.OperationHandler;
//import jakarta.annotation.Resource;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.time.LocalDateTime;
//import java.util.List;
//import java.util.Optional;
//
//@Transactional
//@Component
//public class DoneMsgRecordOperationHandler implements OperationHandler<UserMessageRecord, Void> {
//
//
//    private final FabosJsonDao fabosJsonDao;
//
//    @Resource
//    private MsgUpgradeRepository msgUpgradeRepository;
//    @Resource
//    private MsgRepository msgRepository;
//
//    public DoneMsgRecordOperationHandler(FabosJsonDao fabosJsonDao) {
//        this.fabosJsonDao = fabosJsonDao;
//    }
//
//    @Override
//    public String exec(List<UserMessageRecord> data, Void modelObject, String[] param) {
//        UserMessageRecord userMessageRecord = data.get(0);
//        userMessageRecord.setDone(YesOrNoStatus.YES.getValue());
//        userMessageRecord.setStatus(YesOrNoStatus.YES.getValue());
//        userMessageRecord.setUpdateTime(LocalDateTime.now());
//        msgUpgradeRepository.deleteById(userMessageRecord.getMessage().getId());
//        Optional<Message> message = msgRepository.findById(userMessageRecord.getMessage().getId());
//        message.ifPresent(msg -> {
////            msg.setMessageStatus(BusinessDefinition.MessageStatus.PROCESSED);
//            msg.setUpdateTime(LocalDateTime.now());
//            msgRepository.saveAndFlush(msg);
//        });
//        fabosJsonDao.mergeAndFlush(userMessageRecord);
//        return "已处理";
//    }
//}
