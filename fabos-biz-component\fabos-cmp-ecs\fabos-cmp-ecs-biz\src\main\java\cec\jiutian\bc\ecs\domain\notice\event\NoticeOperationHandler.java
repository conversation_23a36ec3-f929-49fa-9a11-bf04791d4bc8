//package cec.jiutian.bc.ecs.domain.notice.event;
//
//import cec.jiutian.bc.ecs.domain.notice.entity.Notice;
//import cec.jiutian.bc.flow.enums.ExamineStatusEnum;
//import cec.jiutian.common.util.BeanUtils;
//import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
//import cec.jiutian.core.frame.module.MetaUser;
//import cec.jiutian.view.fun.OperationHandler;
//import jakarta.annotation.Resource;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.util.CollectionUtils;
//
//import java.time.LocalDateTime;
//import java.util.*;
//
//@Component
//public class NoticeOperationHandler implements OperationHandler<Notice, Void> {
//
//    @Resource
//    private  FabosJsonDao fabosJsonDao;
//
//    @Transactional
//    @Override
//    public String exec(List<Notice> data, Void modelObject, String[] param) {
//
//        if (CollectionUtils.isEmpty(data)) {
//            return "重发失败！";
//        }
//        Notice notice = data.get(0);
//        Notice newNotice = new Notice();
//        BeanUtils.copyProperties(notice, newNotice);
//        ArrayList<MetaUser> metaUsers = new ArrayList<>();
//        metaUsers.addAll(notice.getUsers());
//        newNotice.setUsers(metaUsers);
//        newNotice.setCreateTime(LocalDateTime.now());
//        newNotice.setExamineStatus(ExamineStatusEnum.UNAUDITED.getCode());
//        newNotice.setUpdateTime(null);
//        newNotice.setId(null);
//        fabosJsonDao.persistAndFlush(newNotice);
//        return "重发成功";
//    }
//}
