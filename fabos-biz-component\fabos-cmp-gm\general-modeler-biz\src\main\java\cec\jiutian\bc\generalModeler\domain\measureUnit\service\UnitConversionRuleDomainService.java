package cec.jiutian.bc.generalModeler.domain.measureUnit.service;

import cec.jiutian.bc.generalModeler.domain.measureUnit.model.UnitConversionRule;
import cec.jiutian.bc.generalModeler.domain.measureUnit.util.CalculateUtil;
import cec.jiutian.data.jpa.JpaCrud;
import com.googlecode.aviator.Expression;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2024/10/25 10:14
 * @description：
 */
@Service
public class UnitConversionRuleDomainService {
    @Resource
    private JpaCrud jpaCrud;

    public String calculateResult(String ruleId, Object param) {
        String result = "";
        Map<String, Object> data = new HashMap<>();
        data.put("X", param);

        if (StringUtils.isNotEmpty(ruleId)) {
            UnitConversionRule unitConversionRule = jpaCrud.getById(UnitConversionRule.class, ruleId);
            Expression compileExpression = CalculateUtil.compile(unitConversionRule.getFormula());
            result = String.valueOf(CalculateUtil.generalExpressionCalculate(compileExpression, data));
        }
        return result;
    }
}
