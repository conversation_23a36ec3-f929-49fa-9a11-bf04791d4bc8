package cec.jiutian.bc.ecs.inbound.remote.scheduler;

import cec.jiutian.bc.ecs.domain.message.entity.Message;
import cec.jiutian.bc.ecs.domain.message.service.MessageService;
import cec.jiutian.bc.job.provider.IJobProvider;
import cec.jiutian.core.frame.annotation.FabosCustomizedService;
import cec.jiutian.core.frame.constant.YesOrNoStatus;
import cec.jiutian.meta.FabosJob;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

@FabosCustomizedService(value = Message.class)
@Component
public class MessageUpgradeScheduler implements IJobProvider {

    private final MessageService messageService;

    public MessageUpgradeScheduler(MessageService messageService) {
        this.messageService = messageService;
    }

    @FabosJob(comment = "告警升级定时任务")
    @Override
    /**
     * 告警升级定时任务
     * 在定时任务组件中配置后，定时查询未按时处理完成的告警信息，根据当前升级级别进行升级操作
     * @param code
     * @param param
     * @return
     */
    public String exec(String code, String param) {
        // TODO: 1. 查询未按时处理完成的告警信息
        List<Message> messages = messageService.getToUpgradeMessage(LocalDateTime.now());
        messages.forEach(message -> {
            // 设置当前消息的升级级别
            message.setCurrentUpNumber(message.getCurrentUpNumber() + 1);
            // 设置消息为已升级
            message.setUpgraded(YesOrNoStatus.YES.getValue());
            messageService.sendMessage(message);
        });
        return "";
    }
}
