package cec.jiutian.bc.ecs.domain.userMessageRecord.entity;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 */
@Getter
public enum ProcessedStatus {
    YES("Y", "是"), NO("N", "否"), NONEED("", "不需处理");

    private final String value;
    private final String label;

    ProcessedStatus(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static class ChoiceFetch implements ChoiceFetchHandler {
        @Override
        public List<VLModel> fetch(String[] params) {
            return Stream.of(ProcessedStatus.values()).map(SwitchStatus ->
                    new VLModel(SwitchStatus.getValue() + "", SwitchStatus.getLabel())).collect(Collectors.toList());
        }

    }
}
