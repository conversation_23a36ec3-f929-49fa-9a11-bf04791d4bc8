package cec.jiutian.bc.urm.inbound.remote.feign;

import cec.jiutian.api.remoteCallLog.dto.RemoteCallResult;
import cec.jiutian.api.remoteCallLog.enums.RemoteCallCodeEnum;
import cec.jiutian.bc.urm.domain.role.service.RoleService;
import cec.jiutian.bc.urm.service.RoleAuthDistributeService;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("/fabos-cmp-urm" + FabosJsonRestPath.FABOS_REMOTE_API)
public class RoleUserFeign {

    private final RoleService roleService;

    private final RemoteCallResult remoteCallResult;

    public RoleUserFeign(RoleAuthDistributeService roleAuthDistributeService, RoleService roleService, RemoteCallResult remoteCallResult) {
        this.roleService = roleService;
        this.remoteCallResult = remoteCallResult;
    }


    // 根据角色，返回角色下电话
    @PostMapping("/getUserPhoneByRoleIds")
    public RemoteCallResult<Set<String>> getUserByRoles(@RequestBody List<String> roleIds) {
        try {
            Set<String> userPhones = roleService.getUserPhonesByRoles(roleIds);
            return remoteCallResult.success(userPhones);
        } catch (Exception e) {
            return remoteCallResult.error(RemoteCallCodeEnum.FAILED.getCode(), e.getMessage());
        }
    }
}
