package cec.jiutian;

import cec.jiutian.core.frame.annotation.FabosJsonScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;

/**
 * <AUTHOR>
 * @date 2024/5/24
 */
@EntityScan
@SpringBootApplication
@FabosJsonScan
//@EnableDubbo
public class AppBaseServer {
    public static void main(String[] args) {
        try {
            ClassLoader systemClassLoader = ClassLoader.getSystemClassLoader();
            ClassLoader classLoader = AppBaseServer.class.getClassLoader();
            System.out.println(classLoader.equals(systemClassLoader));
            System.out.println("类加载器名称:" + classLoader.getName());
            SpringApplication.run(AppBaseServer.class, args);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }
}

