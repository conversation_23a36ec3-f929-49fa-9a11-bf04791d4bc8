package cec.jiutian.bc.ecs.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：2023/5/31 17:45
 * @description：
 */
@ApiModel("EnumDTO")
@Data
public class EnumDTO {

    /**
     * 字典编码
     */
    @ApiModelProperty("字典编码")
    private String enumCode;

    /**
     * 字典值
     */
    @ApiModelProperty("字典值")
    private String enumValue;

    /**
     * 字典值描述
     */
    @ApiModelProperty("字典值描述")
    private String enumValueDs;
}
