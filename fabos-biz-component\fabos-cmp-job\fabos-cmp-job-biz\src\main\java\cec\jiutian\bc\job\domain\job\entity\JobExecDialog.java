package cec.jiutian.bc.job.domain.job.entity;

import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import lombok.Getter;
import lombok.Setter;

@FabosJson(name = "job execute")
@Getter
@Setter
public class JobExecDialog extends BaseModel{

    @FabosJsonField(
            edit = @Edit(title = "任务参数", type = EditType.CODE)
    )
    private String param;

}
