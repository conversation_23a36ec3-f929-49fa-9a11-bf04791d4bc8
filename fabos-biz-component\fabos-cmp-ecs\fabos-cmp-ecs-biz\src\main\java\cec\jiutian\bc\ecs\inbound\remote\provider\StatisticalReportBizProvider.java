//package cec.jiutian.bc.ecs.inbound.remote.provider;
//
//import cec.jiutian.common.util.JacksonUtil;
//import cec.jiutian.common.util.StringUtils;
//import cec.jiutian.bc.ecs.domain.generalIp.entity.GeneralIp;
//import cec.jiutian.bc.ecs.domain.generalIp.service.GeneralIpService;
//import cec.jiutian.bc.ecs.domain.message.service.MessageService;
//import cec.jiutian.bc.ecs.domain.messagegroup.entity.MessageGroup;
//import cec.jiutian.bc.ecs.domain.messagegroup.service.MessageGroupService;
//import cec.jiutian.bc.ecs.domain.messagerecord.entity.MessageRecord;
//import cec.jiutian.bc.ecs.dto.UserDTO;
//import cec.jiutian.bc.ecs.query.dto.StatisticsQueryDTO;
//import cec.jiutian.bc.infra.util.GenericUtils;
//import cec.jiutian.core.frame.controller.FabosJsonDataController;
//import cec.jiutian.core.frame.module.Page;
//import cec.jiutian.core.frame.module.QueryCondition;
//import cec.jiutian.core.frame.module.R;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.util.CollectionUtils;
//
//import java.io.IOException;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.Objects;
//import java.util.stream.Collectors;
//
//@Service
//@Transactional
//public class StatisticalReportBizProvider {
//
//    private final MessageService messageService;
//
//    private final GeneralIpService generalIpBizService;
//
//    private final MessageGroupService messageGroupService;
//
//    private final FabosJsonDataController fabosJsonDataController;
//
//    public StatisticalReportBizProvider(MessageService messageService,
//                                        GeneralIpService generalIpBizService,
//                                        MessageGroupService messageGroupService,
//                                        FabosJsonDataController fabosJsonDataController) {
//        this.messageService = messageService;
//        this.generalIpBizService = generalIpBizService;
//        this.messageGroupService = messageGroupService;
//        this.fabosJsonDataController = fabosJsonDataController;
//    }
//    /**
//     * @Description 统计人员接收效率
//     * <AUTHOR>
//     * @Date 2023/6/7 11:24
//     */
//    public Map<String, Object> countPersonReceiveRate(StatisticsQueryDTO queryDTO) {
//        QueryCondition queryCondition = new QueryCondition();
//        queryCondition.setPage(queryDTO.getPageNum());
//        queryCondition.setPerPage(queryDTO.getPageSize());
//        R<Page> data = fabosJsonDataController.getTableData("MetaUser", queryCondition);
//        Page page = data.getData();
//        List<Object> list = page.getList();
//        if (list == null || list.isEmpty()) {
//            return null;
//        }
//        List<UserDTO> userList = list.stream()
//                .map(o -> convertToUserDTO(o))
//                .filter(Objects::nonNull)
//                .collect(Collectors.toList());
//        List<MessageRecord> messageRecordList = messageService.queryByRangeCreateTime(queryDTO.getStartTime(), queryDTO.getEndTime());
//
//        userList.forEach(user -> {
//            //接收总数
//            List<MessageRecord> receiveTotal = messageRecordList.stream().filter(m -> {
//                return m.getDispatchUser().equals(user.getAccount());
//            }).collect(Collectors.toList());
//
//            //已读总数
//            List<MessageRecord> readTotal = messageRecordList.stream().filter(m -> {
//                return m.getDispatchUser().equals(user.getAccount()) && m.getStatus().equals("Y");
//            }).collect(Collectors.toList());
//
//            // 计算接收平均耗时
//            int min = 0;
//            int sec = 0;
//            String averageTime = "-";
//            if (!CollectionUtils.isEmpty(readTotal)) {
//                for (MessageRecord event : readTotal) {
//                    Map<String, Integer> result = GenericUtils.getMinAndSecondByNum(event.getCreateTime(), event.getUpdateTime());
//                    min = min + result.get("min");
//                    sec = sec + result.get("sec");
//                }
//                min = Math.round(min / readTotal.size());
//                sec = Math.round(sec / readTotal.size());
//                if (sec < 10) {
//                    averageTime = min + "分0" + sec + "秒";
//                } else averageTime = min + "分" + sec + "秒";
//            }
//
//            String rate = GenericUtils.getPercent((int) readTotal.size(), (int) receiveTotal.size());
//            user.setMessageCount((int) receiveTotal.size());
//            user.setReceiveRate(rate);
//            user.setAverageTime(averageTime);
//        });
//        HashMap<String, Object> res = new HashMap<>();
//        res.put("page", page.getPage());
//        res.put("total", page.getTotal());
//        res.put("list", userList);
//        res.put("perPage", page.getPerPage());
//        res.put("totalPage", page.getTotalPage());
//        return res;
//    }
//
//
//    private UserDTO convertToUserDTO(Object o) {
//        if (o instanceof HashMap) {
//            HashMap user = (HashMap) o;
//            UserDTO userDTO = new UserDTO();
//            userDTO.setAccount((String) user.get("account"));
//            userDTO.setName((String) user.get("name"));
//            userDTO.setEmail((String) user.get("emailAddress"));
//            userDTO.setPhoneNumber((String) user.get("phoneNumber"));
//            return userDTO;
//        }
//        return null;
//    }
//
//    /**
//     * 根据发送方统计发送消息数量
//     *
//     * @param queryDTO
//     * @return
//     */
//    public Object countMessageBySystem(StatisticsQueryDTO queryDTO) {
//        List<MessageRecord> messageRecordList = messageService.queryByRangeCreateTime(queryDTO.getStartTime(), queryDTO.getEndTime());
//        HashMap<String, Object> res = generalIpBizService.getGeneralIpList(queryDTO.getPageNum(), queryDTO.getPageSize());
//        List<GeneralIp> generalIps = (List<GeneralIp>) res.get("list");
//        List<HashMap<String, Object>> result = generalIps.stream().map(g -> {
//            long messageCount = messageRecordList.stream().filter(m -> (StringUtils.isNotBlank(m.getOriginalSystem())
//                    &&m.getOriginalSystem().equals(g.getSysName()))).count();
//            HashMap<String, Object> gpo = new HashMap<>();
//            gpo.put("sysName", g.getSysName());
//            gpo.put("sendCount", messageCount);
//            return gpo;
//        }).collect(Collectors.toList());
//        res.put("list", result);
//        return res;
//    }
//
//    /**
//     * 统计消息组接收率
//     *
//     * @param queryDTO
//     * @return
//     */
//    public Object countGroupReceiveRate(StatisticsQueryDTO queryDTO) {
//
//        HashMap<String, Object> res = messageGroupService.getMessageGroupList(queryDTO.getPageNum(), queryDTO.getPageSize());
//        List<MessageGroup> groupList = (List<MessageGroup>) res.get("list");
//        List<MessageRecord> messageList = messageService.queryByRangeCreateTime(queryDTO.getStartTime(), queryDTO.getEndTime());
//
//        List<Map<String, Object>> list = groupList.stream().map(g -> {
//            long messageCount = messageList.stream().filter(m -> StringUtils.isNotEmpty(m.getMessageGroupId()) && m.getMessageGroupId().equals(g.getId())).count();
//            long receiveCount = messageList.stream().filter(m -> StringUtils.isNotEmpty(m.getMessageGroupId())
//                    && m.getMessageGroupId().equals(g.getId()) && (m.getStatus().equals("Y"))).count();
//
//            List<MessageRecord> receivedCountEvent = messageList.stream().filter(m -> StringUtils.isNotEmpty(m.getMessageGroupId())
//                    && m.getMessageGroupId().equals(g.getId()) && m.getStatus().equals("Y")).collect(Collectors.toList());
//            // 计算接收平均耗时
//            int min = 0;
//            int sec = 0;
//            String averageTime = "";
//            if (!CollectionUtils.isEmpty(receivedCountEvent)) {
//                for (MessageRecord event : receivedCountEvent) {
//                    Map<String, Integer> result = GenericUtils.getMinAndSecondByNum(event.getCreateTime(), event.getUpdateTime());
//                    min = min + result.get("min");
//                    sec = sec + result.get("sec");
//                }
//                min = Math.round(min / receivedCountEvent.size());
//                sec = Math.round(sec / receivedCountEvent.size());
//                if (sec < 10) {
//                    averageTime = min + "分0" + sec + "秒";
//                } else averageTime = min + "分" + sec + "秒";
//            }
//
//            String rate = GenericUtils.getPercent((int) receiveCount, (int) messageCount);
//
//            try {
//                Map<String, Object> map = JacksonUtil.fromJsonToMap(JacksonUtil.toJson(g));
//                map.put("messageCount", messageCount);
//                map.put("receiveRate", rate);
//                map.put("averageTime", averageTime);
//                return map;
//            } catch (IOException e) {
//                throw new RuntimeException(e);
//            }
//        }).toList();
//        res.put("list", list);
//        return res;
//    }
//
//    public Object messageSendStatistical(StatisticsQueryDTO queryDTO) {
//        return messageService.messageSendCount(queryDTO.getStartTime(), queryDTO.getEndTime());
//    }
//
//}
