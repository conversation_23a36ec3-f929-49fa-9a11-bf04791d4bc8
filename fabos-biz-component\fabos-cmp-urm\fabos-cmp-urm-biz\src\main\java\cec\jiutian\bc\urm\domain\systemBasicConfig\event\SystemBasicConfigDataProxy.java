package cec.jiutian.bc.urm.domain.systemBasicConfig.event;

import cec.jiutian.bc.urm.domain.systemBasicConfig.entity.SystemBasicConfig;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.fc.log.domain.permissionOperationLog.manager.PermissionLogManager;
import cec.jiutian.fc.log.enums.OperationTargetEnum;
import cec.jiutian.fc.log.enums.OperationTypeEnum;
import cec.jiutian.view.fun.DataProxy;
import cec.jiutian.view.query.Condition;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.TypedQuery;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@Component
public class SystemBasicConfigDataProxy implements DataProxy<SystemBasicConfig> {

    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private PermissionLogManager permissionLogManager;

    // 由配置文件中获取application id
    @Value("${app.id}")
    private String appId;

    @Override
    public void beforeAdd(SystemBasicConfig systemBasicConfig) {
        try {
            EntityManager entityManager = fabosJsonDao.getEntityManager();
            // 创建查询
            TypedQuery<SystemBasicConfig> query = entityManager.createQuery("SELECT s FROM SystemBasicConfig s where (systemApplicationId = ?1 or systemApplicationId is null)", SystemBasicConfig.class);
            query.setParameter(1, appId);

            // 设置分页参数
            query.setFirstResult(0);
            query.setMaxResults(1);
//            SystemBasicConfig result = query.getSingleResult();
            List<SystemBasicConfig> results = query.getResultList();
            SystemBasicConfig result = results.isEmpty() ? null : results.get(0);
            if (result != null) {
                throw new FabosJsonApiErrorTip("已存在系统配置，不能再继续添加");
            }
            systemBasicConfig.setSystemApplicationId(appId);
        } catch (NoResultException e) {
            return;
        }
    }

    @Override
    public void beforeDelete(SystemBasicConfig systemBasicConfig) {
        throw new FabosJsonApiErrorTip("系统配置不得删除！");
    }
    @Override
    public void afterDelete(SystemBasicConfig systemBasicConfig) {
        permissionLogManager.addOrDelete(systemBasicConfig, OperationTargetEnum.Enum.CONFIG, OperationTypeEnum.Enum.DELETE);
    }

    @Override
    public String beforeFetch(List<Condition> conditions) {
        return DataProxy.super.beforeFetch(conditions);
    }

    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        list.removeIf(map -> {
            Object systemAppId = map.get("systemApplicationId");
            return systemAppId != null && !systemAppId.equals(appId);
        });
        DataProxy.super.afterFetch(list);
    }

    @Override
    public void afterAdd(SystemBasicConfig systemBasicConfig) {
        permissionLogManager.addOrDelete(systemBasicConfig, OperationTargetEnum.Enum.CONFIG, OperationTypeEnum.Enum.ADD);
    }

    @Override
    public void beforeUpdate(SystemBasicConfig systemBasicConfig) {
        EntityManager entityManager = fabosJsonDao.getEntityManager();
        entityManager.clear();
        SystemBasicConfig systemSecurityConfigDb = entityManager.find(SystemBasicConfig.class, systemBasicConfig.getId());
        permissionLogManager.update(systemSecurityConfigDb,systemBasicConfig, OperationTargetEnum.Enum.CONFIG, OperationTypeEnum.Enum.UPDATE);

    }
}
