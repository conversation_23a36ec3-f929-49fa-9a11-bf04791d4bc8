package cec.jiutian.bc.job.domain.job.service;

import cec.jiutian.core.frame.config.GsonFactory;
import cec.jiutian.bc.infra.notify.DataAction;
import cec.jiutian.bc.infra.notify.NotifyData;
import cec.jiutian.bc.job.domain.job.entity.JobInfo;
import cec.jiutian.core.prop.FabosJsonProp;
import com.google.gson.reflect.TypeToken;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.quartz.SchedulerException;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.stereotype.Service;

import java.text.ParseException;

@Service
public class JobMessageListener {

    public static final String JOB_TOPIC = "job:data-proxy:notify";

    @Resource
    private RedisConnectionFactory redisConnectionFactory;

    @Resource
    private FabosJsonProp fabosJsonProp;

    @Resource
    private JobService jobService;

    @PostConstruct
    public void post() {
        if (fabosJsonProp.isRedisSession()) {
            redisConnectionFactory.getConnection().subscribe((message, pattern) -> {
                NotifyData<JobInfo> notifyData = GsonFactory.getGson().fromJson(message.toString(), new TypeToken<NotifyData<JobInfo>>() {
                }.getType());
                try {
                    if (DataAction.ADD.equals(notifyData.getAction())) {
                        jobService.addJob(notifyData.getData());
                    } else if (DataAction.UPDATE.equals(notifyData.getAction())) {
                        jobService.modifyJob(notifyData.getData());
                    } else if (DataAction.DELETE.equals(notifyData.getAction())) {
                        jobService.deleteJob(notifyData.getData());
                    }
                } catch (SchedulerException | ParseException e) {
                    throw new RuntimeException(e);
                }
            }, JOB_TOPIC.getBytes());
        }
    }

}
