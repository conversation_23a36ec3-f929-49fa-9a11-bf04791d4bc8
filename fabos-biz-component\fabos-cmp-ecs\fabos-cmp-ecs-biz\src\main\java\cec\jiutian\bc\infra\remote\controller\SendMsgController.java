package cec.jiutian.bc.infra.remote.controller;

import cec.jiutian.bc.ecs.domain.message.service.MessageService;
import cec.jiutian.bc.ecs.dto.SendMsgGroupDTO;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping()
public class SendMsgController {

    @Resource
    private MessageService messageService;

    @PostMapping("/innerSend")
    public void sendMsg(@RequestBody SendMsgGroupDTO sendMsgGroupDTO) {
        messageService.sendMsgToGroup(sendMsgGroupDTO);
    }

}
