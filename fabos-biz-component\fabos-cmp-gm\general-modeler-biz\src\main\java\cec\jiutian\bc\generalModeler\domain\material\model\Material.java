package cec.jiutian.bc.generalModeler.domain.material.model;

import cec.jiutian.bc.generalModeler.domain.material.proxy.MaterialDataProxy;
import cec.jiutian.bc.generalModeler.domain.measureUnit.model.MeasureUnit;
import cec.jiutian.bc.generalModeler.domain.supplier.model.Supplier;
import cec.jiutian.bc.generalModeler.enumeration.MaterialEnum;
import cec.jiutian.bc.generalModeler.handler.MaterialAddHandler;
import cec.jiutian.bc.generalModeler.handler.MaterialCategorySelectHandler;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.InputGroup;
import cec.jiutian.view.ReferenceTreeType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.config.Comment;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.field.edit.TabTableReferType;
import cec.jiutian.view.type.LinkTree;
import cec.jiutian.view.type.RowBaseOperation;
import jakarta.persistence.ConstraintMode;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "物料主数据",
        orderBy = "Material.createTime desc",
        linkTree = @LinkTree(field = "materialCategory"),
        dataProxy = MaterialDataProxy.class,
        rowBaseOperation = {
                @RowBaseOperation(code = "add",
                        needInitFormValue = true,
                        operationHandler = MaterialAddHandler.class
                )
        }
)
@Table(name = "material",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"materialCode"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "treeTable")
public class Material extends MetaModel {
    @FabosJsonField(
            views = @View(title = "物料编码"),
            edit = @Edit(title = "物料编码", readonly = @Readonly(add = false, edit = true), notNull = true, search = @Search(vague = true))
    )
    private String materialCode;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "分类代码", column = "code"),
            edit = @Edit(title = "分类代码", readonly = @Readonly(add = false, edit = true), notNull = false,
                    type = EditType.REFERENCE_TREE,
                    referenceTreeType = @ReferenceTreeType(pid = "parentCategory.id"),
                    search = @Search())
    )
    private MaterialCategory materialCategory;

    @Transient
    @FabosJsonField(
            edit = @Edit(title = "物料分类选择后处理", show = false),
            dynamicField = @DynamicField(dependFiled = @DependFiled(
                    changeBy = "materialCategory", dynamicHandler = MaterialCategorySelectHandler.class))
    )
    @Comment("关联物料分类变化，在分类变化后，设置相关字段的显隐标记")
    private String materialCategoryHandle;

    @FabosJsonField(
            views = @View(title = "物料名称"),
            edit = @Edit(title = "物料名称", notNull = true)
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "物料简称"),
            edit = @Edit(title = "物料简称", notNull = false)
    )
    private String shortName;

    @FabosJsonField(
            views = @View(title = "物料别名"),
            edit = @Edit(title = "物料别名", notNull = false)
    )
    @Comment("物料英文名称或别名")
    private String aliasName;

    @FabosJsonField(
            views = @View(title = "牌号"),
            edit = @Edit(title = "牌号", notNull = false)
    )
    private String brandCode;

    @FabosJsonField(
            views = @View(title = "规格"),
            edit = @Edit(title = "规格", notNull = false, search = @Search(vague = true))
    )
    private String materialSpecification;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "单位", column = "unitChnName"),
            edit = @Edit(title = "单位",
                    type = EditType.REFERENCE_TABLE,
                    notNull = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "unitChnName",
                            type = ReferenceTableType.SelectShowTypeMTO.LIST, groupField = "unitType")
            )
    )
    private MeasureUnit accountUnit;

    @FabosJsonField(
            views = @View(title = "长度"),
            edit = @Edit(title = "长度", notNull = false,
                    inputGroup = @InputGroup(postfix = "米"), numberType = @NumberType(min = 0))
    )
    private String materialLength;

//@FabosJsonField(
//        views = @View(title = "最大库存量"),
//        edit = @Edit(title = "最大库存量", notNull = false)
//)
//private Double maxInventoryQuantity;

    @FabosJsonField(
            views = @View(title = "最大复验次数"),
            edit = @Edit(title = "最大复验次数", notNull = false,
                    inputGroup = @InputGroup(postfix = "次"), numberType = @NumberType(min = 0))
    )
    private Integer maxReinspectTimes;

    @FabosJsonField(
            views = @View(title = "保质天数"),
            edit = @Edit(title = "保质天数", notNull = false,
                    inputGroup = @InputGroup(postfix = "天"), numberType = @NumberType(min = 0))
    )
    private Integer guaranteePeriodDays;

    @ManyToOne(fetch = FetchType.EAGER)
    @FabosJsonField(
            views = @View(title = "保管员", column = "name"),
            edit = @Edit(title = "保管员",
                    type = EditType.REFERENCE_TABLE,
                    notNull = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name", type = ReferenceTableType.SelectShowTypeMTO.DROPDOWN_TABLE),
                    allowAddMultipleRows = false,
                    search = @Search()
            )
    )
    @JoinColumn(foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    private MetaUser storeKeeper;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "e_material_usefor_product", // 中间表表名，如下为中间表的定义
            joinColumns = @JoinColumn(name = "target_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "source_id", referencedColumnName = "id"))
    @FabosJsonField(
            views = @View(title = "用途", column = "productCodeD", type = ViewType.TABLE_VIEW, export = false),
            edit = @Edit(title = "用途",
                    type = EditType.TAB_TABLE_REFER,
                    tabTableReferType = @TabTableReferType(type = TabTableReferType.SelectShowTypeMTM.LIST, label = "name"),
                    notNull = false, search = @Search()
            )
    )
    private List<FinalProductDTM> useForProductlist;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "供应商", column = "supplierName", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "供应商",
                    type = EditType.REFERENCE_TABLE,
                    notNull = false,
                    referenceTableType = @ReferenceTableType(id = "id", label = "supplierName"),
                    search = @Search()
            )
    )
    private Supplier materialSupplier;

    @FabosJsonField(
            views = @View(title = "附件"),
            edit = @Edit(title = "附件", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType)
    )
    private String materialDocument;

    @FabosJsonField(
            views = @View(title = "当前状态"),
            edit = @Edit(title = "当前状态", notNull = true, type = EditType.CHOICE,
                    search = @Search(),
                    defaultVal = "ACTIVE",
                    choiceType = @ChoiceType(fetchHandler = MaterialEnum.class)
            )
    )
    private String currentStatus;

    @Transient
    @FabosJsonField(edit = @Edit(title = "技术标准号/技术要求", show = false))
    private String wl09Show = "N";

    @FabosJsonField(
            views = @View(title = "技术标准号/技术要求"),
            edit = @Edit(title = "技术标准号/技术要求", notNull = false, dependFieldDisplay = @DependFieldDisplay(showOrHide = "wl09Show == 'Y'"))
    )
    @Comment("技术要求")
    private String wl09;

    @Transient
    @FabosJsonField(edit = @Edit(title = "技术标准号/技术要求", show = false))
    private String wl10Show = "N";
    @FabosJsonField(
            views = @View(title = "规格标准号"),
            edit = @Edit(title = "规格标准号", notNull = false, dependFieldDisplay = @DependFieldDisplay(showOrHide = "wl10Show == 'Y'"))
    )
    private String wl10;

    @Transient
    @FabosJsonField(edit = @Edit(title = "技术标准号/技术要求", show = false))
    private String wl11Show = "N";
    @FabosJsonField(
            views = @View(title = "加工工艺"),
            edit = @Edit(title = "加工工艺", notNull = false, dependFieldDisplay = @DependFieldDisplay(showOrHide = "wl11Show == 'Y'"))
    )
    private String wl11;

    @Transient
    @FabosJsonField(edit = @Edit(title = "技术标准号/技术要求", show = false))
    private String wl12Show = "N";
    @FabosJsonField(
            views = @View(title = "热处理工艺"),
            edit = @Edit(title = "热处理工艺", notNull = false, dependFieldDisplay = @DependFieldDisplay(showOrHide = "wl12Show == 'Y'"))
    )
    private String wl12;

    @Transient
    @FabosJsonField(edit = @Edit(title = "技术标准号/技术要求", show = false))
    private String wl13Show = "N";
    @FabosJsonField(
            views = @View(title = "力学性能"),
            edit = @Edit(title = "力学性能", notNull = false, dependFieldDisplay = @DependFieldDisplay(showOrHide = "wl13Show == 'Y'"))
    )
    private String wl13;

    @Transient
    @FabosJsonField(edit = @Edit(title = "技术标准号/技术要求", show = false))
    private String wl14Show = "N";
    @FabosJsonField(
            views = @View(title = "特殊要求"),
            edit = @Edit(title = "特殊要求", notNull = false, dependFieldDisplay = @DependFieldDisplay(showOrHide = "wl14Show == 'Y'"))
    )
    private String wl14;

    @Transient
    @FabosJsonField(edit = @Edit(title = "技术标准号/技术要求", show = false))
    private String wl16Show = "N";
    @FabosJsonField(
            views = @View(title = "型号"),
            edit = @Edit(title = "型号", notNull = false, dependFieldDisplay = @DependFieldDisplay(showOrHide = "wl16Show == 'Y'"))
    )
    private String wl16;

    @Transient
    @FabosJsonField(edit = @Edit(title = "技术标准号/技术要求", show = false))
    private String wl17Show = "N";
    @FabosJsonField(
            views = @View(title = "产品标准号/国外品牌"),
            edit = @Edit(title = "产品标准号/国外品牌", notNull = false, dependFieldDisplay = @DependFieldDisplay(showOrHide = "wl17Show == 'Y'"))
    )
    private String wl17;

    @Transient
    @FabosJsonField(edit = @Edit(title = "技术标准号/技术要求", show = false))
    private String wl19Show = "N";
    @FabosJsonField(
            views = @View(title = "颜色"),
            edit = @Edit(title = "颜色", notNull = false, dependFieldDisplay = @DependFieldDisplay(showOrHide = "wl19Show == 'Y'"))
    )
    private String wl19;

    @Transient
    @FabosJsonField(edit = @Edit(title = "技术标准号/技术要求", show = false))
    private String wl20Show = "N";
    @FabosJsonField(
            views = @View(title = "材质"),
            edit = @Edit(title = "材质", notNull = false, dependFieldDisplay = @DependFieldDisplay(showOrHide = "wl20Show == 'Y'"))
    )
    private String wl20;

    @Transient
    @FabosJsonField(edit = @Edit(title = "技术标准号/技术要求", show = false))
    private String wl22Show = "N";
    @FabosJsonField(
            views = @View(title = "品牌"),
            edit = @Edit(title = "品牌", notNull = false, dependFieldDisplay = @DependFieldDisplay(showOrHide = "wl22Show == 'Y'"))
    )
    private String wl22;

    @Transient
    @FabosJsonField(edit = @Edit(title = "技术标准号/技术要求", show = false))
    private String wl23Show = "N";
    @FabosJsonField(
            views = @View(title = "系列"),
            edit = @Edit(title = "系列", notNull = false, dependFieldDisplay = @DependFieldDisplay(showOrHide = "wl23Show == 'Y'"))
    )
    private String wl23;

    @Transient
    @FabosJsonField(edit = @Edit(title = "技术标准号/技术要求", show = false))
    private String wl24Show = "N";
    @FabosJsonField(
            views = @View(title = "型号(货号/品种代号)"),
            edit = @Edit(title = "型号(货号/品种代号)", notNull = false, dependFieldDisplay = @DependFieldDisplay(showOrHide = "wl24Show == 'Y'"))
    )
    private String wl24;

    @Transient
    @FabosJsonField(edit = @Edit(title = "技术标准号/技术要求", show = false))
    private String wl25Show = "N";
    @FabosJsonField(
            views = @View(title = "强度等级"),
            edit = @Edit(title = "强度等级", notNull = false, dependFieldDisplay = @DependFieldDisplay(showOrHide = "wl25Show == 'Y'"))
    )
    private String wl25;

    @Transient
    @FabosJsonField(edit = @Edit(title = "技术标准号/技术要求", show = false))
    private String wl26Show = "N";
    @FabosJsonField(
            views = @View(title = "质地"),
            edit = @Edit(title = "质地", notNull = false, dependFieldDisplay = @DependFieldDisplay(showOrHide = "wl26Show == 'Y'"))
    )
    private String wl26;

    @Transient
    @FabosJsonField(edit = @Edit(title = "技术标准号/技术要求", show = false))
    private String wl27Show = "N";
    @FabosJsonField(
            views = @View(title = "制造商"),
            edit = @Edit(title = "制造商", notNull = false, dependFieldDisplay = @DependFieldDisplay(showOrHide = "wl27Show == 'Y'"))
    )
    private String wl27;

    @Transient
    @FabosJsonField(edit = @Edit(title = "技术标准号/技术要求", show = false))
    private String wl28Show = "N";
    @FabosJsonField(
            views = @View(title = "性能等级"),
            edit = @Edit(title = "性能等级", notNull = false, dependFieldDisplay = @DependFieldDisplay(showOrHide = "wl28Show == 'Y'"))
    )
    private String wl28;

    @Transient
    @FabosJsonField(edit = @Edit(title = "技术标准号/技术要求", show = false))
    private String wl29Show = "N";
    @FabosJsonField(
            views = @View(title = "表面处理"),
            edit = @Edit(title = "表面处理", notNull = false, dependFieldDisplay = @DependFieldDisplay(showOrHide = "wl29Show == 'Y'"))
    )
    private String wl29;

    @Transient
    @FabosJsonField(edit = @Edit(title = "技术标准号/技术要求", show = false))
    private String wl30Show = "N";
    @FabosJsonField(
            views = @View(title = "产品等级"),
            edit = @Edit(title = "产品等级", notNull = false, dependFieldDisplay = @DependFieldDisplay(showOrHide = "wl30Show == 'Y'"))
    )
    private String wl30;

    @Transient
    @FabosJsonField(edit = @Edit(title = "技术标准号/技术要求", show = false))
    private String wl31Show = "N";
    @FabosJsonField(
            views = @View(title = "前置代号"),
            edit = @Edit(title = "前置代号", notNull = false, dependFieldDisplay = @DependFieldDisplay(showOrHide = "wl31Show == 'Y'"))
    )
    private String wl31;

    @Transient
    @FabosJsonField(edit = @Edit(title = "技术标准号/技术要求", show = false))
    private String wl32Show = "N";
    @FabosJsonField(
            views = @View(title = "基本代号"),
            edit = @Edit(title = "基本代号", notNull = false, dependFieldDisplay = @DependFieldDisplay(showOrHide = "wl32Show == 'Y'"))
    )
    private String wl32;

    @Transient
    @FabosJsonField(edit = @Edit(title = "技术标准号/技术要求", show = false))
    private String wl33Show = "N";
    @FabosJsonField(
            views = @View(title = "后置代号"),
            edit = @Edit(title = "后置代号", notNull = false, dependFieldDisplay = @DependFieldDisplay(showOrHide = "wl33Show == 'Y'"))
    )
    private String wl33;

    @Transient
    @FabosJsonField(edit = @Edit(title = "技术标准号/技术要求", show = false))
    private String wl35Show = "N";
    @FabosJsonField(
            views = @View(title = "封装形式"),
            edit = @Edit(title = "封装形式", notNull = false, dependFieldDisplay = @DependFieldDisplay(showOrHide = "wl35Show == 'Y'"))
    )
    private String wl35;

    @Transient
    @FabosJsonField(edit = @Edit(title = "技术标准号/技术要求", show = false))
    private String wl38Show = "N";
    @FabosJsonField(
            views = @View(title = "导线"),
            edit = @Edit(title = "导线", notNull = false, dependFieldDisplay = @DependFieldDisplay(showOrHide = "wl38Show == 'Y'"))
    )
    private String wl38;

    @Transient
    @FabosJsonField(edit = @Edit(title = "技术标准号/技术要求", show = false))
    private String wl39Show = "N";
    @FabosJsonField(
            views = @View(title = "精度"),
            edit = @Edit(title = "精度", notNull = false, numberType = @NumberType(min = 0), dependFieldDisplay = @DependFieldDisplay(showOrHide = "wl39Show == 'Y'"))
    )
    private String wl39;

    @Transient
    @FabosJsonField(edit = @Edit(title = "技术标准号/技术要求", show = false))
    private String wl46Show = "N";
    @FabosJsonField(
            views = @View(title = "证书编号"),
            edit = @Edit(title = "证书编号", notNull = false, dependFieldDisplay = @DependFieldDisplay(showOrHide = "wl46Show == 'Y'"))
    )
    private String wl46;

    @Transient
    @FabosJsonField(edit = @Edit(title = "技术标准号/技术要求", show = false))
    private String wl47Show = "N";
    @FabosJsonField(
            views = @View(title = "出厂编号"),
            edit = @Edit(title = "出厂编号", notNull = false, dependFieldDisplay = @DependFieldDisplay(showOrHide = "wl47Show == 'Y'"))
    )
    private String wl47;


}
