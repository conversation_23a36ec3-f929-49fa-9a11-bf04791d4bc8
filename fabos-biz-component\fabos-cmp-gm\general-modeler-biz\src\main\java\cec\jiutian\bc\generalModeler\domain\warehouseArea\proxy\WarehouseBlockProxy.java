package cec.jiutian.bc.generalModeler.domain.warehouseArea.proxy;


import cec.jiutian.bc.generalModeler.domain.warehouseArea.model.WarehouseBlock;
import cec.jiutian.bc.generalModeler.enumeration.WarehouseStateEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Component
public class WarehouseBlockProxy implements DataProxy<WarehouseBlock> {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Override
    public void afterAdd(WarehouseBlock warehouseBlock) {

    }

    @Override
    public void beforeAdd(WarehouseBlock warehouseBlock) {
        //唯一性验证友好提示
        CriteriaBuilder criteriaBuilder = fabosJsonDao.getEntityManager().getCriteriaBuilder();
        CriteriaQuery<WarehouseBlock> criteriaQuery = criteriaBuilder.createQuery(WarehouseBlock.class);
        Root<WarehouseBlock> warehouseBlockRoot = criteriaQuery.from(WarehouseBlock.class);
        criteriaQuery.where(
                criteriaBuilder.or(
                        criteriaBuilder.equal(warehouseBlockRoot.get("code"), warehouseBlock.getCode()),
                        criteriaBuilder.equal(warehouseBlockRoot.get("name"), warehouseBlock.getName())
                )
        );
        List<WarehouseBlock> resultList = fabosJsonDao.getEntityManager().createQuery(criteriaQuery).getResultList();
        if (!resultList.isEmpty()) {
            throw new FabosJsonApiErrorTip("编码或者名称已存在");
        }
        warehouseBlock.setLockState(WarehouseStateEnum.Enum.Normal.name());
        handlerDetail(warehouseBlock);
    }

    private void handlerDetail(WarehouseBlock entity) {
        if (!CollectionUtils.isEmpty(entity.getWarehouseBlockDetails())) {
            AtomicInteger index = new AtomicInteger(1);
            entity.getWarehouseBlockDetails().forEach(d -> {
                d.setWarehouseBlock(entity);
                d.setRequestDetailNumber(entity.getCode() + "-" + String.format("%03d", index.get()));
                d.setCreateBy(entity.getCreateBy());
                d.setCreateTime(LocalDateTime.now());
                d.setUpdateBy(entity.getUpdateBy());
                d.setUpdateTime(LocalDateTime.now());
                index.getAndIncrement();
            });
        }
    }

    @Override
    public void beforeDelete(WarehouseBlock warehouseBlock) {
        if (warehouseBlock.getLockState().equals(WarehouseStateEnum.Enum.Locked.name())) {
            throw new FabosJsonApiErrorTip("已锁定库区不可删除");
        }
        // todo 校验库存是否存在
    }

}
