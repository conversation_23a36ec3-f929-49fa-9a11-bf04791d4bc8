package cec.jiutian.bc.job.domain.mail.entity;

import cec.jiutian.bc.job.domain.mail.event.JobMailDataProxy;
import cec.jiutian.core.frame.constant.RegexConst;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.BoolType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@FabosJsonI18n
@FabosJson(
        name = "发送邮件",
        dataProxy = JobMailDataProxy.class,
        power = @Power(edit = false),
        orderBy = "id desc"
)
@Entity
@Getter
@Setter
@Table(name = "fd_job_mail")
public class JobMail extends MetaModel {

    @FabosJsonField(
            views = @View(title = "接收人"),
            edit = @Edit(title = "接收人", notNull = true, search = @Search(vague = true),
                    inputType = @InputType(fullSpan = true, regex = RegexConst.EMAIL_REGEX))
    )
    private String recipient;

    @FabosJsonField(
            views = @View(title = "抄送人"),
            edit = @Edit(title = "抄送人", type = EditType.TAGS)
    )
    private String cc;

    @FabosJsonField(
            views = @View(title = "主题"),
            edit = @Edit(title = "主题", notNull = true, search = @Search(vague = true), inputType = @InputType(fullSpan = true))
    )
    private String subject;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", boolType = @BoolType(trueText = "成功", falseText = "失败"), show = false)
    )
    private Boolean status;

    @Column(columnDefinition = "TEXT")
    @FabosJsonField(
            views = @View(title = "内容"),
            edit = @Edit(title = "内容", notNull = true, type = EditType.HTML_EDITOR)
    )
    private String content;

    @Column(length = 5000)
    private String errorInfo;

}
