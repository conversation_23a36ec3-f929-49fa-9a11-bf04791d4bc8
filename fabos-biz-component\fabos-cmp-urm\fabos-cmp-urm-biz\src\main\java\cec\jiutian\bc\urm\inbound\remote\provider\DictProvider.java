package cec.jiutian.bc.urm.inbound.remote.provider;

import cec.jiutian.bc.urm.domain.dictionary.service.DictionaryService;
import cec.jiutian.bc.urm.dto.MetaDictItem;
import cec.jiutian.bc.urm.provider.IDictProvider;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 *
 */

@Component
public class DictProvider implements IDictProvider {

    private final DictionaryService dictionaryService;

    public DictProvider(DictionaryService dictionaryService) {
        this.dictionaryService = dictionaryService;
    }


    @Override
    public List<MetaDictItem> getDictItems(String code) {
        return dictionaryService.getDictItems(code);
    }
}
