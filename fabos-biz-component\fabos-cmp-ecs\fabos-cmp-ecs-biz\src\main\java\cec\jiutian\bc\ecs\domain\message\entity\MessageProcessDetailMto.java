package cec.jiutian.bc.ecs.domain.message.entity;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description:
 */
@Entity
@Table(name = "ecs_message")
@Getter
@Setter
@FabosJson(name = "告警信息",
        orderBy = "Message.createTime desc",
        power = @Power(add = true, edit = true, delete = true, export = true, importable = false, viewDetails = true)

)
@FabosJsonI18n
public class MessageProcessDetailMto extends MetaModel {

    @FabosJsonField(
            views = @View(title = "处理说明"),
            edit = @Edit(title = "处理说明", type = EditType.TEXTAREA,
                    formColumns = 3, inputType = @InputType(length = 400)
            )
    )
    private String processDetail;


}
