package cec.jiutian.bc.urm.domain.openApi.entity;

import cec.jiutian.bc.urm.domain.openApi.event.FabosJsonOpenApiDataProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.BoolType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static cec.jiutian.bc.urm.domain.openApi.event.FabosJsonOpenApiDataProxy.DISPLAY_SECRET;
import static cec.jiutian.bc.urm.domain.openApi.event.FabosJsonOpenApiDataProxy.DISPLAY_TOKEN;
import static cec.jiutian.bc.urm.domain.openApi.event.FabosJsonOpenApiDataProxy.REQUEST_NEW_SECRET;
import static cec.jiutian.bc.urm.domain.openApi.event.FabosJsonOpenApiDataProxy.REQUEST_NEW_TOKEN;

/**
 * <AUTHOR>
 * @time 2025-01-13 09:50
 */


@Entity
@Table(name = "fd_open_api")
@FabosJson(
        name = "开放接口管理",
        dataProxy = FabosJsonOpenApiDataProxy.class,
        formColumnElements = 2,
        power = @Power(export = false, print = false),
        rowOperation = {
                @RowOperation(title = "更新密钥", code = "requestNewAppSecret", operationHandler = FabosJsonOpenApiDataProxy.class, operationParam = REQUEST_NEW_SECRET, mode = RowOperation.Mode.SINGLE, callHint = "更新密钥后，旧密钥和旧Token将立即失效，确定要重新生成密钥吗？"),
                @RowOperation(title = "查看密钥", code = "displayNewAppSecret", operationHandler = FabosJsonOpenApiDataProxy.class, operationParam = DISPLAY_SECRET, mode = RowOperation.Mode.HEADER, callHint = ""),
                @RowOperation(title = "更新Token", code = "requestNewToken", operationHandler = FabosJsonOpenApiDataProxy.class, operationParam = REQUEST_NEW_TOKEN, mode = RowOperation.Mode.SINGLE, callHint = "更新Token后，旧token将立即失效，确定要重新生成Token吗？"),
                @RowOperation(title = "查看Token", code = "displayNewToken", operationHandler = FabosJsonOpenApiDataProxy.class, operationParam = DISPLAY_TOKEN, mode = RowOperation.Mode.HEADER, callHint = ""),
        }
)
@Getter
@Setter
public class FabosJsonOpenApi extends MetaModel {

    @FabosJsonField(
            views = @View(title = "AppID", sortable = true),
            edit = @Edit(title = "AppID", show = false)
    )
    @Column(unique = true)
    private String appid;

    @FabosJsonField(
            views = @View(title = "名称", sortable = true),
            edit = @Edit(title = "名称", notNull = true, search = @Search(vague = true))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "Token有效期", sortable = true),
            edit = @Edit(title = "Token有效期", tips = "小时, 0则永不过期", numberType = @NumberType(min = 0), notNull = true)
    )
    private Integer expire;

    @FabosJsonField(
            views = @View(title = "状态", sortable = true),
            edit = @Edit(
                    title = "状态", search = @Search, type = EditType.BOOLEAN, notNull = true,
                    boolType = @BoolType(trueText = "激活", falseText = "锁定")
            )
    )
    private Boolean status = true;

    @FabosJsonField(
            views = @View(title = "可访问的API资源", type = ViewType.TABLE_VIEW),
            edit = @Edit(title = "可访问的API资源", search = @Search, type = EditType.TAB_TABLE_ADD)
    )
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private List<FabosJsonOpenApiResources> availableResources;

    @FabosJsonField(
            sort = 2000,
            views = @View(title = "密钥"),
            edit = @Edit(title = "密钥", show = false, readonly = @Readonly)
    )
    private String secret;

    private String currentToken;

    public static String maskSecret(String secret) {
        return secret.substring(0, 4) + "********" + secret.substring(secret.length() - 4);
    }

    public Map<String, String> transformToMap(String expireTime) {
        HashMap<String, String> objectHashMap = new HashMap<>();
        objectHashMap.put("token", getCurrentToken());
        objectHashMap.put("secret", maskSecret(getSecret()));
        objectHashMap.put("appId", getAppid());
        objectHashMap.put("expireTime", Objects.isNull(expireTime) ? "长期有效" : expireTime);
        return objectHashMap;
    }

}