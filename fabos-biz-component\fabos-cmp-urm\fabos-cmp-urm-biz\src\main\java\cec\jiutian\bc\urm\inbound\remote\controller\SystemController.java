package cec.jiutian.bc.urm.inbound.remote.controller;

import cec.jiutian.bc.urm.domain.systemBasicConfig.service.SystemService;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import cec.jiutian.core.view.fabosJson.view.FabosJsonApiModel;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description:  系统配置查询  不能验证权限，所以不能走通用查询
 */
@RestController
@RequestMapping("/fabos-cmp-urm"+ FabosJsonRestPath.FABOS_API)
public class SystemController {
    @Resource
    private SystemService systemService;

    @GetMapping("/getSystemConfig")
    public FabosJsonApiModel getSystemConfig() {
        return FabosJsonApiModel.successApi(systemService.getCombinedSystemConfig());
    }

}
