package cec.jiutian.bc.generalModeler.domain.measureUnit.model;

import cec.jiutian.bc.generalModeler.domain.measureUnit.handler.CalculateDynamicHandler;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DependFiled;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date ：2024/10/30 16:40
 * @description：
 */
@FabosJson(
        name = "单位换算规则试算数据模型"
)
@Getter
@Setter
public class UnitConversionRuleData {

    @Id
    @FabosJsonField(
            edit = @Edit(title = "", show = false)
    )
    private String id;

    @FabosJsonField(
            views = @View(title = "主计量单位", column = "unitChnName"),
            edit = @Edit(title = "主计量单位",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "unitChnName",
                            type = ReferenceTableType.SelectShowTypeMTO.LIST, groupField = "unitType")
            )
    )
    @ManyToOne
    @JoinColumn(name = "source_unit_id")
    private MeasureUnit sourceMeasureUnit;

    @FabosJsonField(
            views = @View(title = "辅计量单位", column = "targetMeasureUnitName"),
            edit = @Edit(title = "辅计量单位",
                    type = EditType.REFERENCE_TABLE,
                    notNull = true,
                    referenceTableType = @ReferenceTableType(id = "id", label = "targetMeasureUnitName",
                            type = ReferenceTableType.SelectShowTypeMTO.LIST, groupField = "auxiliaryUnitType")
            )
    )
    @ManyToOne
    @JoinColumn(name = "target_unit_id")
    private UnitConversionRule targetMeasureUnit;

    @FabosJsonField(
            edit = @Edit(title = "主单位数量", notNull = true,
                    numberType = @NumberType(min = 0))
    )
    private Double mainUnitParam;

    @FabosJsonField(
            edit = @Edit(title = "辅单位数量", type = EditType.input_text, readonly = @Readonly),
            dynamicField = @DynamicField(
                    dependFiled = @DependFiled(changeBy = {"sourceMeasureUnit", "targetMeasureUnit", "mainUnitParam"},
                            dynamicHandler = CalculateDynamicHandler.class)
            )
    )
    private String auxiliaryUnitParam;

//    @FabosJsonField(
//            edit = @Edit(title = "精度",readonly = @Readonly())
//    )
//    private String precision;
//
//    @FabosJsonField(
//            edit = @Edit(title = "换算公式", notNull = true,readonly = @Readonly())
//    )
//    private String formula;
//
//    @FabosJsonField(
//            views = @View(title = "换算公式模版",show = false),
//            edit = @Edit(title = "换算公式模版", show = false,
//                    type = EditType.CHOICE,
//                    choiceType = @ChoiceType(fetchHandler = ConversionFormulaTemplateEnum.class))
//    )
//    private String formulaTemplate;
//
//    @FabosJsonField(
//            edit = @Edit(title = "转换系数", readonly = @Readonly(),
//                    inputType = @InputType(regex = "^\\d+(\\.\\d+)?$"))
//    )
//    private String coefficient;
//
//    @FabosJsonField(
//            edit = @Edit(title = "修正值",readonly = @Readonly(),
//                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "formulaTemplate=='TwoVariableMultiplication' || formulaTemplate=='TwoVariableDivision'"),
//                    inputType = @InputType(regex = "^(\\-|\\+)?\\d+(\\.\\d+)?$"))
//    )
//    private String constantTerm;
//
//    @FabosJsonField(
//            views = @View(title = "换算公式_计算用",show = false),
//            edit = @Edit(title = "换算公式_计算用", show = false)
//    )
//    private String calculateFormula;
//
//    @FabosJsonField(
//            edit = @Edit(title = "换算规则描述",readonly = @Readonly())
//    )
//    private String ruleDescription;
}
