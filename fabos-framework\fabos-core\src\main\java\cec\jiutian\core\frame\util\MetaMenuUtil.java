package cec.jiutian.core.frame.util;

import cec.jiutian.bc.urm.dto.MetaMenu;
import cec.jiutian.core.frame.constant.FabosFunPermissions;
import cec.jiutian.core.frame.constant.MenuButtonTypeEnum;
import cec.jiutian.core.frame.constant.MenuShowTypeEnum;
import cec.jiutian.core.frame.constant.MenuSubTypeEnum;
import cec.jiutian.core.frame.constant.MenuTypeEnum;
import cec.jiutian.core.frame.service.FabosJsonCoreService;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowBaseOperation;
import cec.jiutian.view.type.RowOperation;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.RandomStringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

/**
 *
 */
@Getter
@Setter
public class MetaMenuUtil {

//    public static MetaMenu createRootMenu(String code, String name, String icon, Integer sort, String oid, String componentName) {
//        MetaMenu metaMenu = new MetaMenu();
//        metaMenu.setCode(code);
//        metaMenu.setName(name);
//        metaMenu.setStatus(1);
//        metaMenu.setSort(sort);
//        metaMenu.setIcon(icon);
//        metaMenu.setOid(oid);
//        metaMenu.setComponentName(componentName);
//        return metaMenu;
//    }

//    public static MetaMenu createSimpleMenu(String code, String name, String value, MetaMenu parent, Integer sort, String icon, String type, Integer status, String oid, String componentName) {
//        MetaMenu metaMenu = new MetaMenu();
//        metaMenu.setCode(code);
//        metaMenu.setName(name);
//        metaMenu.setType(type);
//        metaMenu.setStatus(status);
//        metaMenu.setSort(sort);
//        metaMenu.setIcon(icon);
//        metaMenu.setOid(oid);
//        metaMenu.setValue(value);
//        metaMenu.setComponentName(componentName);
//        metaMenu.setParentMenu(parent);
//        createOperations(metaMenu);
//        return metaMenu;
//    }


    public static void createOperations(MetaMenu metaMenu) {
        if (null != metaMenu.getType() && null != metaMenu.getValue()) {
            //csv 读取的 type是subType
            if (MenuSubTypeEnum.checkExist(metaMenu.getType())) {
                List<MetaMenu> operations = new ArrayList<>();
                AtomicInteger i = new AtomicInteger();
                Optional.ofNullable(FabosJsonCoreService.getFabosJson(metaMenu.getValue())).ifPresent(it -> {
                    Map<String,RowBaseOperation> map = new HashMap<>();
                    for (RowBaseOperation baseOperation:it.getFabosJson().rowBaseOperation()) {
                        map.put(baseOperation.code(),baseOperation);
                    }
                    //Map<String,RowBaseOperation> map = Arrays.stream(it.getFabosJson().rowBaseOperation()).collect(Collectors.toMap(RowBaseOperation::code, d -> d));
                    Power power = it.getFabosJson().power();
                    for (FabosFunPermissions fun : FabosFunPermissions.values()) {
                        if (fun.verifyPower(power)) {
                            MetaMenu operation = new MetaMenu(
                                    MetaMenuUtil.getCode(),
                                    fun.getName(), MenuTypeEnum.BUTTON.getCode(),
                                    getFunPermissionsCode(metaMenu.getValue(), fun),
                                    metaMenu, i.addAndGet(10), metaMenu.getOid(), metaMenu.getComponentName(), metaMenu.getApplicationName()
                            );
                            //默认弹窗
                            operation.setShowType(MenuShowTypeEnum.POPUP.getType());
                            RowBaseOperation baseOperation = map.get(fun.name());
                            if(baseOperation!=null){
                                //如果配置了就可以修改
                                operation.setShowType(baseOperation.menuShowTypeEnum().name());
                                operation.setButtonType(baseOperation.menuButtonType().name());

                            }
                            operations.add(operation);
                        }
                    }
                    for (RowOperation row : it.getFabosJson().rowOperation()) {
                        MetaMenu operation = new MetaMenu(
                                MetaMenuUtil.getCode(),
                                row.title(), MenuTypeEnum.BUTTON.getCode(),
                                row.code(),
                                metaMenu, i.addAndGet(10), metaMenu.getOid(), metaMenu.getComponentName()
                        );
                        operation.setShowType(row.menuShowTypeEnum().name());
                        // TODO:菜单调整-0721：这里有冲突，自定义按钮上的按钮类型，与菜单模型中的按钮类型有冲突（菜单上的按钮类型没有混入，而自定义按钮的按钮类型有混入）
                        operation.setButtonType(row.menuButtonType().name());
                        operations.add(operation);
                    }
                    metaMenu.setOperations(operations);
                });
            }
        }
    }

    /*转换rowOperation中的字段到菜单中的字段
    * 由于rowOperation中枚举不是使用的菜单模型中的枚举，因此需做转换
    * TODO:菜单调整-0721：这个方法为临时解决方案，后面需要统一rowOperation和菜单的配置
    *  */
    public static void convertOperationToButton(MetaMenu operation){
        if (operation.getButtonType().equals(RowOperation.MenuButtonTypeEnum.MIX.getName()) ||
        operation.getButtonType().equals(RowOperation.MenuButtonTypeEnum.DEFAULT.getName())
        ){
            operation.setButtonType(MenuButtonTypeEnum.DEFAULT.getType());
        } else if (operation.getButtonType().equals(RowOperation.MenuButtonTypeEnum.JUMP.getName())) {
            operation.setButtonType(MenuButtonTypeEnum.JUMP.getType());
        }



    }


//    public static MetaMenu createSimpleMenu(String code, String name, String value, MetaMenu parent, Integer sort, String type, String oid, String componentName) {
//        return createSimpleMenu(code, name, value, parent, sort, null, type, 1, oid, componentName);
//    }

//    public static MetaMenu createFabosJsonClassMenu(Class<?> fabosJsonClass, MetaMenu parent, Integer sort, MenuSubTypeEnum menuTypeEnum, Integer status, String oid, String componentName) {
//        return createSimpleMenu(fabosJsonClass.getSimpleName(), fabosJsonClass.getAnnotation(FabosJson.class).name(),
//                fabosJsonClass.getSimpleName(), parent, sort, "", menuTypeEnum.getCode(), status, oid, componentName);
//    }

//    public static MetaMenu createFabosJsonClassMenu(Class<?> fabosJsonClass, MetaMenu parent, Integer sort, String oid, String componentName) {
//        return createFabosJsonClassMenu(fabosJsonClass, parent, sort, MenuSubTypeEnum.TABLE, 1, oid, componentName);
//    }
//
//    public static MetaMenu createFabosJsonClassMenu(Class<?> fabosJsonClass, MetaMenu parent, Integer sort, Integer status, String oid, String componentName) {
//        return createFabosJsonClassMenu(fabosJsonClass, parent, sort, MenuSubTypeEnum.TABLE, status, oid, componentName);
//    }
//
//    public static MetaMenu createFabosJsonClassMenu(Class<?> fabosJsonClass, MetaMenu parent, Integer sort, MenuSubTypeEnum menuTypeEnum, String oid, String componentName) {
//        return createFabosJsonClassMenu(fabosJsonClass, parent, sort, menuTypeEnum, 1, oid, componentName);
//    }

    public static String getFunPermissionsCode(String eruptName, FabosFunPermissions funPermissions) {
        return eruptName + "@" + funPermissions.name();
    }


    /**
     * code只能是随机随机字符串 统一调整初始化的该字段逻辑
     * @return
     */
    public static String getCode(){
        return RandomStringUtils.randomAlphanumeric(8);
    }

}
