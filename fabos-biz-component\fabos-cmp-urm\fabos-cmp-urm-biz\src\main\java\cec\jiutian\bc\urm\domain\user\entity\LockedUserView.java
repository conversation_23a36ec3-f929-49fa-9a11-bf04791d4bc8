package cec.jiutian.bc.urm.domain.user.entity;

import cec.jiutian.bc.urm.domain.user.event.UserUnlockOperationHandler;
import cec.jiutian.core.data.annotation.LinkTable;
import cec.jiutian.core.frame.constant.RegexConst;
import cec.jiutian.core.frame.constant.SwitchStatus;
import cec.jiutian.core.frame.constant.YesOrNoStatus;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.SubTableField;
import cec.jiutian.view.config.Comment;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;


@Getter
@Setter
@Entity
@LinkTable
@Table(name = "fd_meta_user")
@FabosJsonI18n
@FabosJson(name = "锁定用户",
        orderBy = "LockedUser.createTime desc",
        power = @Power(add = false, edit = false, delete = false, export = false, importable = false)
)
public class LockedUserView extends MetaModel {

    @FabosJsonField(
            views = @View(title = "账号"),
            edit = @Edit(title = "账号",
                    readonly = @Readonly
            )
    )
    @SubTableField
    private String account;

    @FabosJsonField(
            views = @View(title = "姓名"),
            edit = @Edit(title = "姓名",
                    readonly = @Readonly)
    )
    @SubTableField
    private String name;

    @FabosJsonField(
            views = @View(title = "工号"),
            edit = @Edit(title = "工号",
                    readonly = @Readonly
            )
    )
    private String employeeNumber;

    /**
     * 该字段用于登录，锁定不可登录。该字段不影响业务是否可选择用户
     */
    @FabosJsonField(
            views = @View(title = "锁定标识"),
            edit = @Edit(title = "锁定标识",
                    show = false,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {YesOrNoStatus.ChoiceFetch.class}))
    )
    private String lockedFlag;

    @Comment("锁定日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "锁定日期",show = false),
            edit = @Edit(title = "锁定日期", show = false, search = @Search)
    )
    private LocalDateTime lockDate; // 锁定日期字段

    @FabosJsonField(
            views = @View(title = "锁定原因"),
            edit = @Edit(title = "锁定原因",
                    notNull = true,
                    type = EditType.TEXTAREA,
                    inputType = @InputType(length = 100)
            )
    )
    private String lockReason;

}
