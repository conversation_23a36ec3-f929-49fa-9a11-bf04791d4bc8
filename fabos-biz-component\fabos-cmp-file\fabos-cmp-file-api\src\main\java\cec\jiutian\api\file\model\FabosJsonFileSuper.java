package cec.jiutian.api.file.model;

import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.View;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.SneakyThrows;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * <AUTHOR>
 * @time 2024-10-30 22:42
 */

@Getter
@Setter
@MappedSuperclass
@NoArgsConstructor
public class FabosJsonFileSuper extends MetaModel {

    @FabosJsonField(
            views = @View(title = "文件名")
    )
    private String originalFileName;
    @Column(unique = true)
    @FabosJsonField(
            views = @View(title = "唯一文件名")
    )
    private String modifiedFileName;
    @FabosJsonField(
            views = @View(title = "文件扩展名")
    )
    private String filenameExtension;

    @FabosJsonField(
            views = @View(title = "文件系统地址")
    )
    private String pathOnFileSystem;

    /**
     * 如果使用的OSS不包含存储桶的概念，则留空
     */
    private String bucket;
    @Column(name = "path_on_oss")
    @FabosJsonField(
            views = @View(title = "对象存储地址")
    )
    private String pathOnOSS;

    @FabosJsonField(
            views = @View(title = "上传人员")
    )
    private String createBy;

    @FabosJsonField(
            views = @View(title = "上传时间")
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime createTime;

    @FabosJsonField(
            views = @View(title = "文件大小（字节）")
    )
    private Long sizeInBytes;
    @FabosJsonField(
            views = @View(title = "md5")
    )
    private String md5;
    /**
     * 公共文件标记：如果此项为 true 则表示此文件下载时不受权限管控，可在 public 接口中下载
     */
    @FabosJsonField(
            views = @View(title = "下载时不校验权限")
    )
    private Boolean nonProtected;
    @Column(name = "file_uuid", unique = true)
    @FabosJsonField(
            views = @View(title = "文件id")
    )
    private String fileUUID;

    @SneakyThrows
    public FabosJsonFileSuper (MultipartFile file) {
        String fileUUID = UUID.randomUUID().toString();
        this.setFileUUID(fileUUID);
        this.setOriginalFileName(file.getOriginalFilename());
        this.setSizeInBytes(file.getSize());
        this.setMd5(DigestUtils.md5Hex(file.getInputStream()));
    }

    @SneakyThrows
    public FabosJsonFileSuper (MultipartFile file, String filename) {
        String fileUUID = UUID.randomUUID().toString();
        this.setFileUUID(fileUUID);
        this.setOriginalFileName(StringUtils.isNotBlank(filename) ? filename : file.getOriginalFilename());
        this.setSizeInBytes(file.getSize());
        this.setMd5(DigestUtils.md5Hex(file.getInputStream()));
    }
}

