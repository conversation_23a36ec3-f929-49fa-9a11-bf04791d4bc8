package cec.jiutian.bc.urm.inbound.local.service.command;

import cec.jiutian.bc.urm.domain.menu.entity.Menu;
import cec.jiutian.common.constant.SessionKey;
import cec.jiutian.core.frame.context.MetaContext;
import cec.jiutian.core.frame.context.MetaFabosJson;
import cec.jiutian.core.frame.service.FabosJsonCoreService;
import cec.jiutian.core.service.FabosJsonSessionService;
import cec.jiutian.bc.urm.dto.MetaUserinfo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class FabosJsonContextService {

    @Resource
    private FabosJsonSessionService sessionService;

    //获取上下文对象
    @Deprecated
    public Class<?> getContextFabosJsonClass() {
        return FabosJsonCoreService.getFabosJson(MetaContext.getFabosJson().getName()).getClazz();
    }

    //获取当前请求token
    public String getCurrentToken() {
        return sessionService.getCurrentToken();
    }

    public MetaUserinfo getSimpleUserInfo() {
        return sessionService.getSimpleUserInfo();
    }

    //获取当前菜单对象
    public Menu getCurrentFabosJsonMenu() {
        MetaFabosJson metaFabosJson = MetaContext.getFabosJson();
        return sessionService.getMapValue(SessionKey.MENU_VALUE_MAP + getCurrentToken()
                , (metaFabosJson.getMenuValue() == null ? metaFabosJson.getName() : metaFabosJson.getMenuValue()).toLowerCase(),
                Menu.class);
    }

}
