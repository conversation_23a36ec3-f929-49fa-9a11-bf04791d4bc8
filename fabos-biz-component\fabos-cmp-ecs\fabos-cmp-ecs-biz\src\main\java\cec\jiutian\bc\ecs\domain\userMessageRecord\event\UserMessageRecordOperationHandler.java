package cec.jiutian.bc.ecs.domain.userMessageRecord.event;

import cec.jiutian.bc.ecs.domain.userMessageRecord.entity.UserMessageRecord;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.OperationHandler;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Transactional
@Component
public class UserMessageRecordOperationHandler  implements OperationHandler<UserMessageRecord, Void> {


    private final FabosJsonDao fabosJsonDao;

    public UserMessageRecordOperationHandler(FabosJsonDao fabosJsonDao) {
        this.fabosJsonDao = fabosJsonDao;
    }

    @Override
    public String exec(List<UserMessageRecord> data, Void modelObject, String[] param) {
        UserMessageRecord userMessageRecord = data.get(0);
        userMessageRecord.setStatus("Y");
        userMessageRecord.setUpdateTime(LocalDateTime.now());
        fabosJsonDao.mergeAndFlush(userMessageRecord);
        return "消息已读";
    }
}
