package cec.jiutian.bc.urm.domain.user.event;

import cec.jiutian.bc.urm.domain.user.entity.LockedUser;
import cec.jiutian.bc.urm.domain.user.service.UserService;
import cec.jiutian.view.fun.OperationHandler;
import cec.jiutian.bc.urm.dto.MetaUserinfo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Component
public class UserUnlockOperationHandler implements OperationHandler<LockedUser, Void> {
    @Resource
    private UserService userService;
    @Override
    public String exec(List<LockedUser> data, Void modelObject, String[] param) {
        MetaUserinfo curr = userService.getSimpleUserInfo();
        if(curr==null){
            return "msg.error('current user is not login!')";
        }
//        if (!(curr.getRoles().contains(UserManagerTypeEnum.securityManager.getCode())||curr.getRoles().contains(UserManagerTypeEnum.superManager.getCode()))) {
//            return "msg.error(当前登录用户无操作权限')";
//        }
        //这里不会存在超级管理员 所以直接使用电话号码解锁即可
        userService.unlockUser(data.get(0));
        return "msg.success('解锁成功')";
    }
}
