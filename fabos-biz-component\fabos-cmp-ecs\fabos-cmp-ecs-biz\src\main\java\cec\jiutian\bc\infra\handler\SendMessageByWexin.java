package cec.jiutian.bc.infra.handler;

import cec.jiutian.bc.ecs.domain.messagerecord.entity.MessageRecord;
import cec.jiutian.bc.ecs.inbound.local.enums.DispatchWayEnum;
import cec.jiutian.bc.infra.port.client.WeixinClient;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.frame.module.MetaUser;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @date ：2023/5/22 17:48
 * @description：
 */
@Component(DispatchWayEnum.WECHAT_CODE)
public class SendMessageByWexin implements MessageHandler {
    private static final Logger log = LoggerFactory.getLogger(SendMessageByWexin.class);

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private WeixinClient weixinClient;

    @Override
    public void sendMessage(MessageRecord createDTO) throws Exception {
        MetaUser toUser = fabosJsonDao.queryEntity(MetaUser.class, "phoneNumber=:phoneNumber", new HashMap<>() {{
            put("phoneNumber", createDTO.getDispatchUser());
        }});
        if (toUser != null) {
            weixinClient.weixinSendTextMessage(Collections.singletonList(toUser.getWechatAccount()), createDTO.getContent());
        }
        if (createDTO.getDispatchWeixinGroup() != null) {
            weixinClient.weixinSendGroupTextMessage(createDTO.getDispatchWeixinGroup().getChatId(), createDTO.getContent());
        }

        log.info("微信发送完成");
    }
}
