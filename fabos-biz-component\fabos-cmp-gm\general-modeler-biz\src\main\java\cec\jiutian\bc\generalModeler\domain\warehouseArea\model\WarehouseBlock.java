package cec.jiutian.bc.generalModeler.domain.warehouseArea.model;


import cec.jiutian.bc.generalModeler.domain.warehouseArea.handler.WarehouseBlockHoldHandler;
import cec.jiutian.bc.generalModeler.domain.warehouseArea.handler.WarehouseBlockReferenceAddHandler;
import cec.jiutian.bc.generalModeler.domain.warehouseArea.handler.WarehouseBlockUnHoldHandler;
import cec.jiutian.bc.generalModeler.domain.warehouseArea.proxy.WarehouseBlockProxy;
import cec.jiutian.bc.generalModeler.enumeration.WarehouseBlockPurposeEnum;
import cec.jiutian.bc.generalModeler.enumeration.WarehouseBlockTypeEnum;
import cec.jiutian.bc.generalModeler.enumeration.WarehouseStateEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.ReferenceAddType;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.config.Comment;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "库区",
        dataProxy = WarehouseBlockProxy.class,
        orderBy = "WarehouseBlock.createTime desc",
        rowOperation = {
                @RowOperation(
                        code = "WarehouseBlock@Hold",
                        operationHandler = WarehouseBlockHoldHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        title = "锁定",
                        ifExpr = "lockState == 'Locked'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE),
                @RowOperation(
                        code = "WarehouseBlock@UnHold",
                        operationHandler = WarehouseBlockUnHoldHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        title = "解锁",
                        ifExpr = "lockState == 'Normal'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE)
        }
)
@Table(name = "mos_warehouse_block",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"code", "oid"}),
                @UniqueConstraint(columnNames = {"name", "oid"})
        }
)
@Entity
@Getter
@Setter
@TemplateType(type = "multiTable")
public class WarehouseBlock extends MetaModel {

    @FabosJsonField(
            views = @View(title = "库区编码"),
            edit = @Edit(title = "库区编码", notNull = true, search = @Search(vague = true),
                    readonly = @Readonly(add = false, edit = true), inputType = @InputType(length = 40))
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "库区名称"),
            edit = @Edit(title = "库区名称", notNull = true, search = @Search(vague = true),
                    readonly = @Readonly(add = false, edit = true), inputType = @InputType(length = 40))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "库区描述"),
            edit = @Edit(title = "库区描述", type = EditType.input_text)
    )
    private String description;

    @FabosJsonField(
            views = @View(title = "区域类型"),
            edit = @Edit(title = "区域类型",
                    type = EditType.CHOICE,
                    notNull = true,
                    choiceType = @ChoiceType(fetchHandler = WarehouseBlockPurposeEnum.class))
    )
    private String purpose;


    @Comment("引用字段，对应warehouse的ID")
    @FabosJsonField(
            views = @View(title = "所属仓库", column = "name"),
            edit = @Edit(title = "所属仓库", notNull = true, search = @Search(vague = true),
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name", type = ReferenceTableType.SelectShowTypeMTO.LIST)
            )
    )
    @ManyToOne
    private Warehouse warehouse;

    @FabosJsonField(
            views = @View(title = "库区类型"),
            edit = @Edit(title = "库区类型", notNull = true, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = WarehouseBlockTypeEnum.class)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "warehouse", beFilledBy = "type"))
    )
    private String type;

    @FabosJsonField(
            views = @View(title = "层数"),
            edit = @Edit(title = "层数", type = EditType.NUMBER,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "type == 'Stereo'"))
    )
    private Integer rowQuantity;


    @FabosJsonField(
            views = @View(title = "列数"),
            edit = @Edit(title = "列数", type = EditType.NUMBER,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "type == 'Stereo'"))
    )
    private Integer columnQuantity;


    @FabosJsonField(
            views = @View(title = "盘点锁"),
            edit = @Edit(title = "盘点锁", show = false, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = WarehouseStateEnum.class))
    )
    private String lockState;

    @FabosJsonField(
            views = @View(title = "地面标识"),
            edit = @Edit(title = "地面标识", type = EditType.input_text,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "type == 'Block'"))
    )
    // 平库货位填写，对应地面标识号
    private String groundSign;



/*    @FabosJsonField(
            views = @View(title = "巷道号"),
            edit = @Edit(title = "巷道号", type = EditType.input_text,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "type == 'Stereo'"))
    )
    // 立库库区填写
    private String laneNumber;

    @FabosJsonField(
            views = @View(title = "允许存放物料品类"),
            edit = @Edit(title = "允许存放物料品类", type = EditType.input_text)
    )
    // 填写内容需要与物料分类表中名称编码一致，多个分类用逗号分隔
    private String storageCategory;*/


    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注", type = EditType.TEXTAREA)
    )
    private String remark;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = false)
    @JoinColumn(name = "parent_id")
    @OrderBy
    @FabosJsonField(
            edit = @Edit(title = "允许存放的物料", type = EditType.TAB_REFER_ADD,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "warehouse != null")),
            referenceAddType = @ReferenceAddType(referenceClass = "MaterialDTO", queryCondition = "{\"warehouseRelationId\":\"${warehouse.id}\"}",
                    referenceAddHandler = WarehouseBlockReferenceAddHandler.class),
            views = @View(title = "允许存放的物料", type = ViewType.TABLE_VIEW, extraPK = "materialId")
    )
    private List<WarehouseBlockDetail> warehouseBlockDetails;

}
