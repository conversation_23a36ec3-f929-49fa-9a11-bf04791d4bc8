package cec.jiutian.bc.generalModeler.handler;


import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleModel;
import cec.jiutian.bc.generalModeler.service.NamingRuleService;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


@Component
public class NamingRuleGenerateHandler implements DependFiled.DynamicHandler<NamingRuleModel> {

    @Resource
    private NamingRuleService namingRuleService;

    @Override
    @SneakyThrows
    public Map<String, Object> handle(NamingRuleModel namingRuleModel) {
        Map<String, Object> map = new HashMap<>();
        map.put("generalCode", String.valueOf(namingRuleService.getNameCode(namingRuleModel.getNamingCode(), 1, namingRuleModel.getParameters()).get(0)));
        return map;
    }

}