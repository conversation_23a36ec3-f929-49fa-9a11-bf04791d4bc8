package cec.jiutian.bc.urm.inbound.remote.feign;

import cec.jiutian.api.remoteCallLog.dto.RemoteCallResult;
import cec.jiutian.api.remoteCallLog.enums.RemoteCallCodeEnum;
import cec.jiutian.bc.urm.service.RoleAuthDistributeService;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/fabos-cmp-urm"+ FabosJsonRestPath.FABOS_REMOTE_API)
public class MenuFeign {

    private final RoleAuthDistributeService roleAuthDistributeService;

    private final RemoteCallResult remoteCallResult;

    public MenuFeign(RoleAuthDistributeService roleAuthDistributeService, RemoteCallResult remoteCallResult) {
        this.roleAuthDistributeService = roleAuthDistributeService;
        this.remoteCallResult = remoteCallResult;
    }


    // 接收UBP下发的角色系统菜单权限，并更新
    // 参数格式：{"roleId": "1", "menus": ["menuId1", "menuId2"]}
    @PostMapping("/updateRoleMenus")
    @Transactional
    public RemoteCallResult updateRoleMenus(@RequestBody Map<String, Object> params) {
        String roleId = params.get("roleId").toString();
        List<String> roleMenus = (List<String>) params.get("menus");
        try {
            roleAuthDistributeService.updateRoleMenus(roleId, roleMenus);
            return remoteCallResult.success();
        } catch (Exception e) {
            return remoteCallResult.error(RemoteCallCodeEnum.FAILED.getCode(), e.getMessage());
        }
    }
}
