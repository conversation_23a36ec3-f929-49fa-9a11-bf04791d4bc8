package cec.jiutian.bc.generalModeler.handler;

import cec.jiutian.bc.generalModeler.domain.material.model.Product;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.view.fun.OperationHandler;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ProductAddHandler implements OperationHandler<Product, Product> {

    @Override
    public Product fabosJsonFormValue(List<Product> data, Product fabosJsonForm, String[] param) {
        MetaUser currentUser = new MetaUser(UserContext.get());
        Product product = new Product();
        product.setStoreKeeper(currentUser);
        return product;
    }
}
