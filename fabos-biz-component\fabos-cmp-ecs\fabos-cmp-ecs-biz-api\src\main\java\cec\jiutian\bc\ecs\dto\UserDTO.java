package cec.jiutian.bc.ecs.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：2023/5/24 17:34
 * @description：
 */
@ApiModel("UserDTO")
@Data
public class UserDTO {

    /**
     * 登录名
     */
    @ApiModelProperty("登录名")
    private String account;

    /**
     * 登录名
     */
    @ApiModelProperty("姓名")
    private String name;

    /**
     * 登录名
     */
    @ApiModelProperty("电话号码")
    private String phoneNumber;

    /**
     * 登录名
     */
    @ApiModelProperty("邮箱")
    private String email;

    private Integer messageCount;

    private String receiveRate;

    private String averageTime;
}
