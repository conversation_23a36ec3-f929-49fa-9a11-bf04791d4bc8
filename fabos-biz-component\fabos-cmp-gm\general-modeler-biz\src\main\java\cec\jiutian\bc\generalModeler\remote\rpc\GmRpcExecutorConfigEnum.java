package cec.jiutian.bc.generalModeler.remote.rpc;

import cec.jiutian.functionexecutor.dto.InvokeDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description: 函数执行器调用配置 这里需要配置的是被调用者的参数信息。避免多次调用重复构建InvokeDTO参数
 */
@AllArgsConstructor
@Getter
public enum GmRpcExecutorConfigEnum {
    //用户重置密码
    RESET_PASSWORD("fabos-cmp-urm", "cec.jiutian.bc.urm.domain.user.service.UserService", "resetPassword", new String[]{"java.lang.String"}, "fabos-cmp-urm", "3.2.0"),
    //其他被调用过的接口
    ;

    private final String bizName;
    private final String classFullName;
    private final String methodName;
    private final String[] argsType;
    private final String group;
    private final String moduleVersion;

    public InvokeDTO buildInvokeDTO(Object[] args) {
        InvokeDTO dto = new InvokeDTO();
        dto.setBizName(this.bizName);
        dto.setClassFullName(this.classFullName);
        dto.setMethodName(this.methodName);
        dto.setArgsType(this.argsType.clone());
        dto.setArgs(args);
        dto.setGroup(this.group);
        dto.setModuleVersion(this.moduleVersion);
        return dto;
    }

}
