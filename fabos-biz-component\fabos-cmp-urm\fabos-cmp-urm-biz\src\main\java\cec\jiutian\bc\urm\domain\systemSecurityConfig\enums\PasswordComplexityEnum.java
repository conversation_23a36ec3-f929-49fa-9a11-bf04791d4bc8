package cec.jiutian.bc.urm.domain.systemSecurityConfig.enums;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
public enum PasswordComplexityEnum {
//    大小写英文字母、数字、特殊字符不限制
        NONE("1", " 大小写英文字母、数字、特殊字符不限制"),
//    大小写英文字母、数字、特殊字符中二者
        TWO("2", "大小写英文字母、数字、特殊字符中二者"),
//    大小写英文字母、数字、特殊字符中三者
        THREE("3", "大小写英文字母、数字、特殊字符中三者"),
    ;

    private String code;

    private String name;

    PasswordComplexityEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static class ChoiceFetch implements ChoiceFetchHandler {
        @Override
        public List<VLModel> fetch(String[] params) {
            return Stream.of(PasswordComplexityEnum.values()).map(complexityEnum ->
                    new VLModel(complexityEnum.getCode(), complexityEnum.getName())).collect(Collectors.toList());
        }

    }
}
