package cec.jiutian.api.remoteCallLog.config;

import lombok.Data;
import lombok.Getter;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Data
@Getter
public class RemoteCallLogMQConfig {

    public static final String ROUTING_KEY = "fabos.log.remoteCallLog";

    @Value("${mq.queue.remoteCallLog:remoteCallLog}")
    private String queueName;
    @Value("${mq.queue.exchange.remoteCallLog:remoteCallLogExchange}")
    private String exchange;

    @Bean
    public Queue remoteCallLogQueue() {
        return new Queue(getQueueName(), false);
    }

    @Bean("remoteCallLogDirectExchange")
    DirectExchange exchange() {
        return new DirectExchange(getExchange());
    }

    @Bean
    Binding bindingQueue(Queue remoteCallLogQueue, @Qualifier("remoteCallLogDirectExchange") DirectExchange exchange) {
        return BindingBuilder.bind(remoteCallLogQueue).to(exchange).with(ROUTING_KEY);
    }


}
