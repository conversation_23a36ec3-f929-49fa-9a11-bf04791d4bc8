package cec.jiutian.bc.urm.service;

import cec.jiutian.bc.urm.domain.menu.entity.ExternalMenu;
import cec.jiutian.bc.urm.domain.menu.entity.Menu;
import cec.jiutian.bc.urm.domain.role.entity.RoleAuthDistribute;
import cec.jiutian.core.frame.constant.MenuTypeEnum;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.fc.log.domain.permissionOperationLog.dto.PermissionDTO;
import cec.jiutian.fc.log.domain.permissionOperationLog.manager.PermissionLogManager;
import cec.jiutian.fc.log.enums.PermissionTypeEnum;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @description: 用于应用权限管理的全代码页面
 */
@Service
@Slf4j
public class RoleAuthDistributeService {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private EntityManager entityManager;

    @Resource
    private PermissionLogManager permissionLogManager;

    @Transactional(readOnly = true)
    public Map<String, Object> getRoleExternalMenus(String roleId) {
        Map<String, Object> result = new HashMap<>();

        // Fetch all ExternalMenus
        List<ExternalMenu> allExternalMenus = fetchAllExternalMenus();
        result.put("allExternalMenus", buildTree(allExternalMenus));

        // Fetch ExternalMenus bound to the role
        List<ExternalMenu> roleExternalMenus = fetchRoleExternalMenus(roleId);
        Map<String, List<String>> roleExternalMenusMap = roleExternalMenus.stream()
            .collect(Collectors.groupingBy(ExternalMenu::getSystemCode, 
                Collectors.mapping(ExternalMenu::getId, Collectors.toList())));
        result.put("roleExternalMenus", roleExternalMenusMap);

        return result;
    }

    private List<ExternalMenu> fetchAllExternalMenus() {
        return fabosJsonDao.queryEntityList(ExternalMenu.class);
    }

    private List<ExternalMenu> fetchRoleExternalMenus(String roleId) {
        return fabosJsonDao.findById(RoleAuthDistribute.class,roleId).getExternalMenus();

    }

    private List<ExternalMenuDTO> buildTree(List<ExternalMenu> menus) {
        Map<String, ExternalMenu> menuMap = menus.stream().collect(Collectors.toMap(ExternalMenu::getId, Function.identity()));
        Map<String, ExternalMenuDTO> dtoMap = new HashMap<>();
        List<ExternalMenuDTO> rootMenus = new ArrayList<>();

        for (ExternalMenu menu : menus) {
            ExternalMenuDTO menuDTO = dtoMap.computeIfAbsent(menu.getId(), id -> ExternalMenuDTO.fromEntity(menu));
            if (menu.getParent() == null) {
                rootMenus.add(menuDTO);
            } else {
                ExternalMenuDTO parentDTO = dtoMap.computeIfAbsent(menu.getParent().getId(), id -> ExternalMenuDTO.fromEntity(menuMap.get(id)));
                parentDTO.addChild(menuDTO);
            }
        }

        return rootMenus;
    }

    // 统一基础平台执行角色外部应用菜单权限的变更，并记录日志
    @Transactional
    public void updateRoleExternalMenus(String roleId, Map<String, List<String>> roleExternalMenus) {
        RoleAuthDistribute roleAuthDistribute = fabosJsonDao.findById(RoleAuthDistribute.class, roleId);
        if (roleAuthDistribute == null) {
            throw new FabosJsonApiErrorTip("Role not found");
        }

        List<ExternalMenu> allExternalMenus = fetchAllExternalMenus();
        Map<String, ExternalMenu> externalMenuMap = allExternalMenus.stream()
                .collect(Collectors.toMap(ExternalMenu::getId, Function.identity()));
        // 记录原菜单，用于后续记日志
        Map<String, List<String>> oldMenusBySystem = new HashMap<>();

        for (Map.Entry<String, List<String>> entry : roleExternalMenus.entrySet()) {
            String systemCode = entry.getKey();
            List<String> menuIds = entry.getValue();

            // 按系统编码删除权限
            Set<ExternalMenu> toRemove = roleAuthDistribute.getExternalMenus().stream()
                    .filter(m -> systemCode.equals(m.getSystemCode()))
                    .collect(Collectors.toSet());
            // 将toRemove记录到oldMenusBySystem中
            oldMenusBySystem.put(systemCode, toRemove.stream().map(ExternalMenu::getId).collect(Collectors.toList()));

            // 从角色中移除这些菜单
            toRemove.forEach(roleAuthDistribute.getExternalMenus()::remove);

            // 按系统编码插入权限
            List<ExternalMenu> updatedExternalMenus = menuIds.stream()
                    .map(externalMenuMap::get)
                    .filter(Objects::nonNull)
                    .filter(menu -> systemCode.equals(menu.getSystemCode()))
                    .collect(Collectors.toList());

            roleAuthDistribute.getExternalMenus().addAll(updatedExternalMenus);
        }

        fabosJsonDao.mergeAndFlush(roleAuthDistribute);

        // 记录权限更改日志
        for (Map.Entry<String, List<String>> entry : roleExternalMenus.entrySet()) {
            String systemCode = entry.getKey();
            List<String> oldMenuIds = oldMenusBySystem.get(systemCode);
            List<String> menuIds = entry.getValue();
            try {

                permissionLogManager.updatePermission(getPermissionDTO(roleId, roleAuthDistribute.getName() + "<" + systemCode +">", oldMenuIds, menuIds), PermissionTypeEnum.MENU);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("角色添加菜单添加三员日志异常:", e);
            }
        }
    }

    public PermissionDTO getPermissionDTO(String roleId,String roleName,List<String> idsDb,List<String> ids){
        // 差集 (ids - idsDb)
        List<String> addIds = ids.stream().filter(item -> !idsDb.contains(item)).toList();
        List<String> deleteIds = idsDb.stream().filter(item -> !ids.contains(item)).toList();

        List<String> addNames = getNameByIds(addIds);
        List<String> deleteNames = getNameByIds(deleteIds);
        return new PermissionDTO(roleId,roleName,addIds,addNames,deleteIds,deleteNames);
    }

    private List<String> getNameByIds(List<String> menuIds){
        List<String> result = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(menuIds)){
            EntityManager em = fabosJsonDao.getEntityManager();
            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<ExternalMenu> cq = cb.createQuery(ExternalMenu.class);
            Root<ExternalMenu> root = cq.from(ExternalMenu.class);
            cq.select(root).where(cb.in(root.get("id")).value(menuIds));
            TypedQuery<ExternalMenu> q = em.createQuery(cq);
            List<ExternalMenu> resultList = q.getResultList();
            if(CollectionUtils.isNotEmpty(resultList)){
                resultList.forEach(d->{
                    if(MenuTypeEnum.BUTTON.getCode().equals(d.getModuleTypeCode())){
                        String parentName = d.getParent().getName();
                        result.add(d.getName()+"("+parentName+")");
                    }else {
                        result.add(d.getName());
                    }
                });
            }
        }
        return result;
    }

    // 该方法用于接收UBP下发的角色菜单权限更新（不记录权限变更日志）
    @Transactional
    public void updateRoleMenus(String roleId, List<String> roleMenus) {
        RoleAuthDistribute roleAuthDistribute = fabosJsonDao.findById(RoleAuthDistribute.class, roleId);
        if (roleAuthDistribute == null) {
            throw new FabosJsonApiErrorTip("Role not found");
        }

        List<Menu> allMenus = fabosJsonDao.queryEntityList(Menu.class);
        Map<String, Menu> allMenuMap = allMenus.stream()
                .collect(Collectors.toMap(Menu::getId, Function.identity()));

        // 获取待删除菜单
        Set<Menu> toRemove = roleAuthDistribute.getMenus().stream()
                .collect(Collectors.toSet());
        // 移除这些菜单
        toRemove.forEach(roleAuthDistribute.getMenus()::remove);

        for (String menuId : roleMenus) {
            // 插入权限
            Menu updatedMenu = allMenuMap.get(menuId);
            if (updatedMenu != null) {
                roleAuthDistribute.getMenus().add(updatedMenu);
            }
        }

        fabosJsonDao.mergeAndFlush(roleAuthDistribute);
    }

    public static class ExternalMenuDTO {
        private String id;
        private String name;
        private String systemCode;
        private List<ExternalMenuDTO> children;

        public ExternalMenuDTO(String id, String name, String systemCode, List<ExternalMenuDTO> children) {
            this.id = id;
            this.name = name;
            this.systemCode = systemCode;
            this.children = children != null ? children : new ArrayList<>();
        }

        public String getId() {
            return id;
        }

        public String getName() {
            return name;
        }

        public String getSystemCode() {
            return systemCode;
        }

        public List<ExternalMenuDTO> getChildren() {
            return children;
        }

        public void addChild(ExternalMenuDTO child) {
            this.children.add(child);
        }

        public static ExternalMenuDTO fromEntity(ExternalMenu menu) {
            return new ExternalMenuDTO(menu.getId(), menu.getName(), menu.getSystemCode(), new ArrayList<>());
        }


    }
}
