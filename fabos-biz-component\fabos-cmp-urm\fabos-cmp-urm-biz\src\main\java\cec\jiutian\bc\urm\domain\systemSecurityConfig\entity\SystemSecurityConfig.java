package cec.jiutian.bc.urm.domain.systemSecurityConfig.entity;

import cec.jiutian.bc.urm.domain.systemSecurityConfig.enums.PasswordComplexityEnum;
import cec.jiutian.bc.urm.domain.systemSecurityConfig.enums.UnlockMethodEnum;
import cec.jiutian.bc.urm.domain.systemSecurityConfig.event.SystemSecurityConfigDataProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.BoolType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "system_security_config")
@Getter
@Setter
@FabosJson(
        name = "系统安全配置",
        orderBy = "SystemSecurityConfig.createTime desc",
        dataProxy = {SystemSecurityConfigDataProxy.class},
        power = @Power(delete = false)
)
public class SystemSecurityConfig extends MetaModel {

//    // 是否开启单端登录
//    @FabosJsonField(
//            views = @View(title = "是否开启单端登录"),
//            edit = @Edit(title = "是否开启单端登录", notNull = true,
//                    boolType = @BoolType)
//    )
//    private Boolean singleLoginEnabled;

    // 开启三员管理
    @FabosJsonField(
            views = @View(title = "开启三员管理"),
            edit = @Edit(title = "开启三员管理", notNull = true,
                    boolType = @BoolType)
    )
    private Boolean triadManagementEnabled;

    // 超时登出（分钟）
    @FabosJsonField(
            views = @View(title = " 超时登出（分钟）"),
            edit = @Edit(title = " 超时登出（分钟）", notNull = true,
                    numberType = @NumberType(min = 5, max = 7 * 24 * 60))
    )
    private Integer timeoutLogoutMinutes;

    // 密码有效期（天）
    @FabosJsonField(
            views = @View(title = "密码有效期（天）"),
            edit = @Edit(title = "密码有效期（天）", notNull = true,
                    numberType = @NumberType(min = 1, max = 1000))
    )
    private Integer passwordValidityDays;

    // 身份鉴别失败次数
    @FabosJsonField(
            views = @View(title = "身份鉴别失败次数"),
            edit = @Edit(title = "身份鉴别失败次数", notNull = true,
                    numberType = @NumberType(min = 2, max = 10))
    )
    private Integer authenticationFailureCount;

    // 账号解锁方式
    @FabosJsonField(
            views = @View(title = "账号解锁方式"),
            edit = @Edit(title = "账号解锁方式", notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {UnlockMethodEnum.ChoiceFetch.class}))
    )
    private String accountUnlockMethod;

    // 账号自动解锁时间（分钟） ，如果解锁方式为手动，则这里的值不起作用
    @FabosJsonField(
            views = @View(title = "账号自动解锁时间（分钟）"),
            edit = @Edit(title = "账号自动解锁时间（分钟）", notNull = true,
                    numberType = @NumberType(min = 1, max = 7 * 24 * 60))
    )
    private Integer accountAutoUnlockMinutes;

    // 密码最小长度
    @FabosJsonField(
            views = @View(title = "密码最小长度"),
            edit = @Edit(title = "密码最小长度", notNull = true,
                    numberType = @NumberType(min = 6, max = 60))
    )
    private Integer passwordMinLength;

    // 密码组合复杂度
    @FabosJsonField(
            views = @View(title = "密码组合复杂度"),
            edit = @Edit(title = "密码组合复杂度", notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {PasswordComplexityEnum.ChoiceFetch.class}))
    )
    private String passwordComplexity;

    @Transient
    public Integer getAccountAutoUnlockSeconds(){
        return accountAutoUnlockMinutes * 60;
    }
    @Transient
    public Integer getPasswordValiditySeconds() {
        return passwordValidityDays * 24 * 60 * 60;
    }
    @Transient
    public Integer getTimeoutLogoutSeconds() {
        return timeoutLogoutMinutes * 60;
    }

}
