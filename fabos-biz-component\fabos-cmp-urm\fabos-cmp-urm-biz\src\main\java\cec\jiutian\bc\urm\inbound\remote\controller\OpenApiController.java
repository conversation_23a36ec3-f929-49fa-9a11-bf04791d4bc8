package cec.jiutian.bc.urm.inbound.remote.controller;

import cec.jiutian.bc.urm.inbound.local.service.command.OpenApiService;
import cec.jiutian.core.frame.module.R;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import static cec.jiutian.core.frame.constant.FabosJsonRestPath.FABOS_OPEN_API;

@RestController
@RequestMapping(FABOS_OPEN_API)
@Slf4j
@AllArgsConstructor
public class OpenApiController {
    private final OpenApiService openApiService;

    /**
     * 获取token, 每个 appid 同一时间只有一个 token 有效
     *
     * @param appid  appid
     * @param secret secret
     * @return token
     */
    @GetMapping("/requestToken")
    @Transactional
    public R<Map<String, String>> createToken(@RequestParam("appid") String appid, @RequestParam("secret") String secret) {
        return R.ok(openApiService.requestNewToken(appid, secret));
    }

}
