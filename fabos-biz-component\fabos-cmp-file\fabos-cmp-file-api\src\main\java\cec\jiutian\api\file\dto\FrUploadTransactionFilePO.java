package cec.jiutian.api.file.dto;

/**
 * <AUTHOR>
 * @time 2025-05-12 17:29
 */


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

@Data
@Entity
@NoArgsConstructor
public class FrUploadTransactionFilePO implements Serializable {
    private String uuid;
    private String fileName;
    private String fileSourceCode;
    private String fileUrl;
    private String fileTypeCode;
    private Double fileSizeNumber;
    /**
     * 这里的eTag已经未使用，此外还有几个字段没使用，需细看
     */
    @Deprecated
    private String fileTagIdentifier;
    private String transactionCompleteFlag = "N";
    private String transactionIdentifier;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Asia/Shanghai")
    private Date createTime;
    private String createUser;
    private String lastEventName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Asia/Shanghai")
    private Date lastEventTime;
    private String lastEventUser;
    private String lastEventComment;


}