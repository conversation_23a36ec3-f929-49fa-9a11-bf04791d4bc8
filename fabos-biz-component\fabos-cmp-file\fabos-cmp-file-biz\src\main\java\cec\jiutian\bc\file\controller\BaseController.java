package cec.jiutian.bc.file.controller;

/**
 * <AUTHOR>
 * @time 2025-04-28 16:12
 */

import com.alibaba.fastjson.JSONObject;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import jakarta.validation.Validator;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.util.ContentCachingRequestWrapper;

import java.io.Serializable;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 此 Base Controller 和其内部类/方法仅供老框架兼容文件接口使用
 */
@Component
public class BaseController {

    @Autowired
    protected Validator validator;
    protected HttpServletRequest request;
    protected HttpServletResponse response;
    protected HttpSession session;

    /*
     * @Description 返回操作成功后的数据
     * @Date 9:14 2022-3-28
     * @Param [input, output, msg, path, lastEventUser, lastEventComment]
     * @return javax.ws.rs.core.Response
     **/
    protected Response respSuccessResult(Object output, String message) {
        ErrorCodeResult errorCodeResult = new ErrorCodeResult();
        errorCodeResult.setCode("000");
        errorCodeResult.setMessage(message);
        FillerUtil.fillErrorCodeResult(errorCodeResult);
        Response baseResponse = new Response();
        baseResponse.setStatus(HttpStatus.OK.value());
        baseResponse.setEntity(errorCodeResult);
        FillerUtil.setOutput(baseResponse, output);
        return baseResponse;
    }

    protected Response respSuccessResult(String message) {
        return respSuccessResult(null, message);
    }

    /*
     * @Description 返回操作失败后的数据
     * @Date 9:14 2022-3-28
     * @Param [input, output, msg, path, lastEventUser, lastEventComment]
     * @return javax.ws.rs.core.Response
     **/
    protected Response respFaultResult(Object output, String message) {
        ErrorCodeResult errorCodeResult = new ErrorCodeResult();
        errorCodeResult.setCode("111");
        errorCodeResult.setMessage(message);
        FillerUtil.fillErrorCodeResult(errorCodeResult);
        Response baseResponse = new Response();
        baseResponse.setStatus(HttpStatus.SERVICE_UNAVAILABLE.value());
        baseResponse.setEntity(errorCodeResult);
        FillerUtil.setOutput(baseResponse, output);
        return baseResponse;
    }

    protected Response respFaultResult(String message) {
        return respFaultResult(null, message);
    }

    protected Response respResult(boolean result, Object output, String successMsg, String faultMsg) {
        return result ? this.respSuccessResult(output, successMsg)
                : this.respFaultResult(output, faultMsg);
    }

    protected Response respResult(boolean result, String successMsg, String faultMsg) {
        return respResult(result, null, successMsg, faultMsg);
    }

    @Data
    public static class Response implements Serializable {
        Object output;
//        Pager pager;
        Object pager;
        int status;
        Object entity;
    }

    @Data
    public static class ErrorCodeResult implements Serializable {

        /**
         * 默认值为"000"，成功状态
         */
        private String code = "000";

        /**
         * 中英message
         */
        private String message;

        /**
         * 错误明细
         */
        private String detail;

        /**
         * 行为
         */
        private String action;

        @Override
        public String toString() {
            return ToStringBuilder.reflectionToString(this);
        }
    }

    public static class FillerUtil {

        // 定义填充规则
        private static String FILLER_RULE = "\\{\\{(.*?)\\}\\}";

        public static boolean hasMatches(String regix, String str) {
            Pattern pattern = Pattern.compile(regix);
            Matcher m = pattern.matcher(str);
            return m.find();
        }

        public static String fill(String str, List<String> fillList) {
            if (null == str) {
                str = "";
            }
            Pattern pattern = Pattern.compile(FILLER_RULE);

            Matcher matcher = pattern.matcher(str);
            int index = 0;
            while (matcher.find()) {
                str = str.replaceFirst(FILLER_RULE, getItem(fillList, index++));
            }
            return str;
        }

        private static String getItem(List<String> list, int index) {
            if (null == list || list.isEmpty() || index < 0 || index >= list.size()) {
                return "";
            } else {
                return getObject(list.get(index));
            }
        }

        /**
         * 经前端协定： Object 为null时，则返回 js的空实体即："{}" String 为null时，则返回js的空字符串：""
         */
        private static Object getObject(Object obj) {
            if (null == obj) {
                return "{}";
            } else {
                return obj;
            }
        }

        private static String getObject(String obj) {
            if (null == obj) {
                return "";
            } else {
                return obj;
            }
        }

        public static void fillErrorCodeResult(ErrorCodeResult errorCodeResult) {
            if (null == errorCodeResult) {
                return;
            } else {
                errorCodeResult.setCode(getObject(errorCodeResult.getCode()));
//            errorCodeResult.setOutput(getObject(errorCodeResult.getOutput()));
                errorCodeResult.setMessage(getObject(errorCodeResult.getMessage()));
                errorCodeResult.setDetail(getObject(errorCodeResult.getDetail()));
            }
        }

        public static void fillErrorCodeResult(ErrorCodeResult errorCodeResult, HttpServletRequest req) {
            if (null == req || null == errorCodeResult) {
                return;
            } else {
                // 获取路径
//            errorCodeResult.setPath(req.getServletPath());
                // 获取输入
                if (req != null && req instanceof ContentCachingRequestWrapper) {
                    ContentCachingRequestWrapper wrapper = (ContentCachingRequestWrapper) req;
                    String input = StringUtils.toEncodedString(wrapper.getContentAsByteArray(),
                            Charset.forName(wrapper.getCharacterEncoding()));
//                errorCodeResult.setInput(input);
                }
            }
        }

        public static List<JSONObject> filtrationRow(JSONObject filter, List<JSONObject> jobList) {
            if (null == filter) {
                return jobList;
            }
            Set<String> keys = filter.keySet();
            //遍历filter元素
            for (String key : keys) {
                Object valueObj = filter.get(key);
                // 1: value为null这过滤为null的
                if (null == valueObj) {
                    jobList.removeIf(job -> null != job.get(key));
                } else if (valueObj instanceof ArrayList) {//2:value为array则批量过滤
                    List<Object> values = (List) filter.get(key);
                    List<String> valuesStr = values.stream().map(x -> String.valueOf(x)).collect(Collectors.toList());
                    //遍历行 匹配行中是当前 filter元素的key  的值，在value中，不是则删除
                    jobList.removeIf(job -> !valuesStr.contains(String.valueOf(job.get(key))));
                } else {//3:value为string 或 number则普通过滤
                    jobList.removeIf(job -> !String.valueOf(valueObj).equals(String.valueOf(job.get(key))));
                }

            }
            return jobList;
        }

        public static List<JSONObject> filtrationColumn(List<String> columns, List<JSONObject> jobList) {
            if (CollectionUtils.isEmpty(columns)) {
                return jobList;
            }
            List<JSONObject> result = new ArrayList<>();
            for (JSONObject job : jobList) {
                JSONObject resJOb = new JSONObject();
                for (String column : columns) {
                    if (null != job.get(column)) {
                        resJOb.put(column, job.get(column));
                    }
                }
                result.add(resJOb);
            }
            return result;
        }

        public static void setOutput(Response baseResponse, Object output) {
            if (null == baseResponse || null == output) {
                return;
            }
//            if (output instanceof PageInfo) {
//                PageInfo page = (PageInfo) output;
//                baseResponse.setOutput(page.getList());
//                Pager pager = new Pager();
//                pager.setPageNum(page.getPageNum());
//                pager.setPageSize(page.getPageSize());
//                pager.setTotal(page.getTotal());
//                baseResponse.setPager(pager);
//            } else {
            // 这里只供文件接口使用，故只会出下面的情况
                baseResponse.setOutput(output);
                baseResponse.setPager(null);
//            }
        }
    }


}
