//package cec.jiutian.bc.ecs.domain.extendFabosJsonTest;
//
//import cec.jiutian.core.frame.constant.YesOrNoStatus;
//import cec.jiutian.core.frame.module.BaseModel;
//import cec.jiutian.core.view.fabosJson.FabosJson;
//import cec.jiutian.view.FabosJsonField;
//import cec.jiutian.view.expr.ExprBool;
//import cec.jiutian.view.field.Edit;
//import cec.jiutian.view.field.EditType;
//import cec.jiutian.view.field.View;
//import cec.jiutian.view.field.edit.ChoiceType;
//import cec.jiutian.view.field.edit.Search;
//import cec.jiutian.view.type.Power;
//import cec.jiutian.view.type.RowOperation;
//import jakarta.persistence.Entity;
//import jakarta.persistence.Table;
//import lombok.Getter;
//import lombok.Setter;
//import lombok.experimental.Accessors;
//
//@FabosJson(name = "动物",
//        orderBy = "Animal.createTime desc",
//        power = @Power(add = false, edit = false, delete = false, export = true, importable = false, viewDetails = false)
//)
//@Setter
//@Getter
//@Entity
//@Accessors(chain = true)
//@Table(name = "Animal")
//public class AnimalView extends BaseModel {
//
//    @FabosJsonField(
//            views = @View(title = "名称"),
//            edit = @Edit(title = "名称", search = @Search(vague = true))
//    )
//    private String name;
//
//    @FabosJsonField(
//            views = @View(title = "年龄"),
//            edit = @Edit(title = "年龄", search = @Search(vague = true))
//    )
//    private Integer age;
//
//    @FabosJsonField(
//            views = @View(title = "状态"),
//            edit = @Edit(title = "状态",
//                    type = EditType.CHOICE,
//                    choiceType = @ChoiceType(fetchHandler = {YesOrNoStatus.ChoiceFetch.class}),
//                    search = @Search(vague = true))
//    )
//    private String state;
//}
