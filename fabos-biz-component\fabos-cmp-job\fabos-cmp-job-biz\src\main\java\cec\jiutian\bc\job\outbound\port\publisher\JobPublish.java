package cec.jiutian.bc.job.outbound.port.publisher;

import cec.jiutian.functionexecutor.consumer.DubboGenericAPI;
import cec.jiutian.functionexecutor.dto.InvokeDTO;
import cec.jiutian.bc.job.domain.job.entity.JobInfo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class JobPublish {

    @Resource
    private DubboGenericAPI dubboGenericAPI;

    public Object execute(JobInfo jobInfo){
        InvokeDTO invokeDTO = new InvokeDTO();
        Object[] args = new Object[2];
        args[0] = jobInfo.getCode();
        args[1] = jobInfo.getHandlerParam();
        invokeDTO.setArgs(args);
        invokeDTO.setGroup("fabos-boot");
        String[] argsType = {"java.lang.String","java.lang.String"};
        invokeDTO.setArgsType(argsType);
        invokeDTO.setBizName("fabos-boot");
        invokeDTO.setClassFullName(jobInfo.getHandler());
        invokeDTO.setMethodName("exec");
        invokeDTO.setModuleVersion("3.2.2-SNAPSHOT");
        return dubboGenericAPI.doInvoke(invokeDTO);
    }
}
