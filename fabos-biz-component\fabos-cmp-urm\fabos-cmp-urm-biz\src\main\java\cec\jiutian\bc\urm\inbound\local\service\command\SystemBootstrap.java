package cec.jiutian.bc.urm.inbound.local.service.command;

import cec.jiutian.bc.infra.utils.JwkUtils;
import cec.jiutian.bc.urm.inbound.local.config.SystemConfig;
import cec.jiutian.bc.urm.inbound.local.context.SecurityConfigContext;
import cec.jiutian.bc.urm.inbound.local.service.command.init.ComponentInitService;
import cec.jiutian.bc.urm.inbound.local.service.command.init.RoleMenuService;
import cec.jiutian.bc.urm.inbound.local.service.command.init.RoleUserInitService;
import cec.jiutian.bc.urm.inbound.local.service.command.init.SystemConfigInitService;
import cec.jiutian.bc.urm.inbound.remote.provider.Oauth2PublicKeyProvider;
import cec.jiutian.bc.urm.outbound.port.client.UbpFeignClient;
import cec.jiutian.core.prop.FabosJsonProp;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.security.interfaces.RSAPublicKey;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 系统初始化
 */
@Component
@Slf4j
public class SystemBootstrap implements ApplicationRunner {

    @Resource
    private SystemConfig systemConfig;

    @Resource
    private RoleUserInitService roleUserInitService;
    @Resource
    private ComponentInitService componentInitService;
    @Resource
    private RoleMenuService roleMenuService;
    @Resource
    private SystemConfigInitService systemConfigInitService;
    @Resource
    private UbpFeignClient ubpFeignClient;
    // 如果没有实现，则会为null
    @Autowired(required = false)
    private Oauth2PublicKeyProvider publicKeyProvider;
    @Resource
    private FabosJsonProp fabosJsonProp;
    @Value("${spring.profiles.active}")
    public List<String> activatedProfiles = null;
    // 需要对access验签的spring files
    private static final List<String> profilesNeedValidateSign = Arrays.asList("fat","uat","pro","prod");


    /**
     * 1. 创建超级管理员账号  无角色
     * 2. 创建组件的菜单
     * 2. 创建三员角色并分配对应菜单权限
     * @param args incoming application arguments
     */
    @Override
    public void run(ApplicationArguments args){
        long startTime = System.currentTimeMillis();
        systemConfigInitService.initSystemConfig();
//        if (RightManagementPolicyEnum.SUPER.getValue().equals(systemConfig.getSystemMode())) {
//            log.info("超级管理员模式，系统初始化开始------");
//            // 执行超级管理员模式初始化
//            doSuperUserInit();
//        } else if (RightManagementPolicyEnum.TRIPARTITE.getValue().equals(systemConfig.getSystemMode())) {
//            log.info("三员模式，系统初始化开始------");
//            // 执行三员模式初始化
//            doThrRMInit();
//        } else {
//            throw new FabosJsonApiErrorTip("系统初始化失败，系统模式配置错误");
//        }
        //初始化超级管理员账号  无角色和权限  默认拥有所有权限
        roleUserInitService.initSuperUser(null);
        //初始化所有菜单和创建三员角色
        doThrRMInit();
        //初始化菜单
        // 初始化ubp 公钥
        if (needVerifySign()) {
            performJwkInitialization();
        }
        log.info("系统初始化完成---------，一共耗时：{}秒", (System.currentTimeMillis() - startTime) / 1000);
    }

    public boolean needVerifySign() {
        if (!fabosJsonProp.isVerifySign()) {
            return false;
        }
        if (CollectionUtils.isNotEmpty(activatedProfiles)) {
            for (String profile : activatedProfiles) {
                if (profilesNeedValidateSign.contains(profile)) {
                    return true;
                }
            }
        }
        return false;
    }

    public RSAPublicKey performJwkInitialization() {
        RSAPublicKey publicKey = null;
        if (Objects.nonNull(publicKeyProvider)) {
            // 如果当前应用是ubp，这里的provider将不空
            publicKey = publicKeyProvider.getPublicKey();
        }
        if (Objects.isNull(publicKey)) {
            // 如果当前应用不是ubp，则调用feign接口获取
            try {
                String jwk = ubpFeignClient.fetchJwks();
                publicKey = JwkUtils.parseJwkJsonToPublicKey(jwk);
            } catch (Exception ignored) {
                log.warn("暂未从UBP获取到JWK，可能是UBP未完成启动，将尝试在验证时重新获取");
            }
        }
        SecurityConfigContext.setOauth2PublicKey(publicKey);
        return publicKey;
    }


    /**
     * 超级管理员初始化流程
     * 1.初始化菜单
     * 2.初始化角色并绑定菜单
     * 3.初始化用户并绑定角色
     */
    private void doSuperUserInit() {
        componentInitService.initAllMenu();
        roleUserInitService.initSuper(roleMenuService.getSuperUserMenuList());

    }

    /**
     * 三员模式初始化
     */
    private void doThrRMInit() {
        componentInitService.initAllMenu();
        roleUserInitService.init3Rm(roleMenuService.getSystemMenuList(),roleMenuService.getAuditMenuList(),roleMenuService.getSecurityMenuList());
    }
}
