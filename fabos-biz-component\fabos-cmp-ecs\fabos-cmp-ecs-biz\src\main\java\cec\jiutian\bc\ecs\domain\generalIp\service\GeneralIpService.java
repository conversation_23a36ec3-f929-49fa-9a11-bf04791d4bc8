package cec.jiutian.bc.ecs.domain.generalIp.service;

import cec.jiutian.bc.ecs.domain.generalIp.entity.GeneralIp;
import cec.jiutian.common.exception.MesErrorCodeException;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.bc.infra.util.AesUtil;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;

@Slf4j
@Service
public class GeneralIpService {

    @Value("${aes.key}")
    private String aesKey;

    private final FabosJsonDao fabosJsonDao;

    public GeneralIpService(FabosJsonDao fabosJsonDao) {
        this.fabosJsonDao = fabosJsonDao;
    }

    /**
     * @param system  系统名称
     * @param authKey 密钥
     */
    public void validateSystem(String system, String authKey) {
        if (StringUtils.isEmpty(system) || StringUtils.isEmpty(authKey)) {
            throw new MesErrorCodeException("system或authKey为空，请重试");
        }
        String decrypt = AesUtil.decrypt(authKey, aesKey);
        if (StringUtils.isEmpty(decrypt)) {
            throw new MesErrorCodeException("authKey解析失败，请重试");
        }
        List<GeneralIp> generalIpList = fabosJsonDao.queryEntityList(GeneralIp.class, "sysName = :sysName and status = '启用'");
        if (CollectionUtils.isEmpty(generalIpList)) {
            throw new MesErrorCodeException("该系统未配置或已禁用，请重试");
        }
        GeneralIp generalIp = generalIpList.get(0);
        if (!authKey.equals(generalIp.getAuthKey()) || authKey.split("-")[0].equals(system)) {
            throw new MesErrorCodeException("authKey无效，请重试");
        }
    }

    public HashMap<String, Object> getGeneralIpList(int pageNum, int pageSize) {
        EntityManager entityManager = fabosJsonDao.getEntityManager();
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<GeneralIp> cq = cb.createQuery(GeneralIp.class);
        Root<GeneralIp> generalIpRoot = cq.from(GeneralIp.class);
        cq.orderBy(cb.desc(generalIpRoot.get("createTime")));
        cq.select(generalIpRoot);
        TypedQuery<GeneralIp> query = entityManager.createQuery(cq);
        query.setFirstResult((pageNum - 1) * pageSize); // 设置起始位置
        query.setMaxResults(pageSize); // 设置每页大小

        CriteriaBuilder cbc = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> countCq = cb.createQuery(Long.class);
        Root<GeneralIp> gIpRoot = countCq.from(GeneralIp.class);
        // 如果有筛选条件，这里应该添加相应的 Predicate
        countCq.select(cbc.count(gIpRoot));
        TypedQuery<Long> countQuery = entityManager.createQuery(countCq);

        HashMap<String, Object> res = new HashMap<>();
        res.put("list", query.getResultList());
        res.put("page", pageNum);
        res.put("perPage", pageSize);
        res.put("total", countQuery.getSingleResult());
        return res;
    }
}
