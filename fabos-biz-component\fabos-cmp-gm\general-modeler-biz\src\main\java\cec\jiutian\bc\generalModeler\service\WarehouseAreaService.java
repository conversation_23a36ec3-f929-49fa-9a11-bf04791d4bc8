package cec.jiutian.bc.generalModeler.service;


import cec.jiutian.bc.generalModeler.domain.warehouseArea.service.WarehouseDomainService;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.view.fabosJson.view.FabosJsonApiModel;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service
@Component
public class WarehouseAreaService {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private WarehouseDomainService warehouseDomainService;

    public FabosJsonApiModel warehouseHold(String warehouseId) {
        if (warehouseDomainService.hold(warehouseId)) {
            return FabosJsonApiModel.successApi();
        } else {
            return FabosJsonApiModel.errorApi("操作失败");
        }
    }

    public FabosJsonApiModel warehouseUnHold(String warehouseId) {
        if (warehouseDomainService.unHold(warehouseId)) {
            return FabosJsonApiModel.successApi();
        } else {
            return FabosJsonApiModel.errorApi("操作失败");
        }
    }
}
