package cec.jiutian.bc;

import cec.jiutian.core.frame.annotation.FabosJsonScan;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.core.io.support.PropertiesLoaderUtils;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.io.IOException;
import java.util.Properties;

/**
 * <AUTHOR>
 * @description:
 */
@Slf4j
@SpringBootApplication(scanBasePackages = {
        "cec.jiutian"
})
@ComponentScan(basePackages = {
        "cec.jiutian"
})
@FabosJsonScan(value = {
        "cec.jiutian"
})
//@EnableJpaRepositories(basePackages = "cec.jiutian")
@EntityScan(basePackages = {
        "cec.jiutian"
})
//@EnableDubbo
public class CmpEcsApp {
    private static final Logger LOGGER = LoggerFactory.getLogger(CmpEcsApp.class);

    public static void main(String[] args) throws IOException {
        try {
            Properties properties = PropertiesLoaderUtils.loadAllProperties("application.properties");
            properties.forEach((k, v) -> MDC.put(k.toString(), v.toString()));
            ClassLoader systemClassLoader = ClassLoader.getSystemClassLoader();
            ClassLoader classLoader = CmpEcsApp.class.getClassLoader();
            System.out.println(classLoader.equals(systemClassLoader));
            System.out.println("类加载器名称:" + classLoader.getName());
            SpringApplication.run(CmpEcsApp.class, args);

            LOGGER.info("BaseApplication start!");
            LOGGER.info("Spring Boot Version: "
                    + SpringApplication.class.getPackage().getImplementationVersion());
            LOGGER.info("BaseApplication classLoader: " + CmpEcsApp.class.getClassLoader());
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }


}
