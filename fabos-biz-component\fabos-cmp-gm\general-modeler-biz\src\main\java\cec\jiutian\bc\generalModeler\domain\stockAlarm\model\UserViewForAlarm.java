package cec.jiutian.bc.generalModeler.domain.stockAlarm.model;

import cec.jiutian.bc.generalModeler.domain.stockAlarm.proxy.UserViewForAlarmDataProxy;
import cec.jiutian.bc.urm.domain.dictionary.handler.DictChoiceFetchHandler;
import cec.jiutian.bc.urm.domain.org.entity.Org;
import cec.jiutian.bc.urm.domain.position.entity.Position;
import cec.jiutian.core.data.annotation.LinkTable;
import cec.jiutian.core.frame.constant.SwitchStatus;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.ReferenceTreeType;
import cec.jiutian.view.SubTableField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@LinkTable
@Table(name = "fd_user")
@Getter
@Setter
@FabosJsonI18n
@FabosJson(name = "用户管理",
        orderBy = "UserViewForAlarm.createTime desc",
        dataProxy = UserViewForAlarmDataProxy.class
)
public class UserViewForAlarm extends MetaModel {
    //    用户ID、工号、部门、姓名、性别、岗位，筛选有效状态的用户数据
    @FabosJsonField(
            views = @View(title = "工号"),
            edit = @Edit(title = "工号", notNull = true, search = @Search(vague = true))
    )
    @SubTableField
    private String employeeNumber;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "所属部门", column = "name", index = 3),
            edit = @Edit(title = "所属部门", notNull = true, type = EditType.REFERENCE_TABLE,
                    referenceTreeType = @ReferenceTreeType(pid = "parentOrg.id"), search = @Search())
    )
    @JoinColumn(foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    private Org org;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "所属岗位", column = "name"),
            edit = @Edit(title = "所属岗位", notNull = true, type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType, search = @Search())
    )
    @JoinColumn(foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    private Position position;

    @FabosJsonField(
            views = @View(title = "姓名"),
            edit = @Edit(title = "姓名", search = @Search(vague = true), notNull = true)
    )
    @SubTableField
    private String name;

    @FabosJsonField(
            views = @View(title = "性别"),
            edit = @Edit(title = "性别", search = @Search,
//                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                            fetchHandlerParams = "RYMD003")
            )
    )
    private String gender;


    @FabosJsonField(
            views = @View(title = "状态", show = false),
            edit = @Edit(title = "状态",
                    show = false,
                    defaultVal = "Y",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {SwitchStatus.ChoiceFetch.class}))
    )
    private String state;


}
