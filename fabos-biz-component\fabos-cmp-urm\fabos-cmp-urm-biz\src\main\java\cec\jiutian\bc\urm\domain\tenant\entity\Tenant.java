package cec.jiutian.bc.urm.domain.tenant.entity;

import cec.jiutian.bc.urm.domain.tenant.constant.RightManagementPolicyEnum;
import cec.jiutian.bc.urm.domain.tenant.event.TenantDataProxy;
import cec.jiutian.core.frame.constant.RegexConst;
import cec.jiutian.core.frame.constant.SwitchStatus;
import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.frame.module.FabModule;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DateType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Filter;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2024-05-28 17:22
 */

@Table(name = "fd_tenant")
@Entity
@Getter
@Setter
@FabosJsonI18n
@FabosJson(name = "租户管理", orderBy = "Tenant.createTime desc", dataProxy = {TenantDataProxy.class}, power = @Power(delete = false, export = false, importable = false))
public class Tenant extends BaseModel {

    @FabosJsonField(
            views = @View(title = "名称"),
            edit = @Edit(title = "名称", search = @Search(vague = true), notNull = true)
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态",
                    type = EditType.CHOICE,
                    notNull = true,
                    defaultVal = "Y",
                    choiceType = @ChoiceType(
                            fetchHandler = {SwitchStatus.ChoiceFetch.class}
                    ), search = @Search(defaultVal = "Y"))
    )
    private String state;

    @FabosJsonField(
            views = @View(title = "生效日期", type = ViewType.text),
            edit = @Edit(title = "生效日期", dateType = @DateType(type = DateType.Type.DATE), search = @Search)
    )
    private LocalDate effectDate;

    @FabosJsonField(
            views = @View(title = "有效期", type = ViewType.text),
            edit = @Edit(title = "有效期", dateType = @DateType(type = DateType.Type.DATE), search = @Search)
    )
    private LocalDate expireDate;

    @FabosJsonField(
            views = @View(title = "企业"),
            edit = @Edit(title = "企业", search = @Search)
    )
    private String enterprise;

    @FabosJsonField(
            views = @View(title = "权限管控模式"),
            edit = @Edit(title = "权限管控模式",
                    type = EditType.CHOICE,
                    notNull = true,
                    defaultVal = "superUser",
                    choiceType = @ChoiceType(
                            fetchHandler = {RightManagementPolicyEnum.ChoiceFetch.class}
                    ),readonly = @Readonly(add = false))
    )
    private String rightManagementPolicy;

    @FabosJsonField(
            views = @View(title = "超级管理员电话号码", show = false),
            edit = @Edit(title = "超级管理员电话号码", search = @Search(vague = true), inputType = @InputType(regex = RegexConst.PHONE_REGEX)
                    , dependFieldDisplay = @DependFieldDisplay(notNull = "rightManagementPolicy=='superUser'", showOrHide = "rightManagementPolicy=='superUser'"), readonly = @Readonly(add = false)))
    private String superPhoneNumber;

    @FabosJsonField(
            views = @View(title = "审计管理员电话号码", show = false),
            edit = @Edit(title = "审计管理员电话号码", search = @Search(vague = true), inputType = @InputType(regex = RegexConst.PHONE_REGEX)
                    , dependFieldDisplay = @DependFieldDisplay(notNull = "rightManagementPolicy=='3rm'", showOrHide = "rightManagementPolicy=='3rm'"), readonly = @Readonly(add = false)))
    private String auditPhoneNumber;

    @FabosJsonField(
            views = @View(title = "安全保密管理员电话号码", show = false),
            edit = @Edit(title = "安全保密管理员电话号码", search = @Search(vague = true), inputType = @InputType(regex = RegexConst.PHONE_REGEX)
                    , dependFieldDisplay = @DependFieldDisplay(notNull = "rightManagementPolicy=='3rm'", showOrHide = "rightManagementPolicy=='3rm'"), readonly = @Readonly(add = false)))
    private String securityPhoneNumber;

    @FabosJsonField(
            views = @View(title = "系统管理员电话号码", show = false),
            edit = @Edit(title = "系统管理员电话号码", search = @Search(vague = true), inputType = @InputType(regex = RegexConst.PHONE_REGEX)
                    , dependFieldDisplay = @DependFieldDisplay(notNull = "rightManagementPolicy=='3rm'", showOrHide = "rightManagementPolicy=='3rm'"), readonly = @Readonly(add = false)))
    private String systemPhoneNumber;

    @FabosJsonField(
            views = @View(title = "电子邮件", show = false),
            edit = @Edit(title = "电子邮件", search = @Search(vague = true), notNull = true, inputType = @InputType(regex = RegexConst.EMAIL_REGEX))
    )
    private String emailAddress;

    @FabosJsonField(
            views = @View(title = "企业图标"),
            edit = @Edit(title = "企业图标", type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(fileTypes = {".jpg,.png"}, size = 5120,type = AttachmentType.Type.IMAGE))
    )
    private String logo;

    @ManyToMany
    @JoinTable(
            name = "fd_tenant_module",
            joinColumns = @JoinColumn(name = "tenant_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "module_id", referencedColumnName = "id"))
    @FabosJsonField(
            views = @View(title = "已授权组件"),
            edit = @Edit(title = "已授权组件",
                    type = EditType.TAB_TABLE_REFER,
                    filter = @Filter(value = "FabModule.state = 'activated' and FabModule.name != 'fabos-base-starter'"),
                    referenceTableType = @ReferenceTableType)
    )
    private List<FabModule> authorizedModule;

}
