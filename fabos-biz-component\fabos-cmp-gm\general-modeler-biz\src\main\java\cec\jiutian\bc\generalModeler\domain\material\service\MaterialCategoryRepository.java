package cec.jiutian.bc.generalModeler.domain.material.service;

import cec.jiutian.bc.generalModeler.domain.material.model.MaterialCategory;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface MaterialCategoryRepository extends JpaRepository<MaterialCategory, String> {

    @Transactional
    @Modifying
    @Query(value = """ 
                                update MATERIAL_CATEGORY set MATERIAL_ATTRIBUTE_TEMPLATE_ID = :templateId
                                where id in
                                (
                                SELECT id
                                FROM MATERIAL_CATEGORY
                                START WITH id =  :id
            CONNECT BY PRIOR id = PARENT_CATEGORY_ID)""", nativeQuery = true
    )
    int updateWithAllChid(@Param("id") String categoryId, @Param("templateId") String templateId);
}
