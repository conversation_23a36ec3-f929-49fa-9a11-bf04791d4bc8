package cec.jiutian.bc.urm.domain.menu.service;

import cec.jiutian.bc.infra.utils.UPMSUtil;
import cec.jiutian.bc.urm.domain.menu.constant.FabosMenuConst;
import cec.jiutian.bc.urm.domain.menu.entity.JumpMenuArgAddView;
import cec.jiutian.bc.urm.domain.menu.entity.JumpMenuView;
import cec.jiutian.bc.urm.domain.menu.entity.Menu;
import cec.jiutian.bc.urm.domain.menu.entity.ModelJumpArg;
import cec.jiutian.bc.urm.domain.menu.service.query.MenuQueryDTO;
import cec.jiutian.bc.urm.domain.role.entity.Role;
import cec.jiutian.bc.urm.domain.role.service.RoleRepository;
import cec.jiutian.bc.urm.dto.JumpMenuDTO;
import cec.jiutian.bc.urm.dto.MenuDTO;
import cec.jiutian.bc.urm.dto.MenuData;
import cec.jiutian.bc.urm.dto.MetaMenu;
import cec.jiutian.bc.urm.dto.ModelJumpArgDTO;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.constant.ButtonJumpTypeEnum;
import cec.jiutian.core.frame.constant.FabosFunPermissions;
import cec.jiutian.core.frame.constant.MenuPageTypeEnum;
import cec.jiutian.core.frame.constant.MenuStatus;
import cec.jiutian.core.frame.constant.MenuTypeEnum;
import cec.jiutian.core.frame.context.MetaContext;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.frame.service.FabosJsonCoreService;
import cec.jiutian.core.view.fabosJson.view.FabosJsonModel;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.data.redis.utils.BaseRedisCacheUtil;
import cec.jiutian.meta.model.MetadataComponent;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.criteria.Predicate;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MenuService {
    private final RoleRepository roleRepository;

    private final FabosJsonDao fabosJsonDao;
    private final JpaCrud jpaCrud;
    private final MenuRepository menuRepository;

    @Value("${spring.application.name}")
    public String currentService = "";

    public MenuService(FabosJsonDao fabosJsonDao, JpaCrud jpaCrud, MenuRepository menuRepository,
                       RoleRepository roleRepository) {
        this.fabosJsonDao = fabosJsonDao;
        this.jpaCrud = jpaCrud;
        this.menuRepository = menuRepository;
        this.roleRepository = roleRepository;
    }

    public String getModuleUrlById(MenuQueryDTO queryDTO) {
        Menu menu = jpaCrud.getById(Menu.class, queryDTO.getId());
        String url = getFullUrl(menu, menu.getName());
        return "/" + url;
    }


    /**
     * 废弃，FabosMenuConst.MENU_VIEW_LIST 设计不合理。
     */
    @Deprecated
    public List<MenuData> getAllMenuList() {
        List<MenuData> list = BaseRedisCacheUtil.getList(FabosMenuConst.MENU_VIEW_LIST, MenuData.class);
        log.debug("getAllMenuList list={}", list);
        if (!CollectionUtils.isEmpty(list)) {
            return list;
        }
        list = new ArrayList<>();
        List<Menu> menuList = getRootMenuList();
        for (Menu menu : menuList) {
            MenuData menuData = convertMenuData(menu);
            buildMenuTree(menu, menuData);
            list.add(menuData);
        }
        log.debug("getAllMenuList set list={}", list);
        BaseRedisCacheUtil.set(FabosMenuConst.MENU_VIEW_LIST, list, 3600L);
        return list;
    }

    @Transactional
    public void buildMenuTree(Menu parentMenu, MenuData parentMenuData) {
        List<Menu> menuList = getModuleListByParentId(parentMenu.getId());
        if (CollectionUtils.isEmpty(menuList)) {
            return;
        }
        ArrayList<MenuData> childList = new ArrayList<>();
        for (Menu menu : menuList) {
            MenuData parentData = convertMenuData(menu);
            buildMenuTree(menu, parentData);
            childList.add(parentData);
        }
        parentMenuData.setChildren(childList);

    }

    public MenuData convertMenuData(Menu menu) {
        MenuData data = new MenuData();
        copyBasicProperties(menu, data);
        setRouterModule(menu, data);
        setJumpArgs(menu, data);
        data.setHide(menu.getHideFlag());
        return data;
    }

    private void copyBasicProperties(Menu menu, MenuData data) {
        data.setId(menu.getId());
        data.setName(menu.getName());
        data.setCode(menu.getModuleCode());
        data.setValue(menu.getModuleValue());
        data.setSort(menu.getSequenceNumber());
        data.setType(menu.getModuleTypeCode());
        data.setIcon(menu.getModuleIconText());
        data.setPageId(menu.getPageId());
        data.setChildren(new ArrayList<>());
        data.setSubType(menu.getSubType());
        data.setHref(menu.getHref());
        data.setJumpType(menu.getJumpType());
        data.setTerminalType(menu.getTerminalType());
        data.setButtonStyle(menu.getButtonStyle());
        data.setOnlyShowIcon(menu.getOnlyShowIcon());
        data.setShowType(menu.getShowType());
        data.setButtonType(menu.getButtonType());
        data.setRouterType(menu.getRouterType());
        data.setPageType(menu.getPageType());
    }

    private void setRouterModule(Menu menu, MenuData data) {
        if (menu.getRouterModule() != null) {
            JumpMenuView routerModule = menu.getRouterModule();
            JumpMenuDTO jumpMenuDTO = new JumpMenuDTO();
            jumpMenuDTO.setModuleValue(routerModule.getModuleValue())
                    .setModuleCode(routerModule.getModuleCode())
                    .setName(routerModule.getName())
                    .setModuleTypeCode(routerModule.getModuleTypeCode());
            data.setRouterModule(jumpMenuDTO);
        }
    }

    private void setJumpArgs(Menu menu, MenuData data) {
        if (isButtonModelJump(menu)) {
            JumpMenuArgAddView menuArgAddView = fabosJsonDao.findById(JumpMenuArgAddView.class, menu.getId());
            if (CollectionUtils.isNotEmpty(menuArgAddView.getJumpArgs())) {
                List<ModelJumpArg> jumpArgs = menuArgAddView.getJumpArgs();
                List<ModelJumpArgDTO> modelJumpArgDTOS = jumpArgs.stream()
                        .map(jumpArg -> {
                            ModelJumpArgDTO modelJumpArgDTO = new ModelJumpArgDTO();
                            BeanUtils.copyProperties(jumpArg, modelJumpArgDTO);
                            return modelJumpArgDTO;
                        })
                        .collect(Collectors.toList());
                data.setJumpArgs(modelJumpArgDTOS);
            }
        }
    }

    private boolean isButtonModelJump(Menu menu) {
        return MenuTypeEnum.BUTTON.getCode().equals(menu.getModuleTypeCode())
                && ButtonJumpTypeEnum.MODEL.getType().equals(menu.getJumpType());
    }

    @Transactional
    public List<Menu> getModuleListByParentId(String parentMenuId) {
        String hql = "from Menu m where m.parent.id= :parentId and m.status in (1,2) order by m.sequenceNumber asc,m.createTime asc";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("parentId", parentMenuId);
        return jpaCrud.select(hql, paramMap);
    }



    @Transactional
    public List<Menu> getAdminMenuList() {
//        String hql = "from Menu m left join fetch m.parent where m.status =1 and m.hideFlag = false order by m.sequenceNumber asc,m.createTime asc";
        String hql = "from Menu m left join fetch m.parent where m.status =1 order by m.sequenceNumber asc,m.createTime asc";
        return jpaCrud.select(hql);
    }

    private String getFullUrl(Menu menu, String url) {
        if (menu.getParent() == null) {
            return url;
        }
        Menu parentMenu = jpaCrud.getById(Menu.class, menu.getParent().getId());
        url = parentMenu.getName() + "/" + url;
        return getFullUrl(parentMenu, url);
    }

    @Transactional
    public void batchSaveOrUpdate(List<MenuData> items) {
        if (items != null) {
            for (MenuData menuData : items) {
                Menu menu = getMenuByParentCode(menuData.getCode(),menuData.getOid());
                if (menu == null) {
                    menu = addMenu(null, menuData);
                } else {
                    updateMenu(menu, null, menuData);
                }
                initMenuData(menu, menuData);
            }
        }
    }

    private void initMenuData(Menu parentMenu, MenuData parentMenuData) {
        if (parentMenuData.getChildren() == null) {
            return;
        }
        for (MenuData menuData : parentMenuData.getChildren()) {
            Menu menu = getMenuByParentCode(menuData.getCode(),parentMenuData.getOid());
            if (menu == null) {
                menu = addMenu(parentMenu, menuData);
            } else {
                updateMenu(menu, parentMenu, menuData);
            }
            initMenuData(menu, menuData);
        }
    }

    @Transactional
    public Menu addMenu(Menu parentMenu, MenuData menuData) {
        Menu menu = new Menu();
        menu.create(parentMenu, menuData.getName(), menuData.getCode(), menuData.getValue(), menuData.getType(), menuData.getSort(), menuData.getHide() ? MenuStatus.DISABLE.getValue() : MenuStatus.OPEN.getValue(), menuData.getIcon(), currentService);
//        menu.setOwnComponent(getFabModule(menuData.getComponentName()));
//        menu.setOid(menuData.getOid());
        jpaCrud.insert(menu);
        addPowerButton(menu);
        return menu;
    }

    @Transactional
    public Menu updateMenu(Menu menu, Menu parentMenu, MenuData menuData) {
        menu.update(parentMenu, menuData.getName(), menuData.getCode(), menuData.getValue(), menuData.getType(), menuData.getSort(), menuData.getHide() ? MenuStatus.DISABLE.getValue() : MenuStatus.OPEN.getValue(), menuData.getIcon(),currentService);
//        menu.setOwnComponent(getFabModule(menuData.getComponentName()));
//        menu.setOid(menuData.getOid());
        jpaCrud.update(menu);
        return menu;
    }

    @Transactional
    public Boolean deleteMenu(String id) {
        jpaCrud.deleteById(Menu.class, id);
        return true;
    }

    @Transactional
    public boolean checkByParentIdAndValue(Menu menu) {
        String hql = "from Menu m where m.parent.id= :parentId and m.moduleValue LIKE :value";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("parentId", menu.getId());
        paramMap.put("value", String.format("%s@%%", menu.getModuleValue()));
        List<Menu> list = jpaCrud.select(hql, paramMap);
        if (CollectionUtils.isEmpty(list)) {
            return true;
        }
        return false;
    }

    @Transactional
    public boolean checkByParentIdAndMenuName(Menu menu) {
        Menu menu1 = new Menu();
        menu1.setParent(menu.getParent());
        menu1.setName(menu.getName());
        List<Menu> list = jpaCrud.select(menu1);
        if (!CollectionUtils.isEmpty(list)) {
            throw new FabosJsonApiErrorTip("不能存在相同名称的菜单");
        }
        return true;
    }

    @Transactional
    public void addPowerButton(Menu menu) {
        if (menu.getModuleValue() == null || (!MenuTypeEnum.checkCreatePower(menu.getModuleTypeCode()))) {
            return;
        }
        if (!checkByParentIdAndValue(menu)) {
            return;
        }
        FabosJsonModel fabosJsonModel = FabosJsonCoreService.getFabosJson(menu.getModuleValue());
        FabosFunPermissions[] funPermissions = FabosFunPermissions.values();
        for (int i = 0; i < funPermissions.length; i++) {
            FabosFunPermissions funPermission = funPermissions[i];
            if (funPermission != null || funPermission.verifyPower(fabosJsonModel.getFabosJson().power())) {
                Menu buttioMenu = new Menu();
                buttioMenu.create(menu, funPermission.getName(), buttioMenu.generateCode(8), UPMSUtil.getFunPermissionsCode(menu.getModuleValue(), funPermission), MenuTypeEnum.BUTTON.getCode(), i, MenuStatus.OPEN.getValue(), null, currentService);
//                buttioMenu.setOwnComponent(menu.getOwnComponent());
//                buttioMenu.setOid(menu.getOid());
                jpaCrud.insert(buttioMenu);
            }
        }
    }

    @Transactional
    public Menu getMenuByParentCode(String code,String oid) {
        Menu menu = new Menu();
        menu.setModuleCode(code);
        menu.setOid(oid);
        List<Menu> list = jpaCrud.select(menu);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    @Transactional
    public List<Menu> getRootMenuList() {
        String hql = "from Menu m where m.parent.id is null and m.status in (1,2) order by m.sequenceNumber asc,m.createTime asc";
        return jpaCrud.select(hql);
    }


    @Transactional
    public MetadataComponent getFabModule(String moduleName) {
        if (StringUtils.isEmpty(moduleName)) {
            return null;
        }
        MetadataComponent fabModule = new MetadataComponent();
        fabModule.setCmpName(moduleName.trim());
        List<MetadataComponent> list = jpaCrud.select(fabModule);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }


    public Menu convertToMenu(MenuDTO dto) {
        Menu menu = new Menu();
        Menu parent = jpaCrud.getById(Menu.class, dto.getParentId());
        BeanUtils.copyProperties(dto, menu);
        menu.setStatus(MenuStatus.OPEN.getValue());
        menu.setParent(parent);
        menu.setCreateTime(LocalDateTime.now());
        menu.setCreateBy(UserContext.getAccount());
        menu.setUpdateTime(menu.getCreateTime());
        menu.setUpdateBy(UserContext.getAccount());
        return menu;
    }

    @Transactional
    public boolean checkByParentIdAndMenuName(MenuDTO dto) {
        List<Menu> list = getByParentIdAndMenuName(dto.getParentId(), dto.getName());
        if (CollectionUtils.isEmpty(list)) {
            return true;
        } else {
            return false;
        }
    }

    @Transactional
    public List<Menu> getByParentIdAndMenuName(String parentId, String name) {
        String hql = "from Menu m where m.parent.id= :parentId and m.name= :name";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("parentId", parentId);
        paramMap.put("name", name);
        List<Menu> list = jpaCrud.select(hql, paramMap);
        return list;
    }

    @Transactional
    public String saveOrUpdateLowCodeMenu(MenuDTO dto) {
        String menuId;
        if (dto==null) {
            return null;
        }
        if (checkByParentIdAndMenuName(dto)) {
            Menu menu = this.convertToMenu(dto);
            jpaCrud.insert(menu);
            menuId = menu.getId();
        } else {
            Menu menu = getByParentIdAndMenuName(dto.getParentId(), dto.getName()).get(0);
            BeanUtils.copyProperties(dto, menu);
            menu.setStatus(MenuStatus.OPEN.getValue());
            jpaCrud.update(menu);
            menuId=menu.getId();
        }
        return menuId;
    }
    @Transactional
    public Boolean saveOrUpdateLowCodeButton(List<MenuDTO> dtos,String parentId) {
        String hql = "from Menu m where m.parent.id= :parentId and m.moduleTypeCode= :moduleTypeCode";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("parentId", parentId);
        paramMap.put("moduleTypeCode", "button");
        if (!CollectionUtils.isEmpty(dtos)) {
            dtos.forEach(menuDTO -> menuDTO.setModuleTypeCode(MenuTypeEnum.BUTTON.getCode()));
            List<String> buttonValues = dtos.stream().map(MenuDTO::getModuleValue).toList();
            hql = "from Menu m where m.parent.id= :parentId and m.moduleTypeCode= :moduleTypeCode and moduleValue not in :buttonValues";
            paramMap.put("buttonValues", buttonValues);
        }
        List<Menu> deleteMenus = jpaCrud.select(hql, paramMap);
        delete(deleteMenus);
        //新增或者修改
        if(!CollectionUtils.isEmpty(dtos)){
            List<MenuDTO> saveOrUpdateList = new ArrayList<>(dtos);
            saveOrUpdateList.forEach(this::saveOrUpdateLowCodeMenu);
        }
        return true;
    }
    private void delete(List<Menu> deleteMenus){
        if(!CollectionUtils.isEmpty(deleteMenus)){
            deleteMenus.forEach(d-> {
                //已经存在的菜单可能已经被分配权限  所以这里不能全部删除了再全部新增。   如果存在删除数据 需要校验菜单是否被使用。被使用就报错发布失败
                if(checkUse(d.getId())){
                    throw new FabosJsonApiErrorTip("存在按钮被使用，禁止删除");
                }
                jpaCrud.deleteById(Menu.class, d.getId());
            });
        }
    }
    /**
     *
     * @param menuId
     * @return  true 在使用   false没有使用
     */
    @Transactional
    public boolean checkUse(String menuId){
        String sql = "select m.menu_id from fd_role_menu m where m.menu_id = '" + menuId +"'";
        List<String> menuIds = fabosJsonDao.getJdbcTemplate().query(sql, (rs, rowNum) -> rs.getString("menu_id"));
        return !CollectionUtils.isEmpty(menuIds);
    }

    /**
     * 查询菜单下的子菜单（包含按钮）
     *
     * @param parent 菜单对象
     * @return true 有子菜单，false 没有子菜单
     */
    @Transactional
    public List<Menu> retrieveChildMenu(Menu parent) {
        Specification<Menu> specification = (root, query, criteriaBuilder) -> {
            // 获取parent的id
            String parentId = parent.getId();

            // 使用parent的id进行比较
            Predicate parentPredicate = criteriaBuilder.equal(root.get("parent").get("id"), parentId);

            // 使用routerModule的id进行比较
            Predicate routerModulePredicate = criteriaBuilder.equal(root.get("routerModule").get("id"), parentId);

            // 使用or条件组合两个Predicate
            return criteriaBuilder.or(parentPredicate, routerModulePredicate);
        };
        return menuRepository.findAll(specification);
    }

    /**
     * 删除子菜单（按钮），而不会删除父菜单
     *
     * @param parentMenu 父菜单
     */
    @Transactional
    public void tryToDeleteChildButtonWithException(Menu parentMenu) {
        List<Menu> retrievedChildMenus = retrieveChildMenu(parentMenu);
        // 如果此菜单下除了按钮，没有其他子菜单，则先删除其按钮
        if (CollectionUtils.isEmpty(retrievedChildMenus)) {
            return;
        }
        List<Menu> childButtons = retrievedChildMenus.stream().filter(m -> Objects.equals(m.getModuleTypeCode(), MenuTypeEnum.BUTTON.getCode())).toList();
        List<Menu> childMenus = retrievedChildMenus.stream().filter(m -> !Objects.equals(m.getModuleTypeCode(), MenuTypeEnum.BUTTON.getCode())).toList();
        // 如果全为按钮的子菜单
        if (CollectionUtils.isNotEmpty(childMenus)) {
            throw new FabosJsonApiErrorTip(String.format("由于菜单[%s]存在子菜单[%s]，此菜单无法被删除"
                    , parentMenu.getName(), childMenus.stream().map(Menu::getName).collect(Collectors.joining(","))));
        }
        // 检查此菜单和其子菜单是否已被分配权限
        List<Role> rolesUsingMenus = roleRepository.findRolesUsingTheseMenus(new ArrayList<>() {{
            add(parentMenu.getId());
            addAll(childButtons.stream().map(Menu::getId).toList());
        }});
        if (CollectionUtils.isNotEmpty(rolesUsingMenus)) {
            throw new FabosJsonApiErrorTip(String.format("由于菜单[%s]或其子菜单已分配给角色[%s]，此菜单无法被删除"
                    , parentMenu.getName(), rolesUsingMenus.stream().map(Role::getName).collect(Collectors.joining(","))));
        }
        menuRepository.deleteAll(childButtons);
    }

    @Transactional
    public List<Menu> saveMetaMenus4Init(List<MetaMenu> items) {
        List<Menu> menus = new ArrayList();
        if(!CollectionUtils.isEmpty(items)){
            for (MetaMenu metaMenu : items) {
                Menu menu = Menu.fromMetaMenu(metaMenu);
//                menu.setOwnComponent(getFabModule(metaMenu.getComponentName()));
//                menu.setOid(metaMenu.getOid());
                String[] values = new String[]{metaMenu.getValue()};
                if(StringUtils.isBlank(menu.getName())){
                    continue;
                }
                //menu = fabosJsonDao.persistIfNotExistWithTenantId(Menu.class, menu, Menu.VALUE, values);
                menu = saveOrUpdateMenuByValue(menu);
                menus.add(menu);
                metaMenu.setId(menu.getId());
                if (null != menu.getModuleTypeCode() && null != menu.getModuleValue()) {
                    if (MenuTypeEnum.checkCreatePower(menu.getModuleTypeCode())) {
                        if(!CollectionUtils.isEmpty(metaMenu.getOperations())){
                            for (MetaMenu op : metaMenu.getOperations()) {
                                Menu opMenu = Menu.fromMetaMenu(op);
                                opMenu.setParent(menu);
//                                opMenu.setOwnComponent(getFabModule(op.getComponentName()));
                                opMenu.setOid(op.getOid());
                                opMenu.setApplicationName(menu.getApplicationName());
                                String[] values1 = new String[]{op.getValue()};
                                //opMenu = fabosJsonDao.persistIfNotExistWithTenantId(Menu.class, opMenu, Menu.VALUE, values1);
                                opMenu = saveOrUpdateMenuByValue(opMenu);
                                menus.add(opMenu);
                            }
                        }
                    }
                }
            }
        }
        return menus;
    }

    /**
     * 初始化逻辑调整
     * @param menu
     * @return
     */
    @Transactional
    public Menu saveOrUpdateMenuByValue(Menu menu){

        Menu query = new Menu();
        query.setModuleValue(menu.getModuleValue());
        List<Menu> menuList = jpaCrud.select(query);
        if(CollectionUtils.isEmpty(menuList)){
            jpaCrud.insert(menu);
            return menu;
        }else {
            Menu existingMenu = menuList.get(0);
            //按钮类型不等于跳转才更新  跳转按钮不更新
            if (checkUpdate(menu, existingMenu)) {
                // 更新 moduleValue唯一键  只存在一个
                // 菜单调整-0721：将数据库中已设置的值，merge到menu中，保证CSV中不存在，但数据库中存在的值不会被覆盖
                // 保留数据库中的关键字段值，只更新CSV中定义的字段
                mergeExistingMenuData(menu, existingMenu);
                fabosJsonDao.mergeAndFlush(menu);
            } else {
                // 不需要更新的情况，直接返回数据库中的现有菜单
                return existingMenu;
            }
        }
        return menu;
    }

    /**
     * 将数据库中已存在的菜单数据merge到新的menu对象中
     * 保证数据库中存在但CSV中不存在的字段值不会被覆盖
     * 这种主要用于处理用户批量在DB中修改了值的情况，比如修改的菜单隐藏、序号、说明、状态
     */
    private void mergeExistingMenuData(Menu newMenu, Menu existingMenu) {
        // 保留数据库中的基础信息
        newMenu.setId(existingMenu.getId());

        // 保留数据库中用户可能手动设置的字段，如果新menu中为空的话
        if (newMenu.getHideFlag() == null) {
            newMenu.setHideFlag(existingMenu.getHideFlag());
        }
        if (newMenu.getSequenceNumber() == null) {
            newMenu.setSequenceNumber(existingMenu.getSequenceNumber());
        }
        if (StringUtils.isBlank(newMenu.getModuleDescription())) {
            newMenu.setModuleDescription(existingMenu.getModuleDescription());
        }
        if (newMenu.getStatus() == null) {
            newMenu.setStatus(existingMenu.getStatus());
        }

        // 设置更新信息
        String setBy = MetaContext.getUser().getName() == null ? "initMenu" : MetaContext.getUser().getName();
        newMenu.setCreateTime(LocalDateTime.now());
        newMenu.setCreateBy(setBy);
        newMenu.setUpdateTime(newMenu.getCreateTime());
        newMenu.setUpdateBy(setBy);
    }

    /**
     * 返回true需要更新
     * @param menu
     * @return
     */
    private boolean checkUpdate(Menu menu, Menu existingMenu) {
        //按钮类型等于跳转返回false
        if(RowOperation.MenuButtonTypeEnum.JUMP.name().equals(menu.getButtonType())){
            return false;
        }
        //按钮类型为默认并且pageType为静态页面并且href不为空 返回false
        if(RowOperation.MenuButtonTypeEnum.DEFAULT.name().equals(menu.getButtonType()) && MenuPageTypeEnum.STATIC.getCode().equals(menu.getPageType()) && StringUtils.isNotBlank(menu.getHref())){
            return false;
        }

        //如果菜单在创建3秒后有改动，则不更新
        if (existingMenu.getCreateTime() != null && existingMenu.getUpdateTime() != null && existingMenu.getUpdateTime().isAfter(existingMenu.getCreateTime().plusSeconds(3))) {
            return false;
        }
        return true;

    }
    /**
     * 剔除button
     * @return
     */
    @Transactional
    public List<MenuData> getRemoveButtonMenuList(){
        List<MenuData> list = new ArrayList<>();
        List<Menu> menuList = getRootMenuList();
        if(CollectionUtils.isEmpty(menuList)){
            return new ArrayList<>();
        }
        List<Menu> removeMenuList = menuList.stream()
                .filter(m->!MenuTypeEnum.BUTTON.getCode().equals(m.getModuleTypeCode()))
                .toList();
        for (Menu menu : removeMenuList) {
            MenuData menuData = convertMenuData(menu);
            buildChildMenuTree(menu, menuData);
            list.add(menuData);
        }
        return list;
    }
    @Transactional
    public void buildChildMenuTree(Menu parentMenu, MenuData parentMenuData) {
        String hql = "from Menu m where m.parent.id= :parentId and m.status in (1,2) and m.moduleTypeCode!='button' order by m.sequenceNumber asc,m.createTime asc";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("parentId", parentMenu.getId());
        List<Menu> menuList = jpaCrud.select(hql, paramMap);
        if (CollectionUtils.isEmpty(menuList)) {
            return;
        }
        ArrayList<MenuData> childList = new ArrayList<>();
        for (Menu menu : menuList) {
            MenuData parentData = convertMenuData(menu);
            buildChildMenuTree(menu, parentData);
            childList.add(parentData);
        }
        parentMenuData.setChildren(childList);

    }

    public Menu getByModuleValue(String value) {
        Menu menu = new Menu();
        menu.setModuleValue(value);
        List<Menu> selected = fabosJsonDao.select(menu);
        if (CollectionUtils.isNotEmpty(selected) && selected.size() != 1) {
            throw new FabosJsonApiErrorTip("没有找到或找到了多个菜单，请检查");
        }
        return selected.isEmpty() ? null : selected.get(0);
    }

}
