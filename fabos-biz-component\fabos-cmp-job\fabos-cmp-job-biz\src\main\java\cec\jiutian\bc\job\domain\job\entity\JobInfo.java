package cec.jiutian.bc.job.domain.job.entity;

import cec.jiutian.bc.job.domain.job.event.JobDataProxy;
import cec.jiutian.bc.job.domain.log.entity.JobLog;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.handler.SqlChoiceFetchHandler;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.constant.AnnotationConst;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.BoolType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Drill;
import cec.jiutian.view.type.Link;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;


@FabosJson(
        name = "任务配置",
        formColumnElements = 1,
        dataProxy = JobDataProxy.class,
        drills = @Drill(title = "日志", icon = "fa fa-sliders", link = @Link(linkFabosJson = JobLog.class, joinColumn = "jobId")),
        rowOperation = @RowOperation(code = "action", title = "执行一次任务", mode = RowOperation.Mode.SINGLE, fabosJsonClass = JobExecDialog.class, operationHandler = JobDataProxy.class)
)
@Entity
@Table(name = "fd_job", uniqueConstraints = @UniqueConstraint(columnNames = "code, oid"))
@Getter
@Setter
public class JobInfo extends MetaModel {

    @Column(length = AnnotationConst.CODE_LENGTH)
    @FabosJsonField(
            views = @View(title = "编码", width = "100px"),
            edit = @Edit(title = "编码", notNull = true, search = @Search(vague = true), readonly = @Readonly(add = false))
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "任务执行器"),
            edit = @Edit(
                    title = "任务执行器",
                    search = @Search(vague=true),
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(
                            fetchHandler = SqlChoiceFetchHandler.class,
                            fetchHandlerParams = {"select METHOD_PACKAGE_NAME, display_name || '(' || method_code || ')' as method_code from metadata_function where source_type = '04'", "5000"}
                    )
            )
    )
    private String handler;

    @FabosJsonField(
            views = @View(title = "任务名称"),
            edit = @Edit(title = "任务名称", notNull = true, search = @Search(vague = true))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "Cron表达式", width = "150px"),
            edit = @Edit(title = "Cron表达式", notNull = true)
    )
    private String cron;

    @FabosJsonField(
            views = @View(title = "任务状态"),
            edit = @Edit(title = "任务状态", boolType = @BoolType(
                    trueText = "启用", falseText = "禁用"
            ), notNull = true, search = @Search)
    )
    private Boolean status = true;

    @FabosJsonField(
            views = @View(title = "记录日志"),
            edit = @Edit(title = "记录日志", boolType = @BoolType(
                    trueText = "是", falseText = "否"
            ), notNull = true, search = @Search)
    )
    private Boolean recordLog = true;

    @Column(length = AnnotationConst.REMARK_LENGTH)
    @FabosJsonField(
            views = @View(title = "失败通知邮箱"),
            edit = @Edit(title = "失败通知邮箱")
    )
    private String notifyEmails;

    @Column(length = AnnotationConst.REMARK_LENGTH)
    @FabosJsonField(
            views = @View(title = "任务参数"),
            edit = @Edit(title = "任务参数", type = EditType.CODE)
    )
    private String handlerParam;

    @Column(length = AnnotationConst.REMARK_LENGTH)
    @FabosJsonField(
            views = @View(title = "描述"),
            edit = @Edit(title = "描述")
    )
    private String remark;

}
