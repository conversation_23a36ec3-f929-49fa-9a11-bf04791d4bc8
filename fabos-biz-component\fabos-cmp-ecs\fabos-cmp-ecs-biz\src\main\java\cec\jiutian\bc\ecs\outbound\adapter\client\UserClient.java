package cec.jiutian.bc.ecs.outbound.adapter.client;

import cec.jiutian.bc.urm.dto.MetaUserinfo;
import cec.jiutian.bc.urm.provider.IUserProvider;
import com.alipay.sofa.koupleless.common.api.AutowiredFromBiz;
import org.springframework.stereotype.Component;

@Component
public class UserClient {

    @AutowiredFromBiz(bizName = "fabos-cmp-urm", bizVersion = "3.2.2-SNAPSHOT")
    private IUserProvider userProvider;

    public MetaUserinfo getMetaUserByAccount(String account){
        return userProvider.getMetaUserByAccount(account);
    }
}
