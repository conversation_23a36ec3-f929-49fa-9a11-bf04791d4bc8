package cec.jiutian.bc.ecs.domain.generalIp.entity;

import cec.jiutian.bc.ecs.domain.generalIp.event.GeneralIpDataProxy;
import cec.jiutian.bc.infra.util.AesUtil;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.field.edit.VL;
import io.swagger.annotations.ApiModelProperty;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.UUID;

@Entity
@Table(name = "ecs_general_ip",
        indexes = {
        @Index(columnList = "sys_name,oid",unique = true)
})
@Getter
@Setter
@FabosJson(name = "白名单管理", dataProxy = {GeneralIpDataProxy.class},
        orderBy = "GeneralIp.createTime desc")
public class GeneralIp extends MetaModel implements Serializable {


    @FabosJsonField(
            views = @View(title = "系统名称"),
            edit = @Edit(title = "系统名称", notNull = true, search = @Search(vague = true))
    )
    @ApiModelProperty("系统名称")
    private String sysName;

    @FabosJsonField(
            views = @View(title = "密钥", show = false),
            edit = @Edit(title = "密钥",  show = false)
    )
    @ApiModelProperty("密钥")
    private String authKey;

    @FabosJsonField(
            views = @View(title = "状态"),
            edit = @Edit(title = "状态", notNull = true, search = @Search,
                         type = EditType.CHOICE,
                         choiceType = @ChoiceType(vl = {@VL(label = "启用", value = "启用"),
                                                        @VL(label = "禁用", value = "禁用")}))
    )
    @ApiModelProperty("状态")
    private String status;

    @FabosJsonField(
            views = @View(title = "备注", show = false),
            edit = @Edit(title = "备注", show = false)
    )
    @ApiModelProperty("备注")
    @Column(name = "LST_EVNT_CMNT")
    private String lastEventComment;

    public void setAuthKey(String sysName, String aesKey) {
        this.authKey = AesUtil.encrypt(sysName + "-" + UUID.randomUUID().toString().replaceAll("-", ""), aesKey);
    }
}
