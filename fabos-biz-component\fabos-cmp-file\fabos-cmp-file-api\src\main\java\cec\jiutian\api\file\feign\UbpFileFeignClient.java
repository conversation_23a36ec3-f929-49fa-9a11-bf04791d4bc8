package cec.jiutian.api.file.feign;

import cec.jiutian.api.remoteCallLog.dto.RemoteCallResult;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @time 2025-05-08 16:44
 */


//@FeignClient(name = "xlx-ubp", url = "http://*************:8080/xlx-ubp/")
@FeignClient(name = "xlx-ubp", contextId = "ubpFileFeignClient")
public interface UbpFileFeignClient {

    @PostMapping(value = FabosJsonRestPath.FABOS_REMOTE_API_OLD + "/fileUpload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    RemoteCallResult<String> fileUpload(@RequestPart(value = "file") MultipartFile file, @RequestParam(value = "fabosJsonFileSuper") String fabosJsonFileSuperStr);


    //    @GetMapping(value = FabosJsonRestPath.FABOS_FILE + DOWNLOAD_PATH + "/**", produces = {MediaType.APPLICATION_OCTET_STREAM_VALUE})
    @PostMapping(value = FabosJsonRestPath.FABOS_REMOTE_API_OLD + FabosJsonRestPath.FILE_PREFIX + "/download")
    ResponseEntity<Resource> fileDownload(@RequestParam String id);

}
