<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cec.jiutian</groupId>
        <artifactId>fabos-cmp-ecs</artifactId>
        <version>3.2.2-SNAPSHOT</version>
    </parent>

    <artifactId>fabos-cmp-ecs-biz</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-cmp-urm-biz-api</artifactId>
            <version>${project.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-cmp-ecs-biz-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-component-core</artifactId>
            <version>${project.version}</version>
            <!--            <scope>provided</scope>-->
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa.koupleless</groupId>
            <artifactId>koupleless-app-starter</artifactId>
            <version>2.1.1</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-data-jpa</artifactId>
            <version>${project.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
<!--            <scope>provided</scope>-->
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-faas</artifactId>
            <version>${project.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-cmp-workflow-biz-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-cmp-job-biz-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>cec.jiutian</groupId>
            <artifactId>fabos-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20210307</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>sofa-ark-maven-plugin</artifactId>
                <version>3.1.3</version>
                <executions>
                    <execution>
                        <id>default-cli</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <skipArkExecutable>true</skipArkExecutable>
                    <outputDirectory>./target</outputDirectory>
                    <bizName>fabos-cmp-ecs</bizName>
                    <webContextPath>fabos-cmp-ecs</webContextPath>
                    <declaredMode>true</declaredMode>
                    <priority>2</priority>
                    <!--					打包、安装和发布 ark biz-->
                    <!--					静态合并部署需要配置-->
                    <!--					<attach>true</attach>-->
                    <!-- 多 bundle 使用该配置文件 conf/ark/rules.txt 排包，单 bundle 由于不存在依赖传递丢失问题建议使用 scope=provided 排包 -->
                    <packExcludesConfig>rules.txt</packExcludesConfig>
                </configuration>
            </plugin>
            <plugin>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-maven-plugin</artifactId>
            <configuration>
                <mainClass>cec.jiutian.bc.CmpEcsApplication</mainClass>
                <skip>true</skip>
            </configuration>
            <executions>
                <execution>
                    <id>repackage</id>
                    <goals>
                        <goal>repackage</goal>
                    </goals>
                    <configuration>
                        <loaderImplementation>CLASSIC</loaderImplementation>
                    </configuration>
                </execution>
            </executions>
        </plugin>
        </plugins>
    </build>
</project>
