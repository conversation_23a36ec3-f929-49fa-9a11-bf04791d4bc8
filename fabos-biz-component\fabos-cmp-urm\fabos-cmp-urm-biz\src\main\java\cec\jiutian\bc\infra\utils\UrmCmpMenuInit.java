package cec.jiutian.bc.infra.utils;

import cec.jiutian.bc.urm.domain.menu.service.MenuService;
import cec.jiutian.bc.urm.domain.tenant.entity.Tenant;
import cec.jiutian.bc.urm.inbound.local.config.SystemConfig;
import cec.jiutian.bc.urm.inbound.local.enums.PlatformTenantModeEnum;
import cec.jiutian.core.frame.constant.MenuTypeEnum;
import cec.jiutian.core.frame.module.FabModule;
import cec.jiutian.core.frame.util.MetaMenuUtil;
import cec.jiutian.bc.urm.domain.tenant.constant.TenantContext;
import cec.jiutian.bc.urm.dto.MetaMenu;
import jakarta.annotation.Resource;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/4
 */
@Service
public class UrmCmpMenuInit implements CommandLineRunner {
    private final MenuService menuService;

    private static final String COMPONENT = "fabos-cmp-urm";

    public UrmCmpMenuInit(MenuService menuService) {
        this.menuService = menuService;
    }

    @Resource
    private SystemConfig systemConfig;

    @Override
    public void run(String... args) {
        //目前是单租户  暂时不处理这部分菜单
        //多租户模式下初始化平台管理相关菜单
//        List<MetaMenu> menus = new ArrayList<>();
//        menus.add(MetaMenu.createRootMenu("$manager", "平台管理", "fa fa-cogs", 1, TenantContext.ROOT_TENANT_ID, COMPONENT));
//        menus.add(MetaMenuUtil.createFabosJsonClassMenu(Tenant.class, menus.get(0), 3, MenuTypeEnum.TABLE, TenantContext.ROOT_TENANT_ID, COMPONENT));
//        menus.add(MetaMenuUtil.createFabosJsonClassMenu(FabModule.class, menus.get(0), 10, MenuTypeEnum.TABLE, TenantContext.ROOT_TENANT_ID, COMPONENT));
//        menuService.saveMetaMenus4Init(menus);
    }
}
