package cec.jiutian.bc.generalModeler.domain.material.proxy;

import cec.jiutian.bc.generalModeler.domain.material.model.Material;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Component
@Service
public class MaterialDataProxy implements DataProxy<Material> {

    @Resource
    private JpaCrud jpaCrud;

    @Override
    public void editBehavior(Material material) {
//
        BeanWrapper beanWrapper = new BeanWrapperImpl(material);
        if (material.getMaterialCategory().getMaterialAttributeTemplate() != null) {
            material.getMaterialCategory().getMaterialAttributeTemplate().getFields().forEach(e -> {
                beanWrapper.setPropertyValue(e.getColumnName() + "Show", 'Y');
            });
        }
        DataProxy.super.editBehavior(material);
    }
}
