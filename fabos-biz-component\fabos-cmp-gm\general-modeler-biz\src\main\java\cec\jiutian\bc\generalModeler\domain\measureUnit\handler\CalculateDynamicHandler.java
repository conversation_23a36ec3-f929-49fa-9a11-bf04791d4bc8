package cec.jiutian.bc.generalModeler.domain.measureUnit.handler;

import cec.jiutian.bc.generalModeler.domain.measureUnit.model.UnitConversionRuleData;
import cec.jiutian.bc.generalModeler.domain.measureUnit.util.CalculateUtil;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.view.DependFiled;
import com.googlecode.aviator.Expression;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2024/11/12 10:44
 * @description：
 */
@Component
public class CalculateDynamicHandler implements DependFiled.DynamicHandler<UnitConversionRuleData> {
    @Override
    public Map<String, Object> handle(UnitConversionRuleData unitConversionRuleData) {
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> data = new HashMap<>();
        if (unitConversionRuleData.getSourceMeasureUnit() != null && unitConversionRuleData.getTargetMeasureUnit() != null) {
            if (unitConversionRuleData.getMainUnitParam() == null) {
                throw new FabosJsonApiErrorTip("参数不可为空");
            }

            if (!unitConversionRuleData.getSourceMeasureUnit().getId().equals(unitConversionRuleData.getTargetMeasureUnit().getSourceMeasureUnit().getId())) {
                throw new FabosJsonApiErrorTip("选中主、辅计量单位之间未建立换算规则，请确认");
            }

            data.put("X", unitConversionRuleData.getMainUnitParam());
            Expression compileExpression = CalculateUtil.compile(unitConversionRuleData.getTargetMeasureUnit().getCalculateFormula());
            String result = String.valueOf(CalculateUtil.generalExpressionCalculate(compileExpression, data));
            BigDecimal bd = BigDecimal.valueOf(Double.parseDouble(result));
            bd = bd.setScale(unitConversionRuleData.getTargetMeasureUnit().getPrecision(), RoundingMode.HALF_UP);
            map.put("auxiliaryUnitParam", String.valueOf(bd));
        }
        return map;
    }
}
