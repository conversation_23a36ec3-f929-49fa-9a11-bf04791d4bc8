package cec.jiutian.bc.ecs.domain.userMessageRecord.entity;

import cec.jiutian.bc.ecs.domain.messagerecord.entity.MessageRecordSuper;
import cec.jiutian.bc.ecs.domain.userMessageRecord.event.UserMessageRecordDataProxy;
import cec.jiutian.bc.ecs.domain.userMessageRecord.event.UserMessageRecordOperationHandler;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.handler.UserRowOperationExprHandler;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.type.Power;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description:
 */
@Entity
@Table(name = "ecs_message_record")
@Getter
@Setter
@FabosJson(name = "我的告警", dataProxy = {UserMessageRecordDataProxy.class},
        orderBy = "UserMessageRecord.createTime desc",
        colorIndicator = "colorFlag",
        power = @Power(add = false, edit = false, delete = false,
                export = false, importable = false, viewDetails = true),
        rowOperation = {
                @RowOperation(
                        operationHandler = UserMessageRecordOperationHandler.class,
                        code = "read",
                        mode = RowOperation.Mode.SINGLE,
                        title = "标记已读",
                        type = RowOperation.Type.FABOSJSON,
                        ifExpr = "status == 'Y'"
                ),
                @RowOperation(
                        title = "处理",
                        code = "MessageRecord@PROCESSCOMPLETED",
                        mode = RowOperation.Mode.HEADER,
                        type = RowOperation.Type.POPUP,
                        popupType = RowOperation.PopupType.FORM,
                        menuButtonType = RowOperation.MenuButtonTypeEnum.MIX,
                        show = @ExprBool(
                                exprHandler = UserRowOperationExprHandler.class, //根据菜单类型值控制是否显示的实现类
                                params = "MessageRecord@PROCESSCOMPLETED"  //权限标识（全局唯一），菜单类型为按钮，类型值为testBtn即可控制该按钮
                        ),
                        ifExpr = "selectedItems[0].processCompleteFlag == 'Y' || selectedItems[0].isProcess != 'Y'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE
                )
})
@FabosJsonI18n
public class UserMessageRecord extends MessageRecordSuper {
//
//    @ManyToOne
//    @FabosJsonField(
//            views = @View(title = "告警消息", column = "title", type = ViewType.TABLE_FORM, show = false),
//            edit = @Edit(title = "告警消息", notNull = true, search = @Search(vague = true), show = false,
//                    type = EditType.REFERENCE_TABLE, referenceTableType = @ReferenceTableType(id = "id", label = "title"),
//                    readonly = @Readonly
//            )
//    )
//    @JoinColumn(foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
//    private Message message;
//
//    @FabosJsonField(
//            views = @View(title = "标题"),
//            edit = @Edit(title = "标题", search = @Search(vague = true))
//    )
//    private String title;
//
//    /**
//     * 推送方式
//     */
//    @FabosJsonField(
//            views = @View(title = "推送方式"),
//            edit = @Edit(title = "推送方式", search = @Search(vague = true))
//    )
//    private String dispatchWay;
//
//    /**
//     * 推送人员
//     */
//    @FabosJsonField(
//            views = @View(title = "推送人员"),
//            edit = @Edit(title = "推送人员")
//    )
//    private String dispatchUser;
//
//    @FabosJsonField(
//            views = @View(title = "是否已读"),
//            edit = @Edit(title = "是否已读",
//                    search = @Search,
//                    type = EditType.CHOICE,
//                    choiceType = @ChoiceType(fetchHandler = {YesOrNoStatus.ChoiceFetch.class}))
//    )
//    private String status;
//
//    @FabosJsonField(
//            views = @View(title = "是否已处理"),
//            edit = @Edit(title = "是否已处理",
//                    notNull = true,
//                    search = @Search,
//                    type = EditType.CHOICE,
//                    choiceType = @ChoiceType(fetchHandler = {YesOrNoStatus.ChoiceFetch.class}))
//    )
//    private String done;
//
//    /**
//     * 备注
//     */
//    @FabosJsonField(
//            views = @View(title = "备注", show = false),
//            edit = @Edit(title = "备注", show = false)
//    )
//    @Column(name = "LST_EVNT_CMNT")
//    private String lastEventComment;
//
//    /**
//     * 消息内容
//     */
//    @FabosJsonField(
//            views = @View(title = "消息内容"),
//            edit = @Edit(title = "消息内容",
//                    type = EditType.TEXTAREA,
//                    search = @Search(vague = true))
//    )
//    private String content;
}
