package cec.jiutian.bc.urm.inbound.local.context;

import cec.jiutian.bc.urm.domain.systemSecurityConfig.entity.SystemSecurityConfig;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.security.interfaces.RSAPublicKey;

@Slf4j
@Component
public class SecurityConfigContext {

    @Getter
    @Setter
    private static SystemSecurityConfig securityConfig;

    // 这里是ubp系统的公钥，在启动时调用ubp服务的/oauth2/jwks端点获取(feign)
    @Setter
    @Getter
    private static RSAPublicKey oauth2PublicKey;


    public static boolean isNotExists(){
        return get() == null;
    }

    public static SystemSecurityConfig get() {
        return getSecurityConfig();
    }

    public static void set(SystemSecurityConfig value) {
        setSecurityConfig(value);
    }

//    public static Boolean getSingleLoginEnabled() {
//        return get().getSingleLoginEnabled();
//    }

    public static Boolean getTriadManagementEnabled() {
        return get().getTriadManagementEnabled();
    }

    public static Integer getTimeoutLogoutMinutes() {
        return get().getTimeoutLogoutMinutes();
    }

    public static Integer getPasswordValidityDays() {
        return get().getPasswordValidityDays();
    }

    public static Integer getAuthenticationFailureCount() {
        return get().getAuthenticationFailureCount();
    }

    public static String getAccountUnlockMethod() {
        return get().getAccountUnlockMethod();
    }

    public static Integer getAccountAutoUnlockMinutes() {
        return get().getAccountAutoUnlockMinutes();
    }

    public static Integer getPasswordMinLength() {
        return get().getPasswordMinLength();
    }

    public static String getPasswordComplexity() {
        return get().getPasswordComplexity();
    }
}
