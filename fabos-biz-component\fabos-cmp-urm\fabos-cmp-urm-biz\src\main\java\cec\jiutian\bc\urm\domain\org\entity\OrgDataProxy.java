package cec.jiutian.bc.urm.domain.org.entity;

import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.persistence.LockModeType;
import org.springframework.stereotype.Service;

@Service
public class OrgDataProxy implements DataProxy<Org> {

    private final FabosJsonDao fabosJsonDao;

    public OrgDataProxy(FabosJsonDao fabosJsonDao) {
        this.fabosJsonDao = fabosJsonDao;
    }

    @Override
    public void beforeAdd(Org org) {
        // 设置role上的autoIncrementId值为递增+1，查询时需上锁
        org.setAutoIncrementId(
                fabosJsonDao.getEntityManager()
                        .createQuery("SELECT COALESCE(MAX(r.autoIncrementId),0) FROM Org r", Integer.class)
                        .setLockMode(LockModeType.PESSIMISTIC_WRITE)
                        .getSingleResult() + 1
        );
    }
}
