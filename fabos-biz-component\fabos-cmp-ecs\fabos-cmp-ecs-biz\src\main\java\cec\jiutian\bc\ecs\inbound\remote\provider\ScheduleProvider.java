package cec.jiutian.bc.ecs.inbound.remote.provider;

import cec.jiutian.bc.ecs.domain.generalIp.entity.GeneralIp;
import cec.jiutian.bc.job.provider.IJobProvider;
import cec.jiutian.core.frame.annotation.FabosCustomizedService;
import cec.jiutian.meta.FabosJob;
import org.springframework.stereotype.Component;

@Component
@FabosCustomizedService(value = GeneralIp.class)
public class ScheduleProvider implements IJobProvider {
    @Override
    @FabosJob(comment = "ECS schedule")
    public String exec(String code, String param) {
        return "ECS执行成功";
    }
}
