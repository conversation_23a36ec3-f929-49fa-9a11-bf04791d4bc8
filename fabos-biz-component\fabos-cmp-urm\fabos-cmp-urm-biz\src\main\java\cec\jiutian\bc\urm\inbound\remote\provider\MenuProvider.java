package cec.jiutian.bc.urm.inbound.remote.provider;

import cec.jiutian.bc.urm.inbound.local.service.command.UrmCommandService;
import cec.jiutian.bc.urm.dto.MenuDTO;
import cec.jiutian.bc.urm.provider.IMenuProvider;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 *
 */

@Component
public class MenuProvider implements IMenuProvider {

    private final UrmCommandService menuService;

    public MenuProvider(UrmCommandService menuService) {
        this.menuService = menuService;
    }


    @Override
    public String saveOrUpdateLowCodeMenu(MenuDTO dto) {
        return menuService.saveOrUpdateLowCodeMenu(dto);
    }
    @Override
    public Boolean saveOrUpdateLowCodeButton(List<MenuDTO> dtos,String parentId) {
        return menuService.saveOrUpdateLowCodeButton(dtos,parentId);
    }
}
