//package cec.jiutian.bc.ecs.domain.extendFabosJsonTest;
//
//
//import cec.jiutian.core.view.fabosJson.FabosJson;
//import cec.jiutian.core.view.fabosJson.FlowExtend;
//import cec.jiutian.core.view.fabosJson.InheritStrategy;
//import cec.jiutian.view.FabosJsonField;
//import cec.jiutian.view.expr.ExprBool;
//import cec.jiutian.view.field.Edit;
//import cec.jiutian.view.field.View;
//import cec.jiutian.view.field.edit.Search;
//import cec.jiutian.view.type.*;
//import jakarta.persistence.Entity;
//import jakarta.persistence.Table;
//import lombok.Getter;
//import lombok.Setter;
//import lombok.experimental.Accessors;
//
//@FabosJson(name = "狗",
//
//        formOperation = {
//                @FormOperation(
//                        name = "测试555",
//                        code = "Dog@form",
//                        position = FormOperation.Position.RIGHT,
//                        closeFormAfterClick = false,
//                        affectMode = FormOperation.AffectMode.edit,
//                        operationHandler = DogFormOperationHandler.class
//                )
//        },
//        rowOperation = {
//                @RowOperation(
//                        code = "Dog@TEST",
//                        ifExpr = "selectedItems[0].state !='Y'",
//                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE,
//                        mode = RowOperation.Mode.HEADER,
//                        type = RowOperation.Type.POPUP,
//                        popupType = RowOperation.PopupType.FORM,
//                        fabosJsonClass = DogView.class,
//                        submitMethod = RowOperation.SubmitMethod.HANDLER,
//                        operationHandler = DogOperationHandler.class,
//                        title = "Dog测试",
//                        show = @ExprBool(
//                                value = true,
//                                params = "Dog@TEST"
//                        )
//                ),
//        },
//        flowCode = "Dog",
//        power = @Power(examineDetails = true, examine = true),
//        drills = {
//                @Drill(title = "D测试1", code = "dogTest1", icon = "fa fa-sliders", link = @Link(linkFabosJson = Animal.class, joinColumn = "id")),
//                @Drill(title = "D测试2", code = "dogTest2", icon = "fa fa-sliders", link = @Link(linkFabosJson = Animal.class, joinColumn = "id"))
//        }
//)
//@Setter
//@Getter
//@Entity
//@Accessors(chain = true)
//@Table(name = "Animal")
//@InheritStrategy(
//
//        excludeParentRowBaseOperationCode = {
//                "edit"
//        },
//        dataProxyFlag = true,
//        flowExtend = @FlowExtend(
//                extendFlag = true,
//                parentFlowCode = "Animal",
//                patentFlowProxy = AnimalFlowProxy.class
//        )
//
//
//)
//public class Dog extends Animal {
//
//
//    @FabosJsonField(
//            views = @View(title = "颜色"),
//            edit = @Edit(title = "颜色", search = @Search(vague = true))
//    )
//    private String color;
//}
