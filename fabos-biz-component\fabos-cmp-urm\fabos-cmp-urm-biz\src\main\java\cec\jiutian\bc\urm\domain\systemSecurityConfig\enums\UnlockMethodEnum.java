package cec.jiutian.bc.urm.domain.systemSecurityConfig.enums;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
public enum UnlockMethodEnum {
    Auto("1", "自动解锁"),
    Manual("2", "人工解锁");

    private String code;

    private String method;

    UnlockMethodEnum(String code, String method) {
        this.code = code;
        this.method = method;
    }

    public static class ChoiceFetch implements ChoiceFetchHandler {
        @Override
        public List<VLModel> fetch(String[] params) {
            return Stream.of(UnlockMethodEnum.values()).map(methodEnum ->
                    new VLModel(methodEnum.getCode(), methodEnum.getMethod())).collect(Collectors.toList());
        }

    }
}
