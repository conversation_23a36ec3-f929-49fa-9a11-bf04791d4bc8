package cec.jiutian.bc.ecs.config;

import lombok.Data;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Data
public class EcsMessageRabbitMQConfig {

    @Value("${mq.queue.ecs}")
    private String ecsQueueName;
    @Value("${mq.queue.group}")
    private String groupQueueName;
    @Value("${mq.queue.person}")
    private String personQueueName;
    @Value("${mq.queue.exchange.ecs}")
    private String exchange;
    @Value("${mq.queue.alarm:ecsAlarmDest-dev}")
    private String alarmQueueName;
    @Value("${mq.queue.role:ecsRoleDest-dev}")
    private String roleQueueName;
    @Bean
    public Queue ecsQueue() {
        return new Queue(getEcsQueueName(), false);
    }

    @Bean
    public Queue groupQueue() {
        return new Queue(getGroupQueueName(), false);
    }

    @Bean
    public Queue personQueue() {
        return new Queue(getPersonQueueName(), false);
    }

    @Bean
    public Queue alarmQueue() {
        return new Queue(getAlarmQueueName(), false);
    }

    @Bean
    public Queue roleQueue() {
        return new Queue(getRoleQueueName(), false);
    }


    @Bean
    TopicExchange exchange() {
        return new TopicExchange(getExchange());
    }

    @Bean
    Binding bindingEcsQueue(Queue ecsQueue, TopicExchange exchange) {
        return BindingBuilder.bind(ecsQueue).to(exchange).with(getExchange());
    }

    @Bean
    Binding bindingGroupQueue(Queue groupQueue, TopicExchange exchange) {
        return BindingBuilder.bind(groupQueue).to(exchange).with(groupQueueName);
    }

    @Bean
    Binding bindingPersonQueue(Queue personQueue, TopicExchange exchange) {
        return BindingBuilder.bind(personQueue).to(exchange).with(personQueueName);
    }

    @Bean
    Binding bindingAlarmQueue(Queue alarmQueue, TopicExchange exchange) {
        return BindingBuilder.bind(alarmQueue).to(exchange).with(alarmQueueName);
    }

    @Bean
    Binding bindingRoleQueue(Queue roleQueue, TopicExchange exchange) {
        return BindingBuilder.bind(roleQueue).to(exchange).with(roleQueueName);
    }

}
