/**
 * 领域的北向接口。该层用于承接外部向领域层发起的请求。<br/>
 * <p>local：应用服务层，对应源代码架构中的businessService层。该层分为command和query两种service。command服务主要执行业务的增删改和提供简单的查询逻辑。
 * 对于复杂的查询逻辑，如跨领域的查询或报表类查询，应当由query service进行处理。</p>
 * remote：包含controller层，对应原代码架构的controller；provider层，对应原代码架构的remote api；subscriber层，用于处理事件和消息，对应原代码架构的message层。<br/>
 *
 * message：消息契约，代表原有的DTO层。根据北向和南向进行了区分。
 * <AUTHOR>
 * @date 2024/3/21
 */
package cec.jiutian.bc.ecs.inbound;