package cec.jiutian.bc.generalModeler.domain.warehouseArea.model;


import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.type.RowBaseOperation;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

@FabosJson(
        name = "仓库详情",
        orderBy = "WarehouseDetail.createTime desc",
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "1==1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        }
)
@Table(name = "mos_warehouse_detail",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"warehouseRelationId", "materialCategoryId"})
        }
)
@Entity
@Getter
@Setter
public class WarehouseDetail extends MetaModel {

    @FabosJsonField(
            views = {
                    @View(title = "仓库编码", column = "code", show = false)
            },
            edit = @Edit(title = "仓库编码", type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "code")
            )
    )
    @ManyToOne
    @JsonIgnoreProperties("warehouseDetails")
    private Warehouse warehouse;

    @FabosJsonField(
            views = @View(title = "物料分类主键", show = false),
            edit = @Edit(title = "物料分类主键", show = false)
    )
    private String materialCategoryId;

    @FabosJsonField(
            views = @View(title = "仓库Id", show = false),
            edit = @Edit(title = "仓库Id", readonly = @Readonly(), show = false)
    )
    private String warehouseRelationId;

    @FabosJsonField(
            views = @View(title = "分类编码"),
            edit = @Edit(title = "分类编码", readonly = @Readonly())
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "分类名称"),
            edit = @Edit(title = "分类名称", readonly = @Readonly())
    )
    private String name;

}
