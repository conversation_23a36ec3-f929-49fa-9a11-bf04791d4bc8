//package cec.jiutian.bc.urm.domain.test;
//
//import cec.jiutian.bc.urm.domain.menu.entity.JumpMenuView;
//import cec.jiutian.bc.urm.domain.role.entity.Role;
//import cec.jiutian.bc.urm.domain.user.entity.User;
//import cec.jiutian.core.data.annotation.LinkTable;
//import cec.jiutian.core.frame.module.MetaModel;
//import cec.jiutian.core.view.fabosJson.FabosJson;
//import cec.jiutian.view.FabosJsonField;
//import cec.jiutian.view.FabosJsonI18n;
//import cec.jiutian.view.ReferenceAddType;
//import cec.jiutian.view.ReferenceTreeType;
//import cec.jiutian.view.field.Edit;
//import cec.jiutian.view.field.EditType;
//import cec.jiutian.view.field.View;
//import cec.jiutian.view.field.ViewType;
//import cec.jiutian.view.field.edit.DependFieldDisplay;
//import cec.jiutian.view.field.edit.InputType;
//import cec.jiutian.view.field.edit.ReferenceTableType;
//import cec.jiutian.view.field.edit.Search;
//import jakarta.persistence.*;
//import lombok.Getter;
//import lombok.Setter;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @description: 测试代码
// */
//@Entity
//@LinkTable
//@Table(name = "test_reference")
//@Getter
//@Setter
//@FabosJsonI18n
//@FabosJson(name = "测试"
//)
//public class TestReferenceTableTypeAndTreeType extends MetaModel {
//
//    @FabosJsonField(
//            views = @View(title = "名称"),
//            edit = @Edit(title = "名称",
//                    search = @Search,
//                    inputType = @InputType(length = 64)
//            )
//    )
//    private String name;
//
//    @ManyToOne
//    @FabosJsonField(
//            views = @View(title = "角色", column = "name"),
//            edit = @Edit(title = "角色", notNull = true,
//                    type = EditType.REFERENCE_TABLE,
//                    referenceTableType = @ReferenceTableType(id = "id", label = "roleCode",queryModelClass = "UserRoleDTO")
//                    ,
//                    queryModelCondition = "{\"ABC\": [\"normal\", \"extended\", \"unqualified\", \"scrap\"]}"
//            )
//    )
//    private Role role;
//
//    @ManyToOne
//    @FabosJsonField(
//            views = @View(title = "跳转菜单", column = "name"),
//            edit = @Edit(title = "跳转菜单", type = EditType.REFERENCE_TREE
//            ,referenceTreeType = @ReferenceTreeType(queryModelClass = "Menu"))
//    )
//    private User user;
//
//}
