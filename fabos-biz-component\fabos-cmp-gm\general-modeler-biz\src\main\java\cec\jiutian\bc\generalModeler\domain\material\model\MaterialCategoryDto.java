package cec.jiutian.bc.generalModeler.domain.material.model;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.config.Comment;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import jakarta.persistence.Entity;
import jakarta.persistence.OneToMany;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


@FabosJson(
        name = "物料分类DTO"
)
@Getter
@Setter
@Entity
public class MaterialCategoryDto extends MetaModel {

    @FabosJsonField(
            views = @View(title = "分类代码"),
            edit = @Edit(title = "分类代码", readonly = @Readonly(add = false, edit = true), notNull = true)
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "分类名称"),
            edit = @Edit(title = "分类名称", notNull = true, readonly = @Readonly(add = false, edit = true))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "显示顺序"),
            edit = @Edit(title = "显示顺序", numberType = @NumberType(min = 1))
    )
    private Integer sort;

    @FabosJsonField(
            edit = @Edit(
                    title = "上级分类ID"
            )
    )
    private String parentCategoryId;

    @OneToMany
//    @JsonIgnoreProperties({"component", "actions", "functions", "fields"})
//    @JoinColumn(name = "parent_category_id")
    @FabosJsonField(
            views = @View(title = "子级分类", type = ViewType.TABLE_NESTED),
            edit = @Edit(
                    title = "子级分类"
            )
    )
    private List<MaterialCategoryDto> materialCategoryDtos;


    @FabosJsonField(
            edit = @Edit(title = "分类级别", show = false),
            views = @View(title = "分类级别", show = true)
    )
    @Comment("逻辑字段，生成的当前类别的级别")
    private Integer categoryLevel;

    @FabosJsonField(
            edit = @Edit(title = "描述", notNull = false,
                    type = EditType.TEXTAREA, inputType = @InputType(length = 400))
    )
    private String desciption;


}
