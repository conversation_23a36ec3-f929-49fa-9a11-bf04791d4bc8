package cec.jiutian.bc.urm.inbound.remote.controller;

import cec.jiutian.bc.urm.inbound.local.service.command.RowColumnPermissionService;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import cec.jiutian.core.view.fabosJson.view.FabosJsonApiModel;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/fabos-cmp-urm"+ FabosJsonRestPath.FABOS_API)
public class ColumnPermissionController {

    @Resource
    private RowColumnPermissionService permissionService;

    @PostMapping("/getColumnPermission")
    public FabosJsonApiModel getColumnPermission(@RequestBody List<String> metadataIds){
        Map<String, Integer> permission = permissionService.getColumnPermissionFromCache(metadataIds);
        return FabosJsonApiModel.successApi(permission);
    }

}
