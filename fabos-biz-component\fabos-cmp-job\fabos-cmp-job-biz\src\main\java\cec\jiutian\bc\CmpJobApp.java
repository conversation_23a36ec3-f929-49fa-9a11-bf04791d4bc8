package cec.jiutian.bc;

import cec.jiutian.core.frame.annotation.FabosJsonScan;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.core.io.support.PropertiesLoaderUtils;

import java.io.IOException;
import java.util.Properties;

@Slf4j
@SpringBootApplication(scanBasePackages = {
        "cec.jiutian"
})
@ComponentScan(basePackages = {
        "cec.jiutian"
})
@FabosJsonScan(value = {
        "cec.jiutian"
})
//@EnableJpaRepositories(basePackages = "cec.jiutian")
@EntityScan(basePackages = {
        "cec.jiutian"
})
//@EnableDubbo
public class CmpJobApp {
    private static final Logger LOGGER = LoggerFactory.getLogger(CmpJobApp.class);

    public static void main(String[] args) throws IOException {
        Properties properties = PropertiesLoaderUtils.loadAllProperties("application.properties");
        properties.forEach((k, v) -> MDC.put(k.toString(), v.toString()));
        ClassLoader systemClassLoader = ClassLoader.getSystemClassLoader();
        ClassLoader classLoader = CmpJobApp.class.getClassLoader();
        System.out.println(classLoader.equals(systemClassLoader));
        System.out.println("类加载器名称:" + classLoader.getName());
        SpringApplication.run(CmpJobApp.class, args);

        LOGGER.info("BaseApplication start!");
        LOGGER.info("Spring Boot Version: "
                + SpringApplication.class.getPackage().getImplementationVersion());
        LOGGER.info("BaseApplication classLoader: " + CmpJobApp.class.getClassLoader());
    }
}
