package cec.jiutian.bc.urm.outbound.port.client;

import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * <AUTHOR>
 * @time 2025-03-14 16:42
 */

@FeignClient(name = "xlx-ubp", contextId = "ubpFeignClient")
// 本地测试可以改成如下配置，以使用服务器上的ubp服务
//@FeignClient(name = "xlx-ubp", url = "http://172.16.200.95:8080/xlx-ubp/")
public interface UbpFeignClient {
    public static final String OAUTH2_PREFIX = "/oauth2";
    public static final String JWK_ENDPOINT = OAUTH2_PREFIX + "/jwks";
    public static final String SYSTEM_AVAILABLE_CHECK = FabosJsonRestPath.FABOS_REMOTE_API + "/checkIfSystemAvailableForUser";

    @GetMapping(JWK_ENDPOINT)
    String fetchJwks();

    @PostMapping(SYSTEM_AVAILABLE_CHECK)
    Boolean  checkIfSystemAvailableForUser(@RequestBody Map<String, String> params);

}
