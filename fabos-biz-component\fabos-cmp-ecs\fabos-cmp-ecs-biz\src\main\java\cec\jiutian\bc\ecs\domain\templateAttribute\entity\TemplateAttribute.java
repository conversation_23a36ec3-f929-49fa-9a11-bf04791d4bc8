package cec.jiutian.bc.ecs.domain.templateAttribute.entity;

import cec.jiutian.bc.ecs.domain.template.entity.Template;
import cec.jiutian.bc.ecs.domain.templateAttribute.event.TemplateAttributeDataProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.SubTableField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@Entity
@Table(name = "ecs_template_attribute",
        indexes = {@Index(columnList = "attribute_name,oid", unique = true)})
@FabosJson(name = "消息模板属性", dataProxy = {TemplateAttributeDataProxy.class},
        orderBy = "TemplateAttribute.orderNumber asc,TemplateAttribute.createTime desc")
public class TemplateAttribute extends MetaModel implements Serializable {

    @FabosJsonField(
            views = @View(title = "属性序号", sortable = true),
            edit = @Edit(title = "属性序号", notNull = true,
                    numberType = @NumberType(min = 1, max = 9999), search = @Search(value = false))
    )
    @SubTableField
    private Integer orderNumber;

    @FabosJsonField(
            views = @View(title = "属性名称"),
            edit = @Edit(title = "属性名称", notNull = true, search = @Search(vague = true))
    )
    @SubTableField
    private String attributeName;

    @FabosJsonField(
            views = @View(title = "关联属性"),
            edit = @Edit(title = "关联属性", search = @Search(vague = true))
    )
    @SubTableField
    private String connectAttribute;

    @FabosJsonField(
            views = @View(title = "发送字段默认值"),
            edit = @Edit(title = "发送字段默认值", notNull = true, search = @Search(value = false))
    )
    @SubTableField
    private String defaultValue;

    @FabosJsonField(
            views = @View(title = "备注", show = false),
            edit = @Edit(title = "备注", show = false)
    )

    @Column(name = "LST_EVNT_CMNT")
    private String lastEventComment;

    @ManyToOne
    @JoinColumn(name = "template_id")
    @FabosJsonField(
            views = @View(title = "模板名称", column = "name"),
            edit = @Edit(title = "模板名称",
                    notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"))
    )
    private Template template;

}
