package cec.jiutian.bc.urm.domain.openApi.entity;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @time 2025-01-13 09:50
 */


@Entity
@Table(name = "fd_open_api_resources")
@FabosJson(
        name = "Open API",
        formColumnElements = 1
)
@Getter
@Setter
public class FabosJsonOpenApiResources extends MetaModel {

    @FabosJsonField(
            views = @View(title = "接口路径"),
            edit = @Edit(title = "接口路径", notNull = true, search = @Search(vague = true), tips = "接口路径需要以\"/\"开头")
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注", tips = "对接口的自定义描述")
    )
    private String remark;


}