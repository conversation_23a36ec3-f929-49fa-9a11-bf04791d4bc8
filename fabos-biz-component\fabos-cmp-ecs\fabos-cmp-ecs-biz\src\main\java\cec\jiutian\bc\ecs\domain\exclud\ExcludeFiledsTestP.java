//package cec.jiutian.bc.ecs.domain.exclud;
//
//import cec.jiutian.core.frame.module.BaseModel;
//import cec.jiutian.core.view.fabosJson.FabosJson;
//import cec.jiutian.view.FabosJsonField;
//import cec.jiutian.view.FabosJsonI18n;
//import cec.jiutian.view.field.Edit;
//import cec.jiutian.view.field.View;
//import cec.jiutian.view.field.edit.Search;
//import cec.jiutian.view.type.Power;
//import jakarta.persistence.Entity;
//import jakarta.persistence.Table;
//import lombok.Getter;
//import lombok.Setter;
//
//@Entity
//@Table(name = "ecs_exclude_fields")
//@Getter
//@Setter
//@FabosJson(name = "字段继承测试",
//        orderBy = "ExcludeFiledsTestP.createTime desc",
//        power = @Power(add = false, edit = false, delete = false, export = true, importable = false, viewDetails = false)
//)
//@FabosJsonI18n
//public class ExcludeFiledsTestP extends BaseModel {
//
//    @FabosJsonField(
//            views = @View(title = "姓名"),
//            edit = @Edit(title = "姓名", search = @Search(vague = true))
//    )
//    private String name;
//
//
//    @FabosJsonField(
//            views = @View(title = "年龄"),
//            edit = @Edit(title = "年龄", search = @Search(vague = true))
//    )
//    private Integer age;
//
//
//    @FabosJsonField(
//            views = @View(title = "性别"),
//            edit = @Edit(title = "性别", search = @Search(vague = true))
//    )
//    private String sex;
//
//    @FabosJsonField(
//            views = @View(title = "地址"),
//            edit = @Edit(title = "地址", search = @Search(vague = true))
//    )
//    private String address;
//}
