package cec.jiutian.bc.ecs.domain.messagegroup.event;

import cec.jiutian.bc.ecs.domain.messagegroup.entity.WeixinGroup;
import cec.jiutian.bc.infra.port.client.WeixinClient;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> @description:
 */
@Service
public class WeixinGroupDataProxy implements DataProxy<WeixinGroup> {

    private final WeixinClient weixinClient;

    private final FabosJsonDao fabosJsonDao;

    public WeixinGroupDataProxy(WeixinClient weixinClient, FabosJsonDao fabosJsonDao) {
        this.weixinClient = weixinClient;
        this.fabosJsonDao = fabosJsonDao;
    }

    @Override
    public void beforeAdd(WeixinGroup weixinGroup) {
        String chatId = "";
        try {
            // 在创建前，先调用微信接口创建该群
            chatId = weixinClient.weixinCreateGroup(weixinGroup.getName(),
                    weixinGroup.getManagerId(),
                    weixinGroup.getMemberIds());
        } catch (Exception e) {
            throw new FabosJsonApiErrorTip("创建微信群失败: " + e);
        }
        weixinGroup.setChatId(chatId);
    }

    @Override
    public void beforeUpdate(WeixinGroup weixinGroup) {
        // 如果群组名称或成员发生变化，则需要调用微信接口更新该群
        WeixinGroup oldWeixinGroup = fabosJsonDao.findById(WeixinGroup.class, weixinGroup.getId());
        if (!oldWeixinGroup.getName().equals(weixinGroup.getName())
                || !oldWeixinGroup.getManagerId().equals(weixinGroup.getManagerId())
                || !oldWeixinGroup.getMemberIds().equals(weixinGroup.getMemberIds())) {
            try {
                // 获取人员变化list
                List<String> oldList = oldWeixinGroup.getMemberIds() != null ? Arrays.asList(oldWeixinGroup.getMemberIds().split(",")) : Collections.emptyList();
                List<String> newList = weixinGroup.getMemberIds() != null ? Arrays.asList(weixinGroup.getMemberIds().split(",")) : Collections.emptyList();
                List<String> add_list = new ArrayList<>(newList);
                add_list.removeAll(oldList);
                List<String> del_list = new ArrayList<>(oldList);
                del_list.removeAll(newList);
                // 调用微信接口更新群组
                weixinClient.weixinUpdateGroup(
                        weixinGroup.getChatId(),
                        weixinGroup.getName(),
                        weixinGroup.getManagerId(),
                        add_list,
                        del_list);
            } catch (Exception e) {
                throw new FabosJsonApiErrorTip("更新微信群失败: " + e);
            }
        }

    }
}
