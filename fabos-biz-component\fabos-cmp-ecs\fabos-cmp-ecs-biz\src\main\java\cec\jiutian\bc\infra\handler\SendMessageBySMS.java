package cec.jiutian.bc.infra.handler;

import cec.jiutian.bc.ecs.domain.messagerecord.entity.MessageRecord;
import cec.jiutian.bc.infra.util.SmsSendUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date ：2023/5/22 17:47
 * @description：
 */
@Slf4j
@Component
public class SendMessageBySMS implements MessageHandler {

    private static SmsSendUtil smsSendUtil;

    @Resource
    public void setSmsSendUtil(SmsSendUtil smsSendUtil){
        SendMessageBySMS.smsSendUtil = smsSendUtil;
    }


    @Override
    public void sendMessage(MessageRecord createDTO) {
        log.warn("短信发送未作实现");
// 根据接收消息用户查询用户列表
//        List<MetaUserinfo> users = createDTO.getUserDTOList();
//
//        for (MetaUserinfo user : users) {
//            if (StringUtils.isNotEmpty(user.getPhoneNumber())){
//                int result = smsSendUtil.send(user.getPhoneNumber(),createDTO.getContent());
//                System.out.print(smsSendUtil.getMessage(result));
//            }
//        }
    }
}
