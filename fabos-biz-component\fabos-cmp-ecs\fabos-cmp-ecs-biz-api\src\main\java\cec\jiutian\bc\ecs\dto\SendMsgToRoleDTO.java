package cec.jiutian.bc.ecs.dto;

import cec.jiutian.bc.ecs.enums.MessageWayEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@Data
@Slf4j
public class SendMsgToRoleDTO implements Serializable {

    private String title;

    private String content;

    private String sendBy;

    /**
     * 消息类型
     */
    private MessageWayEnum way;

    /**
     * 角色ID
     */
    private Set<String> roles;

    public static boolean argsCheck(SendMsgToRoleDTO SendMsgToRoleDTO) {
        if (SendMsgToRoleDTO == null) {
            log.error("参数不能为空");
            return false;
        }
        if (SendMsgToRoleDTO.getWay() == null) {
            log.error("消息类型不能为空");
            return false;
        }
        if (SendMsgToRoleDTO.getRoles() == null || SendMsgToRoleDTO.getRoles().size() == 0) {
            log.error("接收人列表不能为空");
            return false;
        }
        if (StringUtils.isEmpty(SendMsgToRoleDTO.getTitle())) {
            log.error("标题不能为空");
            return false;
        }
        if (StringUtils.isEmpty(SendMsgToRoleDTO.getContent())) {
            log.error("内容不能为空");
            return false;
        }
        return true;
    }

}
