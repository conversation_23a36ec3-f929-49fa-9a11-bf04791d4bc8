package cec.jiutian.fc.ie.util;

import org.apache.poi.ss.usermodel.*;

import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @description:
 */
public class ExcelImpUtil {
    public static List<Map<String, Object>> parseSheetToJavaClassList(Sheet sheet){
        List<Map<String, Object>> dataList = new ArrayList<>();

        // 获取表头
        Row headerRow = sheet.getRow(0);
        if (headerRow == null) {
            throw new IllegalArgumentException("Sheet does not contain header row.");
        }
        // 获取列名
        List<String> columnNames = new ArrayList<>();
        for (Cell cell : headerRow) {
            columnNames.add(cell.getStringCellValue());
        }

        // 遍历数据行
        for (Row row : sheet) {
            if (row.getRowNum() == 0) continue; // 跳过表头行

            // 检查是否为空行
            boolean isRowEmpty = true;
            for (Cell cell : row) {
                if (cell != null && !isCellEmpty(cell)) {
                    isRowEmpty = false;
                    break;
                }
            }
            if (isRowEmpty) {
                continue; // 跳过空行
            }
            Map<String, Object> dataMap = new LinkedHashMap<>();
            for (int i = 0; i < columnNames.size(); i++) {
                Cell cell = row.getCell(i);
                if (cell == null) continue;

                String columnName = columnNames.get(i);
                Object cellValue = getCellValue(cell);
                dataMap.put(columnName, cellValue);
            }
            dataList.add(dataMap);
        }
        return dataList;
    }

    private static Object getCellValue(Cell cell) {
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue();
                } else {
                    double numericValue = cell.getNumericCellValue();
                    return convertToIntegerIfPossible(numericValue); // 关键优化
                }
            case BOOLEAN:
                return cell.getBooleanCellValue();
            case FORMULA:
                return cell.getCellFormula();
            default:
                return null;
        }
    }
    // 核心优化：将整数数值转换为Integer/Long类型
    private static Object convertToIntegerIfPossible(double numericValue) {
        // 检查是否为整数（无小数部分）
        if (numericValue == Math.floor(numericValue)) {
            // 根据数值范围返回Integer或Long
            if (numericValue >= Integer.MIN_VALUE && numericValue <= Integer.MAX_VALUE) {
                return (int) numericValue;
            } else if (numericValue >= Long.MIN_VALUE && numericValue <= Long.MAX_VALUE) {
                return (long) numericValue;
            }
        }
        // 非整数或超出范围时返回原始double
        return numericValue;
    }
    private static boolean isCellEmpty(Cell cell) {
        return cell.getCellType() == CellType.BLANK ||
                (cell.getCellType() == CellType.STRING && cell.getStringCellValue().trim().isEmpty());
    }


    //获取表头信息
    public static List<String> getHeaderRow(Sheet sheet) {
        List<String> headerRow = new ArrayList<>();
        Row row = sheet.getRow(0);
        if (row != null) {
            for (Cell cell : row) {
                headerRow.add(cell.getStringCellValue());
            }
        }
        return headerRow;
    }
}