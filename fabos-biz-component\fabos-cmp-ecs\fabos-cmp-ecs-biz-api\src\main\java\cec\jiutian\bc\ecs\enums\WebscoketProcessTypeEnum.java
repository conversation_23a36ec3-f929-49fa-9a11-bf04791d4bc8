package cec.jiutian.bc.ecs.enums;


import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
public enum WebscoketProcessTypeEnum {

    ImmediateAlarm("即时告警", "ImmediateAlarm");


    private final String name;

    private final String code;

    WebscoketProcessTypeEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public static class ChoiceFetch implements ChoiceFetchHandler {
        @Override
        public List<VLModel> fetch(String[] params) {
            return Stream.of(WebscoketProcessTypeEnum.values())
                    .map(WebscoketProcessTypeEnum -> new VLModel(WebscoketProcessTypeEnum.getCode(), WebscoketProcessTypeEnum.getName()))
                    .collect(Collectors.toList());

        }

    }
}
