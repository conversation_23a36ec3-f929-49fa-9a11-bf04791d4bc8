package cec.jiutian.bc.ecs.provider;

import cec.jiutian.bc.ecs.config.EcsMessageRabbitMQConfig;
import cec.jiutian.bc.ecs.dto.SendAlarmMessageDTO;
import cec.jiutian.bc.ecs.dto.SendMsgGroupDTO;
import cec.jiutian.bc.ecs.dto.SendMsgToPersonDTO;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.common.util.JacksonUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class EcsMessageProvider {

    @Resource
    private AmqpTemplate rabbitTemplate;

    @Resource
    private EcsMessageRabbitMQConfig ecsMessageRabbitMQConfig;


    public void sendGroupMessage(SendMsgGroupDTO sendMsgGroupDTO) {
        String message = null;
        try {
            message = JacksonUtil.toJson(sendMsgGroupDTO);
        } catch (JsonProcessingException e) {
            throw new ServiceException(e);
        }
        rabbitTemplate.convertAndSend(ecsMessageRabbitMQConfig.getGroupQueueName(), message);
    }

    public void sendPersonMessage(SendMsgToPersonDTO sendMsgToPersonDTO) {
        String message = null;
        try {
            message = JacksonUtil.toJson(sendMsgToPersonDTO);
        } catch (JsonProcessingException e) {
            throw new ServiceException(e);
        }
        rabbitTemplate.convertAndSend(ecsMessageRabbitMQConfig.getPersonQueueName(), message);
    }

    public void sendImmediateAlarm(SendAlarmMessageDTO sendAlarmMessageDTO) {
        String message = null;
        try {
            message = JacksonUtil.toJson(sendAlarmMessageDTO);
        } catch (JsonProcessingException e) {
            throw new ServiceException(e);
        }
        rabbitTemplate.convertAndSend(ecsMessageRabbitMQConfig.getAlarmQueueName(), message);
    }
}
