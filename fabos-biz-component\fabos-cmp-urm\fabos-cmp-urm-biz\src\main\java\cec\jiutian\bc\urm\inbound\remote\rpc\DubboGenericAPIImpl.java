//package cec.jiutian.bc.urm.inbound.remote.rpc;
//
//import cec.jiutian.functionexecutor.consumer.DubboGenericAPI;
//import cec.jiutian.functionexecutor.dto.InvokeDTO;
//import org.springframework.stereotype.Service;
//
//@Service
//public class DubboGenericAPIImpl extends DubboGenericAPI {
//
//    @Override
//    public Object doInvoke(InvokeDTO invokeDTO) {
//        return super.functionExecutor.doInvoke(invokeDTO);
//    }
//}
