package cec.jiutian.bc.generalModeler.domain.warehouseArea.handler;

import cec.jiutian.bc.generalModeler.domain.warehouseArea.model.WarehouseBlock;
import cec.jiutian.bc.generalModeler.domain.warehouseArea.model.WarehouseBlockDetail;
import cec.jiutian.bc.generalModeler.port.dto.MaterialDTO;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.ReferenceAddType;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：2024/11/18 17:35
 * @description：
 */
@Component
public class WarehouseBlockReferenceAddHandler implements ReferenceAddType.ReferenceAddHandler<WarehouseBlock, MaterialDTO> {

    @Resource
    private JpaCrud jpaCrud;

    @Override
    public Map<String, Object> handle(WarehouseBlock warehouseBlock, List<MaterialDTO> materialList) {
        Map<String, Object> result = new HashMap<>();
        List<WarehouseBlockDetail> warehouseBlockDetails = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(materialList)) {
            materialList.forEach(c -> {
                WarehouseBlockDetail warehouseBlockDetail = new WarehouseBlockDetail();
                warehouseBlockDetail.setMaterialId(c.getId());
                warehouseBlockDetail.setMaterialCode(c.getMaterialCode());
                warehouseBlockDetail.setName(c.getMaterialName());
                warehouseBlockDetail.setMaterialSpecification(c.getMaterialSpecification());
                warehouseBlockDetail.setMaterialCategory(c.getMaterialCategory());
                warehouseBlockDetails.add(warehouseBlockDetail);
            });
            result.put("warehouseBlockDetails", warehouseBlockDetails);
        }
        return result;
    }
}
