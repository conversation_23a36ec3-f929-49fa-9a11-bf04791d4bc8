package cec.jiutian.bc.generalModeler.domain.measureUnit.model;

import cec.jiutian.bc.generalModeler.domain.measureUnit.proxy.MeasureUnitDataProxy;
import cec.jiutian.bc.urm.domain.dictionary.handler.DictChoiceFetchHandler;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date ：2024/10/24 16:54
 * @description：
 */
@Table(name = "measure_unit")
@FabosJson(
        name = "计量单位",
        dataProxy = MeasureUnitDataProxy.class
)
@Entity
@Getter
@Setter
public class MeasureUnit extends MetaModel {

    @FabosJsonField(
            views = @View(title = "单位分类"),
            edit = @Edit(title = "单位分类", notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = DictChoiceFetchHandler.class,
                            fetchHandlerParams = "JLDW04"),
                    search = @Search(vague = true)
            )
    )
    private String unitType;

    @FabosJsonField(
            views = @View(title = "单位中文名称"),
            edit = @Edit(title = "单位中文名称", notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 20))
    )
    private String unitChnName;

    @FabosJsonField(
            views = @View(title = "单位英文名称"),
            edit = @Edit(title = "单位英文名称", notNull = true,
                    search = @Search(vague = true),
                    inputType = @InputType(length = 20, regex = "^[A-Za-z]+$"))
    )
    private String unitEngName;
}
