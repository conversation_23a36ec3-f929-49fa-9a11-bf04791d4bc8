package cec.jiutian.bc.ecs.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2023/5/24
 */
@Data
@ApiModel("ConfirmMessageDTO")
public class ConfirmMessageDTO {

    /**
     * secret
     */
    @NotNull
    @ApiModelProperty("secret")
    private String secret;

    /**
     * 操作类型(确认接收-receive/确认处理-handle)
     */
    @NotNull
    @ApiModelProperty("操作类型(确认接收-receive/确认处理-handle)")
    private String opType;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String lastEventComment;

}
