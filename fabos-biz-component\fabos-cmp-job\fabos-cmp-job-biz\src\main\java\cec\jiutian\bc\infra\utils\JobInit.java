package cec.jiutian.bc.infra.utils;

import cec.jiutian.bc.job.domain.job.entity.JobInfo;
import cec.jiutian.bc.job.domain.job.service.JobService;
import cec.jiutian.component.api.FabosComponentInitEvent;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.quartz.SchedulerException;
import org.springframework.stereotype.Service;

import java.text.ParseException;

@Service
@Slf4j
public class JobInit implements FabosComponentInitEvent {

    @Resource
    private FabosJsonDao fabosJsonDao;

    @Resource
    private JobService jobService;

    @Override
    public void init() {
        for (JobInfo job : fabosJsonDao.lambdaQuery(JobInfo.class).eq(JobInfo::getStatus, true).list()) {
            try {
                log.info("start init job :" + job.getName());
                jobService.modifyJob(job);
            } catch (SchedulerException e) {
                throw new RuntimeException(e);
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        }
    }
}
