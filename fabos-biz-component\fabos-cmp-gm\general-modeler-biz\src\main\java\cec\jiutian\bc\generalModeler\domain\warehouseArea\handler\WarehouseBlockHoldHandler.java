package cec.jiutian.bc.generalModeler.domain.warehouseArea.handler;

import cec.jiutian.bc.generalModeler.domain.warehouseArea.model.WarehouseBlock;
import cec.jiutian.bc.generalModeler.enumeration.WarehouseStateEnum;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class WarehouseBlockHoldHandler implements OperationHandler<WarehouseBlock, Void> {

    @Resource
    private JpaCrud jpaCrud;

    @Override
    public String exec(List<WarehouseBlock> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            WarehouseBlock warehouseBlock = data.get(0);
            warehouseBlock.setLockState(WarehouseStateEnum.Enum.Locked.name());
            jpaCrud.update(warehouseBlock);
        }
        return "msg.success('操作成功')";
    }
}
