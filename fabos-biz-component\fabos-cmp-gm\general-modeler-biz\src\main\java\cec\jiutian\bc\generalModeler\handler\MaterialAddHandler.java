package cec.jiutian.bc.generalModeler.handler;

import cec.jiutian.bc.generalModeler.domain.material.model.Material;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.view.fun.OperationHandler;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MaterialAddHandler implements OperationHandler<Material, Material> {

    @Override
    public Material fabosJsonFormValue(List<Material> data, Material fabosJsonForm, String[] param) {
        MetaUser currentUser = new MetaUser(UserContext.get());
        Material material = new Material();
        material.setStoreKeeper(currentUser);
        return material;
    }
}
