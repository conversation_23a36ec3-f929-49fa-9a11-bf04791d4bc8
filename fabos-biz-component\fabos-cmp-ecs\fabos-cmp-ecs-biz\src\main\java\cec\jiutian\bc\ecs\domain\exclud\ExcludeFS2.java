//package cec.jiutian.bc.ecs.domain.exclud;
//
//import cec.jiutian.core.view.fabosJson.FabosJson;
//import cec.jiutian.view.FabosJsonI18n;
//import cec.jiutian.view.type.Power;
//import jakarta.persistence.Entity;
//import jakarta.persistence.Table;
//import lombok.Getter;
//import lombok.Setter;
//
//@Entity
//@Table(name = "ecs_exclude_fields")
//@Getter
//@Setter
//@FabosJson(name = "字段继承测试",
//        orderBy = "ExcludeFS2.createTime desc",
//        power = @Power(add = false, edit = false, delete = false, export = true, importable = false, viewDetails = false))
//@FabosJsonI18n
//public class ExcludeFS2 extends ExcludeFiledsTestP {
//
//    private String test;
//    private String test2;
//}
