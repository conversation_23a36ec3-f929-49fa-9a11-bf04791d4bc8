package cec.jiutian.bc.urm.domain.user.event;

import cec.jiutian.bc.urm.domain.user.entity.LockedUser;
import cec.jiutian.common.constant.SessionKey;
import cec.jiutian.core.frame.constant.YesOrNoStatus;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.service.FabosJsonSessionService;
import cec.jiutian.data.redis.utils.BaseRedisCacheUtil;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Component
public class LockedUserDataProxy implements DataProxy<LockedUser> {

    @Resource
    private BaseRedisCacheUtil redisUtil;

    @Resource
    private FabosJsonSessionService fabosJsonSessionService;

    @Resource
    private FabosJsonDao fabosJsonDao;


    @Override
    public void afterFetch(Collection<Map<String, Object>> list) {
        // 在查询结果中，判断是否存在自动锁定的用户，如果存在，修改说明，提示该用户被自动锁定
        // 查询redis中的锁定用户
        Set<String> ids = redisUtil.getCacheKeys(SessionKey.DISABLE_LOGIN_ACCOUNT + "*");

        // 如果ids为空或长度为0，则表示无锁定用户，跳过
        if (ids == null || ids.isEmpty()) {
            DataProxy.super.afterFetch(list);
        }

        // 构建锁定用户的手机号集合
        Set<String> phoneNumbers = new HashSet<>();
        int i = 0;
        for (String id : ids) {
            String[] strings = id.split(":");
            id = strings[strings.length - 1].replace(" ", "");
            phoneNumbers.add(id);
            i++;
        }
        // 由MetaUser中根据phoneNumber查询锁定用户
        List<MetaUser> metaUsers = fabosJsonDao.queryEntityList(MetaUser.class, "phoneNumber in (:phoneNumber)", new HashMap<String, Object>(1) {{
            this.put("phoneNumber", StringUtils.join(phoneNumbers, ","));
        }});
        // 根据ID，判断metaUsers是否在list中存在，存在，则更新锁定状态和锁定原因
        for (Map<String, Object> map : list) {
            String id = (String) map.get("id");
            // 判断metaUsers中的ID是否包含当前id
            if (metaUsers.stream().anyMatch(metaUser -> metaUser.getId().equals(id))) {
                // 如果存在，则更新当前map中的锁定状态和锁定原因字段
                // 判断当前map中的lockedFlag值, 如果lockedFlag为N，则表示当前为自动解锁模式，需额外设置说明
                if (YesOrNoStatus.NO.getValue().equals(map.get("lockedFlag"))) {
                    // 使用metaUser中的phoneNumber，获取ids中的id
                    String phoneNumber = (String) map.get("phoneNumber");
                    // 获取锁定用户的ID
                    String lockedUserId = ids.stream()
                            .filter(key -> key.endsWith(phoneNumber))
                            .findFirst()
                            .orElse(null);
                    Integer expireMinutes = (int) (fabosJsonSessionService.getExpire(lockedUserId) / 60);
                    map.put("lockReason", String.format("由于身份鉴别失败次数过多，当前禁止登录, %d分钟后自动解锁。（当前为自动解锁模式，可手工解锁）", expireMinutes));
                }

            }
        }

        DataProxy.super.afterFetch(list);
    }

}
