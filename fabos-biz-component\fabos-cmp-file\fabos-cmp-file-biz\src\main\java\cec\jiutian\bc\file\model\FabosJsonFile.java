package cec.jiutian.bc.file.model;

import cec.jiutian.api.file.model.FabosJsonFileSuper;
import cec.jiutian.bc.file.proxy.FileProcessProxy;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @time 2025-05-09 15:06
 */


@Table(name = "fd_file")
@FabosJson(name = "附件管理"
        , power = @Power(add = false, edit = false, delete = true, importable = false, viewDetails = false)
        , orderBy = "FabosJsonFile.createTime desc"
        , dataProxy = FileProcessProxy.class
)
@Entity
@NoArgsConstructor
public class FabosJsonFile extends FabosJsonFileSuper {

    public FabosJsonFile (MultipartFile file) {
        super(file);
    }

    public FabosJsonFile (MultipartFile file, String filename) {
        super(file, filename);
    }

    public FabosJsonFile(FabosJsonFileSuper fabosJsonFileSuper) {
        BeanUtils.copyProperties(fabosJsonFileSuper, this);
    }
}
