package cec.jiutian.bc.generalModeler.domain.test.model;


import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
public class SplitModelp1Handler implements OperationHandler<SplitModelFull, SplitModelP1> {


    @Resource
    private cec.jiutian.core.frame.jpa.dao.FabosJsonDao FabosJsonDao;


    @Override
    public java.lang.String exec(List<SplitModelFull> data, SplitModelP1 modelObject, java.lang.String[] param) {
        return null;
    }
}

