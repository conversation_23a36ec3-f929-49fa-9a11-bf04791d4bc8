package cec.jiutian.bc.ecs.provider;

import cec.jiutian.bc.ecs.dto.MessageCreateDTO;
import cec.jiutian.bc.ecs.dto.NoticeCreateDTO;

public interface IMessageRequestProvider {

    public Boolean receiveMsg(MessageCreateDTO messageCreateDTO);

    public Boolean sendMsgByEmail(MessageCreateDTO messageCreateDTO);


    /**
     * 组件内部调用 发送通知公告
     * @param noticeCreateDTO
     * @return
     */
    public Boolean innerSendMsg(NoticeCreateDTO noticeCreateDTO);

    public boolean callBack(String noticeId);
}
