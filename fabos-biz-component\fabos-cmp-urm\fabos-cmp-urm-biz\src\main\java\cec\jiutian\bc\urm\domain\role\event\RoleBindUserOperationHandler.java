package cec.jiutian.bc.urm.domain.role.event;

import cec.jiutian.bc.urm.domain.role.entity.RoleAuthDistribute;
import cec.jiutian.bc.urm.domain.user.entity.User;
import cec.jiutian.bc.urm.domain.user.entity.UserByRoleView;
import cec.jiutian.bc.urm.domain.user.service.UserService;
import cec.jiutian.bc.urm.dto.MetaUserinfo;
import cec.jiutian.bc.urm.enums.UserManagerTypeEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.fc.log.domain.permissionOperationLog.dto.PermissionDTO;
import cec.jiutian.fc.log.domain.permissionOperationLog.manager.PermissionLogManager;
import cec.jiutian.fc.log.enums.PermissionTypeEnum;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class RoleBindUserOperationHandler implements OperationHandler<RoleAuthDistribute, UserByRoleView> {

    @Resource
    private UserService userService;
    @Resource
    private EntityManagerFactory entityManagerFactory;
    @Resource
    private FabosJsonDao fabosJsonDao;
    @Resource
    private PermissionLogManager permissionLogManager;
    @Override
    public String exec(List<RoleAuthDistribute> data, UserByRoleView modelObject, String[] param) {
        MetaUserinfo curr = userService.getSimpleUserInfo();
        if(curr==null){
            return "msg.error('current user is not login!')";
        }
//        if (!(curr.getRoles().contains(UserManagerTypeEnum.securityManager.getCode())||curr.getRoles().contains(UserManagerTypeEnum.superManager.getCode()))) {
//            return "msg.error(当前登录用户无操作权限')";
//        }
        String rId = data.get(0).getId();
        String sql1 = "select user_id from fd_user_role  where role_id = '" + rId +"'";
        List<String> userIdsDb = fabosJsonDao.getJdbcTemplate().query(sql1, (rs, rowNum) -> rs.getString("user_id"));

        EntityManager entityManager = entityManagerFactory.createEntityManager();
        try {
            entityManager.getTransaction().begin();
            if(param!=null){
                // 使用SQL进行删除操作
                String deletedSql = "DELETE FROM fd_user_role WHERE role_id = ?";
                Query dquery = entityManager.createNativeQuery(deletedSql);
                dquery.setParameter(1, rId); // 设置要删除记录的ID值
                dquery.executeUpdate(); // 执行删除操作
                for (String uId:param) {
                    String sql="INSERT INTO fd_user_role (user_id, role_id) VALUES( ?1, ?2)";
                    Query query=entityManager.createNativeQuery(sql);
                    query.setParameter(1,uId);
                    query.setParameter(2,rId);
                    query.executeUpdate();
                }
            }
            entityManager.flush();
            entityManager.getTransaction().commit();
        }catch (Exception e){
            entityManager.getTransaction().rollback();
        }finally {
            entityManager.close();
        }
        List<String> userIds = Arrays.stream(param).toList();
        try {
            permissionLogManager.updatePermission(getPermissionDTO(rId,data.get(0).getName(),userIdsDb,userIds), PermissionTypeEnum.USER);
        }catch (Exception e){
            e.printStackTrace();
            log.error("角色新增用户添加三员日志异常:",e);
        }
        return "msg.success('成功')";
    }
    private PermissionDTO getPermissionDTO(String roleId,String roleName,List<String> idsDb,List<String> ids){
        // 差集 (ids - idsDb)
        List<String> addIds = ids.stream().filter(item -> !idsDb.contains(item)).toList();
        List<String> deleteIds = idsDb.stream().filter(item -> !ids.contains(item)).toList();

        List<String> addNames = getNameByIds(addIds);
        List<String> deleteNames = getNameByIds(deleteIds);
        return new PermissionDTO(roleId,roleName,addIds,addNames,deleteIds,deleteNames);
    }

    private List<String> getNameByIds(List<String> userIds){
        List<String> result = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(userIds)){
            EntityManager em = fabosJsonDao.getEntityManager();
            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<User> cq = cb.createQuery(User.class);
            Root<User> root = cq.from(User.class);
            cq.select(root).where(cb.in(root.get("id")).value(userIds));
            TypedQuery<User> q = em.createQuery(cq);
            List<User> resultList = q.getResultList();
            if(CollectionUtils.isNotEmpty(resultList)){
                return resultList.stream().map(User::getName).collect(Collectors.toList());
            }
        }
        return result;
    }
}
