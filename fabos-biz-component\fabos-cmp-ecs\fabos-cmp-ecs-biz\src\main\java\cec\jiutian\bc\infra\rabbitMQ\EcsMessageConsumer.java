//package cec.jiutian.bc.infra.rabbitMQ;
//
//import cec.jiutian.bc.ecs.domain.message.service.MessageService;
//import cec.jiutian.bc.ecs.dto.SendMsgToPersonDTO;
//import cec.jiutian.bc.ecs.dto.SendMsgGroupDTO;
//import cec.jiutian.bc.ecs.dto.MessageCreateDTO;
//import jakarta.annotation.Resource;
//import org.springframework.context.annotation.Bean;
//import org.springframework.stereotype.Component;
//
//import java.util.function.Consumer;
//
//@Component
//public class EcsMessageConsumer {
//
//    @Resource
//    private MessageService messageService;
//
//    /**
//     * 外部系统发送消息消费入口
//     * @return
//     */
//    @Bean
//    public Consumer<MessageCreateDTO> processMessage(){
//        return messageCreateDTO -> {
//            messageService.processMessage(messageCreateDTO);
//        };
//    }
//
//    /**
//     * 内部系统发送消息消费入口
//     *
//     * @return
//     */
//    @Bean
//    public Consumer<SendMsgGroupDTO> sendMsgToGroup(){
//        return innerSendMsgDTO -> {
//            messageService.sendMsgToGroup(innerSendMsgDTO);
//        };
//    }
//
//    /**
//     * 不通过消息组直接发送消息消费入口
//     * @return
//     */
//    @Bean
//    public Consumer<SendMsgToPersonDTO> sendMsgToPerson(){
//        return sendMsgToPersonDTO -> {
//            try {
//                messageService.sendMsgToPerson(sendMsgToPersonDTO);
//            } catch (Exception e) {
//                throw new RuntimeException(e);
//            }
//        };
//    }
//}
