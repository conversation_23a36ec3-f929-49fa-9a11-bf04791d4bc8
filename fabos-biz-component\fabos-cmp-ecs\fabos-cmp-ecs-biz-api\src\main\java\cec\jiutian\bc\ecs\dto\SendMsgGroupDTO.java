package cec.jiutian.bc.ecs.dto;

import cec.jiutian.common.exception.ServiceException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * 根据消息类型发送消息
 */
@Data
public class SendMsgGroupDTO implements Serializable {

    /**
     * 消息类型编码
     */
    private String messageGroupCode;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息参数：用于链接跳转
     */
    private String parameters;



    public static void argsCheck(SendMsgGroupDTO sendMsgGroupDTO) {
        //参数非空校验
        if (sendMsgGroupDTO == null) {
            throw new ServiceException("参数不能为空");
        }
        if (StringUtils.isBlank(sendMsgGroupDTO.getMessageGroupCode())) {
            throw new ServiceException("消息类型不能为空");
        }
        if (StringUtils.isBlank(sendMsgGroupDTO.getContent())) {
            throw new ServiceException("消息内容不能为空");
        }
    }

}
