package cec.jiutian.bc.generalModeler.handler;

import cec.jiutian.bc.generalModeler.domain.material.model.Material;
import cec.jiutian.bc.generalModeler.domain.material.model.MaterialCategory;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.DependFiled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class MaterialCategorySelectHandler implements DependFiled.DynamicHandler<Material> {


    @Resource
    private JpaCrud jpaCrud;

    @Override
    public Map<String, Object> handle(Material material) {
        Map<String, Object> map = new HashMap<>();
        // 获取所有字段，重置可见标记
        String jpql = "SELECT t.columnName as columnName FROM MetadataModelFieldMTO t WHERE model = 'cec.jiutian.bc.generalModeler.domain.material.model.Material' and columnName like 'wl%'";
        List<String> result = jpaCrud.select(jpql);
        result.forEach(e -> {
            map.put(e + "Show", "N");
        });
        MaterialCategory materialCategory = jpaCrud.getById(MaterialCategory.class, material.getMaterialCategory().getId());
        if (materialCategory.getMaterialAttributeTemplate() != null) {
            materialCategory.getMaterialAttributeTemplate().getFields().forEach(e -> {
                map.put(e.getColumnName() + "Show", 'Y');
            });
        }
        return map;
    }
}