package cec.jiutian.bc.file.controller;

/**
 * <AUTHOR>
 * @time 2025-04-28 16:12
 */

import cec.jiutian.api.file.dto.GetFileInfoDTO;
import cec.jiutian.api.file.utils.FileUtil;
import cec.jiutian.bc.file.model.FabosJsonFile;
import cec.jiutian.bc.file.service.FileService;
import cec.jiutian.common.util.BeanValidators;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.base.BaseApi;
import cec.jiutian.core.view.fabosJson.util.DateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * @description: 功能说明（jira task id）
 * @author: chenjx
 * @date: 20220627
 */
@Slf4j
@Api(tags = "文件操作")
@RestController
@RequiredArgsConstructor
@RequestMapping("/file")
public class FileBizController extends BaseController {

    @Resource
    private final FileService fileService;

    @SneakyThrows
    @PostMapping("/create")
    @ApiOperation(value = "新建文件")
    public Object createFile(@RequestPart("file") MultipartFile file,
                             @RequestParam(required = false) String userFileCreateDTO,
                               @RequestParam(required = false) String lastEventUser,
                               @RequestParam(required = false) String filename,
                             @RequestParam(required = false) boolean responseInNewStyle,
                               @RequestParam(required = false) String lastEventComment) {
        // dto 将被忽略
        FabosJsonFile fabosJsonFile = StringUtils.isBlank(filename)
                ? new FabosJsonFile(file)
                : new FabosJsonFile(file, filename);
        String relativePath = File.separator + DateUtil.getFormatDate(new Date(), DateUtil.DATE);
        String modifiedFileName = FileUtil.processFileRenaming(fabosJsonFile);
        try {
            String errorMessage = fileService.createFile(file, relativePath, modifiedFileName, fabosJsonFile);
            if (responseInNewStyle) {
                return StringUtils.isBlank(errorMessage)
                        ? BaseApi.successApi(modifiedFileName)
                        : BaseApi.errorApi(errorMessage);
            } else {
                return respResult(StringUtils.isBlank(errorMessage), modifiedFileName, errorMessage);
            }
        } catch (Exception e) {
            log.error("fabosJson upload error", e);
            return responseInNewStyle
                    ? BaseApi.errorApi("文件上传失败, " + e.getMessage())
                    : respResult(false, modifiedFileName, "文件上传失败, " + e.getMessage());
        }
    }



    @PostMapping("/update")
    @ApiOperation(value = "更新文件", notes = "更新已存在的文件信息")
    public Object updateFile(@RequestPart("file") MultipartFile file, @RequestParam String userFileUpdateDTO,
                               @RequestParam(required = false) String lastEventUser,
                               @RequestParam(required = false) String filename,
                             @RequestParam(required = false) boolean responseInNewStyle,
                               @RequestParam(required = false) String lastEventComment) {
        return createFile(file, userFileUpdateDTO, lastEventUser, filename, responseInNewStyle, lastEventComment);
    }

    @PostMapping("/delete")
    @ApiOperation("文件彻底删除")
    public Response deleteFile(@RequestBody FileDeleteDTO fileDeleteDTO) {
        BeanValidators.validateWithException(validator, fileDeleteDTO);
        List<String> list = fileDeleteDTO.getUserUploadDocumentIdentifierList().stream().map(FileBizController.FileDeleteDocumentIDDTO::getUserUploadDocumentIdentifier).toList();
        fileService.deleteMultiple(list);
        return respSuccessResult("操作成功");
    }

//    @PostMapping("/deleteRelationByUserAndUUID")
//    @ApiOperation("文件关联项删除")
//    public Response deleteRelationByUserAndUUID(@RequestBody RelationDeleteByUserAndUUIDDTO relationDeleteByUserandUUIDDTO) {
//        BeanValidators.validateWithException(validator, relationDeleteByUserandUUIDDTO);
//        BizParamUtils.setOperationName(BusinessDefinition.FileOperation.FILE_DELETE_RELATION);
//        fileBizService.deleteRelationByUserAndUUID(relationDeleteByUserandUUIDDTO);
//        return respSuccessResult("操作成功");
//    }

//    @PostMapping("/queryFileByUser")
//    @ApiOperation("相关文件查询")
//    public Response queryFileByUser(@RequestBody FileQueryDTO fileQueryDTO) {
//        BeanValidators.validateWithException(validator, fileQueryDTO);
//        Object obj = fileBizService.queryFileByUser(fileQueryDTO);
//        if (null != obj) {
//            return respSuccessResult(obj, "操作成功");
//        } else {
//            return respFaultResult(obj, "操作成功");
//        }
//    }
//
//    @PostMapping("/queryAllFileByFileName")
//    @ApiOperation("所有文件查询")
//    public Response queryAllFileByFileName(@RequestBody AllFileQueryDTO allFileQueryDTO) {
//        BeanValidators.validateWithException(validator, allFileQueryDTO);
//        Object obj = fileBizService.queryAllFileByFileName(allFileQueryDTO);
//        if (null != obj) {
//            return respSuccessResult(obj, "操作成功");
//        } else {
//            return respFaultResult(obj, "操作成功");
//        }
//    }

    @PostMapping("/getFileInfo")
    @ApiOperation(value = "查询文件信息", notes = "根据uuid查询文件信息，该接口接收一个以逗号隔开的uuid列表的字符串，返回一个文件信息数组")
    public Response getFileInfo(@RequestBody GetFileInfoDTO getFileInfoDTO) {
        Object info = fileService.getFileInfoByUUIDs(getFileInfoDTO.getUuid());
        return respSuccessResult(info, "操作成功");
    }

    @GetMapping("/document/download/**")
    @ApiOperation(value = "文件下载", notes = "文件下载")
    @SneakyThrows
    public void download(HttpServletRequest request, HttpServletResponse response) {
        String incomingFileName = StringUtils.removeStart(request.getRequestURI(), "/file/document/download");
        fileService.download(incomingFileName, response);
    }

//    @GetMapping("/officePreview/**")
//    @ApiOperation("文件预览")
//    public void preview(HttpServletRequest request, HttpServletResponse response) {
//        String filePath = StringUtils.removeStart(request.getRequestURI(), "/file/officePreview/");
//        fileDownloadService.preview(response, filePath);
//    }
//
//    @GetMapping("/v2/officePreview/{uuid}")
//    @ApiOperation("文件预览,使用于新的前端框架(react)")
//    public void previewV2(@PathVariable String uuid, HttpServletResponse response) {
//        fileDownloadService.previewV2(response, uuid);
//    }

    @GetMapping("/download/{uuid}")
    @ApiOperation(value = "文件下载", notes = "文件下载,相关表：USR_UPLD_TRNSCN_FL")
    public void downloadById(@PathVariable String uuid, HttpServletResponse response) {
//        fileService.download(uuid, response);
        fileService.downloadById(response, uuid);
    }

//    /**
//     * 大文件上传可能出现超时现象
//     */
//    @PostMapping(value = "/multiFileUploadAndSaveDataForFront")
//    public String multiFileUploadAndSaveData(@RequestPart MultipartFile[] files, @RequestParam String filePath,
//                                             @RequestParam String functionFolder, @RequestParam String fileOperation) {
//        BizParamUtils.setOperationName(BusinessDefinition.FileOperation.FILE_CREATE);
//        return fileBizService.multiFileUploadAndSaveData(files, filePath, functionFolder, fileOperation);
//    }
//
    /**
     * 大文件上传可能出现超时现象
     */
    @PostMapping(value = "/multiFileUploadAndSaveDataForAngular")
    public Response multiFileUploadAndSaveDataForAngular(@RequestPart MultipartFile[] files,
                                                         @RequestParam(required = false) String filePath,
                                                         @RequestParam(required = false) String functionFolder,
                                                         @RequestParam(required = false) String fileOperation) {
        List<String> result = new ArrayList<>();
        if (files != null) {
            for (MultipartFile file: files) {
                FabosJsonFile fabosJsonFile = new FabosJsonFile(file);
                String relativePath = File.separator + DateUtil.getFormatDate(new Date(), DateUtil.DATE);
                String modifiedFileName = FileUtil.processFileRenaming(fabosJsonFile);
                String errorMessage = fileService.createFile(file, relativePath, modifiedFileName, fabosJsonFile);
                if (StringUtils.isBlank(errorMessage)) {
                    result.add(modifiedFileName);
                } else {
                    return respFaultResult("文件上传失败, " + errorMessage);
                }
            }
        }
        return respSuccessResult(String.join(",", result), "操作成功");

    }

    @PostMapping("/deleteBatchFile")
    public Response deleteBatchFile(@RequestBody FileDeleteDocumentIDDTO getFileInfoDTO) {
        if (Objects.nonNull(getFileInfoDTO)
                && StringUtils.isNotBlank(getFileInfoDTO.getUserUploadDocumentIdentifier())) {
            fileService.deleteSingle(getFileInfoDTO.getUserUploadDocumentIdentifier());
        }
        return respSuccessResult("操作成功");
    }

    @Data
    public static class UserFileCreateDTO implements Serializable {

        @NotEmpty
        //文件上传来源
        private String documentUploadTypeCode;
        private String processIdentifier;
        private String processOperationIdentifier;
        private String factoryName;
        private String areaName;
        private String documentCategoryCode;
        private String departmentIdentifier;
        private String lastEventComment;
        @NotEmpty
        private String lastEventUser;
    }

    @Data
    public static class FileDeleteDTO implements Serializable {
        @NotEmpty
        private List<FileDeleteDocumentIDDTO> userUploadDocumentIdentifierList;

    }

    @Data
    public static class FileDeleteDocumentIDDTO implements Serializable {
        //        Long userUploadDocumentIdentifier;
        private String userUploadDocumentIdentifier;
    }

}