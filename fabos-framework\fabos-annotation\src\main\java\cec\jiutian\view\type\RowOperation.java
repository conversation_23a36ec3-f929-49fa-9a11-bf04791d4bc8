package cec.jiutian.view.type;

import cec.jiutian.view.config.Comment;
import cec.jiutian.view.expr.ExprBool;
import cec.jiutian.view.field.RowOperationReadonly;
import cec.jiutian.view.fun.OperationHandler;

import java.beans.Transient;

/**
 * 使用一列或者多列的数据执行特定代码
 *
 *
 */
public @interface RowOperation {

    @Comment("编码，要求唯一，若未定义则会导致该button重复定义在menu表中")
    String code();

    String title();

//    String name() default "";

    @Transient
    ExprBool show() default @ExprBool;

    @Comment("调用提示,空则不提示")
    String callHint() default "fabosJson.operation.call_hint";

    @Comment("按钮初始化时。用于菜单模型Menu中showType的定义")
    MenuShowTypeEnum menuShowTypeEnum() default MenuShowTypeEnum.popup;
    @Comment("按钮初始化时。用于菜单模型Menu中buttonType的定义")
    MenuButtonTypeEnum menuButtonType() default MenuButtonTypeEnum.DEFAULT;
    @Comment("功能模式")
    Mode mode() default Mode.MULTI;

    @Comment("功能类型")
    Type type() default Type.FABOSJSON;
    @Comment("功能类型")
    PopupType popupType() default PopupType.DEFAULT;
    @Comment("弹窗数据回显字段:一般是配置在原模型里面,是fabosJsonClass字段的类型或者对应集合数据。前端通过该字段回显数据")
    String returnPopupDataField() default "";
    @Comment("控制按钮显示与隐藏 或 能否点击（JS表达式），变量：item 获取整行数据")
    String ifExpr() default "";

    @Comment("控制 ifExpr 的结果是控制按钮的 显示与隐藏 还是 能否点击")
    IfExprBehavior ifExprBehavior() default IfExprBehavior.DISABLE;

    @Comment("type为tpl时可用，可在模板中使用rows变量，可获取选中行的数据")
    Tpl tpl() default @Tpl(path = "");
    @Comment("按钮提交时，需要填写的表单信息")
    Class<?> fabosJsonClass() default void.class;

    @Transient
    @Comment("该配置可在operationHandler中获取")
    String[] operationParam() default {};

    @Transient
    @Comment("type为Mode时可用，操作按钮点击后，后台处理逻辑")
    Class<? extends OperationHandler> operationHandler() default OperationHandler.class;

    @Comment("提交行为")
    SubmitMethod submitMethod() default SubmitMethod.NONE;

    @Comment("自定义只读配置，可选择预设或自订配置")
    RowOperationReadonly readonly() default @RowOperationReadonly;

    PopupTreeTableUsageMode popupTreeTableUsageMode() default PopupTreeTableUsageMode.queryAndSubmit;

    /**
     * 弹窗中是否有勾选提交功能
     */
    enum PopupTreeTableUsageMode {
        @Comment("只是查询，不需要勾选和提交")
        queryOnly,
        @Comment("需要选中数据并提交")
        queryAndSubmit,
    }

    /**
     * 除了 SINGLE 模式之外，其余三种都是必定出现在 table 头上
     */
    enum Mode {
        @Comment("依赖单行数据")
        SINGLE,
        @Comment("依赖多行数据")
        MULTI,
        @Comment("不依赖行数据")
        BUTTON,
        @Comment("依赖单行, 但是位置在表头")
        HEADER
    }

    enum Type {
        @Comment("通过fabosJson表单渲染，operationHandler进行逻辑处理")
        FABOSJSON,
        @Comment("弹框显示，显示内容需要配置fabosJsonClass字段")
        POPUP,
        @Comment("下载handler处理的文件")
        FILE
    }

    enum IfExprBehavior {
        @Comment("IfExpr处理按钮显示或隐藏")
        HIDE,
        @Comment("IfExpr处理按钮可否点击")
        DISABLE
    }

    /**
     * 提交时的行为，此配置将决定前端点击提交按钮时调用的接口<br>
     * 前提是<code>type==Type.POPUP && popupType==PopupType.FORM</code><br>
     * 需要注意的是，如果配置的有引用模型（即 fabosJsonClass） ，则会调用引用模型的add/update接口
     * <ol>
     * <li>ADD: 新增操作，提交数据到add接口</li><br>
     * <li>UPDATE: 更新操作，提交数据到update接口</li><br>
     * <li>HANDLER: 逻辑处理，提交数据到handler接口</li><br>
     * <li>NONE：无，即不处理</li><br>
     * </ol
     */
    enum SubmitMethod {
        @Comment("提交数据到add接口")
        ADD,
        @Comment("提交数据到update接口")
        UPDATE,
        @Comment("提交数据到handler接口")
        HANDLER,
        @Comment("不处理")
        NONE
    }

    //如果type=POPUP 选择popup的类型  弹窗展示的类型
    enum PopupType {
        @Comment("弹窗通过树形展示")
        TREE,
        @Comment("弹窗通过表格展示")
        TABLE,
        @Comment("弹窗通过表单展示")
        FORM,
        @Comment("默认数据，非弹窗")
        DEFAULT
    }
    //该配置会使用在菜单初始化时。菜单模型Menu模型中showType字段的定义
    enum MenuShowTypeEnum {
        @Comment("弹窗")
        popup,
        @Comment("抽屉")
        drawer
    }
    //按钮类型  初始化时如果是跳转类型的按钮  不会更新  跳过更新操作，因为调整按钮没办法初始化，只能去菜单页面配置跳转参数。因此这里需要配置为跳转类型的按钮才不会被覆盖
    enum MenuButtonTypeEnum {
        @Comment("默认")
        DEFAULT,
        @Comment("调转")
        JUMP,
        @Comment("混入，当按钮点击后进入到使用全代码开发的页面时，使用此配置")
        MIX;

        public String getName() {
            return this.name();
        }
    }
}
