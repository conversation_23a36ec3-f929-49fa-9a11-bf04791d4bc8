package cec.jiutian.bc.generalModeler.domain.stockAlarm.model;

import cec.jiutian.bc.generalModeler.domain.stockAlarm.proxy.StockAlarmDataProxy;
import cec.jiutian.bc.generalModeler.port.dto.PositionMTO;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.InputGroup;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.fun.ColumnTitleHandler;
import cec.jiutian.view.type.Power;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;

@Table(name = "BWS_STOCK_ALARM")
@FabosJson(
        name = "预警规则配置",
        dataProxy = StockAlarmDataProxy.class,
        power = @Power(add = false, delete = false)
)
@Entity
@Getter
@Setter
public class StockAlarm extends MetaModel {

    @FabosJsonField(
            views = @View(title = "呆滞判定标准"),
            edit = @Edit(title = "呆滞判定标准", notNull = true,
                    inputType = @InputType(length = 20), inputGroup = @InputGroup(prefix = "超过", postfix = "天无作业记录"))
    )
    private String dullValue;

    @FabosJsonField(
            views = @View(title = "临期判定标准"),
            edit = @Edit(title = "临期判定标准", notNull = true,
                    inputType = @InputType(length = 20), inputGroup = @InputGroup(prefix = "距使用期限不足", postfix = "天"))
    )
    private String adventValue;

    @FabosJsonField(
            views = @View(title = "超期判定标准"),
            edit = @Edit(title = "超期判定标准", notNull = true,
                    inputType = @InputType(length = 20), inputGroup = @InputGroup(prefix = "距使用期限不足", postfix = "天"))
    )
    private String overDullValue;

    @Transient
    private static final String keeperCode = "";

    @Transient
    private static final String purchaserCode = "";


    /*    @ManyToOne
    @FabosJsonField(
            views = @View(title = "所属岗位", column = "name"),
            edit = @Edit(title = "所属岗位", notNull = true, type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType, search = @Search())
    )
    private Position position;
    */
    @ManyToOne
    @JoinColumn(name = "keeper_id", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "保管员", column = "name"),
            edit = @Edit(title = "保管员", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(type = ReferenceTableType.SelectShowTypeMTO.DROPDOWN_TABLE, paramsHandler = ParamsHandler.class)
            )
    )
    private PositionMTO keeper;

    @ManyToOne
    @JoinColumn(name = "purchaser_id", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "采购员", column = "name"),
            edit = @Edit(title = "采购员", notNull = true,
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(type = ReferenceTableType.SelectShowTypeMTO.DROPDOWN_TABLE, paramsHandler = ParamsHandler.class)
            )
    )
    private PositionMTO purchaser;


    @Component
    static class ParamsHandler implements ColumnTitleHandler {
        @Override
        public Collection<ColumnAndTitle> handle() {
            return List.of(
                    new ColumnAndTitle("id", "岗位ID"),
                    new ColumnAndTitle("name", "岗位姓名"),
                    new ColumnAndTitle("responsibility", "岗位职责")
            );
        }
    }

}
