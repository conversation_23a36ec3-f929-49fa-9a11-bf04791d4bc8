package cec.jiutian.bc.file.remote.feign;

import cec.jiutian.bc.file.service.FileService;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;

import static cec.jiutian.core.frame.constant.FabosJsonRestPath.FILE_PREFIX;

/**
 * <AUTHOR>
 * @time 2025-06-11 17:41
 */
@Slf4j
@RestController
@RequestMapping(FabosJsonRestPath.FABOS_REMOTE_API_OLD)
@AllArgsConstructor
public class FabosFileFeign {

    @Resource
    private final FileService fileService;

    @PostMapping(value = FILE_PREFIX + "/download")
    public ResponseEntity<org.springframework.core.io.Resource> downloadById(@RequestParam String id, HttpServletResponse response) {

        try {
            byte[] bytes = fileService.downloadAsByteStream(id);
            org.springframework.core.io.Resource resource = new ByteArrayResource(bytes);
            // 设置 Content-Disposition 响应头，这样浏览器会提示用户下载
            // Set Content-Disposition header to prompt the browser for download
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"")
                    .body(resource);
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "File not found: " + id, e);
        }
    }

}
