package cec.jiutian.bc.generalModeler.domain.namingRule.proxy;


import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRule;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleParameter;
import cec.jiutian.bc.generalModeler.domain.namingRule.model.NamingRuleParameterSrValue;
import cec.jiutian.bc.generalModeler.domain.namingRule.service.NamingRuleDomainService;
import cec.jiutian.bc.generalModeler.domain.namingRule.service.NamingRuleParameterDomainService;
import cec.jiutian.bc.generalModeler.enumeration.NamingRuleEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Calendar;

@Component
public class NamingRuleParameterSrValueDataProxy implements DataProxy<NamingRuleParameterSrValue> {

    @Resource
    private FabosJsonDao fabosJsonDao;


    @Resource
    private NamingRuleDomainService namingRuleDomainService;

    @Resource
    private NamingRuleParameterDomainService namingRuleParameterDomainService;

    @Override
    public void beforeAdd(NamingRuleParameterSrValue namingRuleParameterSrValue) {
        NamingRule namingRule = namingRuleDomainService.getParametersByCode(namingRuleParameterSrValue.getSrParameter().getNamingRule().getNamingRuleCode());
        String[] dependParameters = namingRuleParameterSrValue.getSrParameter().getDependParameters().split(",");
        // 设置 yearText,monthText,dayText的值
        for (String dependParameter : dependParameters) {
            NamingRuleParameter namingRuleParameter = namingRuleParameterDomainService.getParameterByAk(namingRule, dependParameter);
            NamingRuleEnum.Enum type = NamingRuleEnum.Enum.valueOf(namingRuleParameter.getParameterType());
            switch (type) {
                case D2:
                    int day = Calendar.getInstance().get(Calendar.DATE);
                    String formatDay = String.format("%02d", day);
                    namingRuleParameterSrValue.setDayText(formatDay);
                    break;
                case M2:
                    int month = Calendar.getInstance().get(Calendar.MONTH) + 1;
                    String formatMonth = String.format("%02d", month);
                    namingRuleParameterSrValue.setMonthText(formatMonth);
                    break;
                case Y2:
                    int year = Calendar.getInstance().get(Calendar.YEAR);
                    String formatYear = String.format("%02d", year % 100);
                    namingRuleParameterSrValue.setYearText(formatYear);
                    break;
                case Y4:
                    int year4 = Calendar.getInstance().get(Calendar.YEAR);
                    String formatYear4 = String.format("%04d", year4);
                    namingRuleParameterSrValue.setYearText(formatYear4);
                    break;
                default:
                    break;
            }
        }
    }

    @Override
    public void beforeUpdate(NamingRuleParameterSrValue namingRuleParameterSrValue) {
        // 设置 yearText,monthText,dayText的值 （当提交的数据为空时）
        NamingRuleParameterSrValue namingRuleParameterSrValueDb = fabosJsonDao.getEntityManager().find(NamingRuleParameterSrValue.class, namingRuleParameterSrValue.getId());
        if (namingRuleParameterSrValue.getYearText() == null) {
            namingRuleParameterSrValue.setYearText(namingRuleParameterSrValueDb.getYearText());
        }
        if (namingRuleParameterSrValue.getMonthText() == null) {
            namingRuleParameterSrValue.setMonthText(namingRuleParameterSrValueDb.getMonthText());
        }
        if (namingRuleParameterSrValue.getDayText() == null) {
            namingRuleParameterSrValue.setDayText(namingRuleParameterSrValueDb.getDayText());
        }

    }


}
