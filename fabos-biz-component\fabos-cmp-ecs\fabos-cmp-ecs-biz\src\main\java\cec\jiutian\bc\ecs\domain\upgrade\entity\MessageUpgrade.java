package cec.jiutian.bc.ecs.domain.upgrade.entity;

import cec.jiutian.bc.ecs.domain.message.entity.Message;
import cec.jiutian.core.frame.module.MetaModel;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Entity
@Table(name = "ecs_message_upgrade")
@Getter
@Setter
@Accessors(chain = true)
public class MessageUpgrade extends MetaModel {


    private String messageId;

    private String messageGroupId;


    public static MessageUpgrade crateByMessage(Message message) {
        MessageUpgrade messageUpgrade = new MessageUpgrade();
        messageUpgrade.setMessageId(message.getId())
                .setMessageGroupId(message.getMessageGroup().getId());
        return messageUpgrade;
    }


}
