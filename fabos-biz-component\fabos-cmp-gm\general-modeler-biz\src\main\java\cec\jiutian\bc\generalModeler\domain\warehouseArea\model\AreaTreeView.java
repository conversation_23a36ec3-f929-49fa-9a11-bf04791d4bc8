package cec.jiutian.bc.generalModeler.domain.warehouseArea.model;

import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.TemplateType;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Tree;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;


/**
 * <AUTHOR>
 * @date 2024/12/10
 * @description TODO
 */
@Table(name = "area_tree_view")
@FabosJson(
        name = "区域树形数据",
        tree = @Tree(id = "id", label = "name", pid = "parentId", expandLevel = 3)
)
@Entity
@Getter
@Setter
@TemplateType(type = "treeForm")
public class AreaTreeView {

    @Id
    @FabosJsonField(
            views = @View(title = "id", show = false),
            edit = @Edit(title = "", show = false)
    )
    private String id;

    @FabosJsonField(
            views = @View(title = "区域名称"),
            edit = @Edit(title = "区域名称", search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "仓库"),
            edit = @Edit(title = "仓库", search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String warehouseName;

    @FabosJsonField(
            views = @View(title = "库区/货架"),
            edit = @Edit(title = "库区/货架", search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String blockName;

    @FabosJsonField(
            views = @View(title = "货位"),
            edit = @Edit(title = "货位", search = @Search(vague = true),
                    inputType = @InputType(length = 40))
    )
    private String shelfName;

    @FabosJsonField(
            views = @View(title = "级别", show = false),
            edit = @Edit(title = "级别", show = false)
    )
    private String level;

    //    @ManyToOne
//    @FabosJsonField(
//            edit = @Edit(
//                    title = "上级树节点",
//                    type = EditType.REFERENCE_TREE,
//                    referenceTreeType = @ReferenceTreeType(pid = "parent.id")
//            )
//    )
//    private AreaTreeDTO parent;
    @FabosJsonField(
            views = @View(title = "上级id", show = false),
            edit = @Edit(title = "上级id", show = false)
    )
    private String parentId;
}
