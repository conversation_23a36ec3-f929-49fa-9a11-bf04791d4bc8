package cec.jiutian.bc.urm.config;

import cec.jiutian.bc.urm.inbound.local.context.BackgroundTaskTraceContext;
import cec.jiutian.common.constant.AuthorizationConstant;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import feign.RequestInterceptor;
import org.apache.seata.core.context.RootContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

@Configuration
public class FeignAuthConfig {
    @Bean
    public RequestInterceptor requestInterceptor() {
        return template -> {
            // 透传当前请求的认证信息（JWT）
            ServletRequestAttributes attributes =
                    (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                String token = attributes.getRequest().getHeader("Authorization");
                template.header("Authorization", token);
            }else {
                //异步请求
                template.header("Authorization", AsyncContextHolder.getAuthorization());
            }
            // 处理后台任务认证
            if (StringUtils.isNotBlank(BackgroundTaskTraceContext.getTraceId())
                    && StringUtils.isSubstringOccurrenceOnce(template.url(), FabosJsonRestPath.FABOS_TASK_API + "/")) {
                template.header(AuthorizationConstant.BACKGROUND_TASK_AUTHORIZATION, BackgroundTaskTraceContext.getTraceId());
            }
            // 处理seata xid传递
            String xid = RootContext.getXID();
            if (xid != null) {
                template.header(RootContext.KEY_XID, xid);
            }
        };
    }
}