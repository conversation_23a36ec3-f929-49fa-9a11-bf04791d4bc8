package cec.jiutian.bc.generalModeler.enumeration;

import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class WarehouseBlockPurposeEnum implements ChoiceFetchHandler {
    @Override
    public List<VLModel> fetch(String[] params) {
        List<VLModel> list = new ArrayList<>();
        for (Enum value : Enum.values()) {
            list.add(new VLModel(value.name(), value.getTitle()));
        }
        return list;
    }

    //不限、收货、发货、存储合格品、质检、退货、存储危废品、存储不合格品
    @AllArgsConstructor
    @Getter
    public enum Enum {
        Unlimited("不限"),
        Receive("收货"),
        Deliver("发货"),
        StoreQualifiedProducts("存储合格品"),
        Quality("质检"),
        Return("退货"),
        StorageHazardousWaste("存储危废品"),
        StoreNonconformingProducts("存储不合格品");

        private final String title;

    }
}
