package cec.jiutian.bc.generalModeler.domain.client.proxy;

import cec.jiutian.bc.generalModeler.domain.client.model.Client;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.DataProxy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class ClientDataProxy implements DataProxy<Client> {

    @Resource
    private JpaCrud jpaCrud;

    @Override
    public void beforeAdd(Client client) {
        Client condition = new Client();
        condition.setCode(client.getCode());
        Client data = jpaCrud.selectOne(condition);
        if (data != null) {
            throw new FabosJsonApiErrorTip("客户编码不可重复，请确认");
        }
    }
}
