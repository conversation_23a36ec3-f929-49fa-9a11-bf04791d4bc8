package cec.jiutian.bc.ecs.domain.messagerecord.entity;

import cec.jiutian.bc.ecs.domain.message.entity.Message;
import cec.jiutian.bc.ecs.domain.messagegroup.entity.WeixinGroup;
import cec.jiutian.bc.ecs.domain.messagerecord.event.MessageRecordDataProxy;
import cec.jiutian.bc.ecs.enumeration.MessageGroupLevelEnum;
import cec.jiutian.core.frame.constant.YesOrNoStatus;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.PreDataProxy;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.CascadeType;
import jakarta.persistence.ConstraintMode;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description:
 */
@MappedSuperclass
@Getter
@Setter
@FabosJson(name = "", colorIndicator = "colorFlag")
@PreDataProxy(MessageRecordDataProxy.class)
public class MessageRecordSuper extends MetaModel {

    @Transient
    private String colorFlag;

    @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE})
    @FabosJsonField(
            views = @View(title = "告警消息", column = "title", type = ViewType.TABLE_FORM, show = false),
            edit = @Edit(title = "告警消息", notNull = true, search = @Search(vague = true), show = false,
                    type = EditType.REFERENCE_TABLE, referenceTableType = @ReferenceTableType(id = "id", label = "title"),
                    readonly = @Readonly
            )
    )
    @JoinColumn(foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    private Message message;

    @FabosJsonField(
            views = @View(title = "标题"),
            edit = @Edit(title = "标题", search = @Search(vague = true))
    )
    private String title;

    /**
     * 推送方式
     */
    @FabosJsonField(
            views = @View(title = "推送方式"),
            edit = @Edit(title = "推送方式", search = @Search(vague = true))
    )
    private String dispatchWay;

    /**
     * 是否为即时告警
     * 即时告警会通过websocket发送前端，前端弹窗或弹出提示，使用户能够及时看到告警信息
     */
    @FabosJsonField(
            views = @View(title = "是否为即时告警"),
            edit = @Edit(title = "是否为即时告警", search = @Search(vague = true))
    )
    private Boolean immediateAlarmFlag;

    /**
     * 推送人员（记录推送人员手机号）
     */
    @FabosJsonField(
            views = @View(title = "接收人员", show = false),
            edit = @Edit(title = "接收人员", show = false)
    )
    private String dispatchUser;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "接收人员", column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "接收人员", notNull = true, search = @Search(vague = true),
                    type = EditType.REFERENCE_TABLE, referenceTableType = @ReferenceTableType(id = "id", label = "name"),
                    readonly = @Readonly
            )
    )
    @JoinColumn(foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    private MetaUser dispatchMetaUser;

    @ManyToOne
    @JoinColumn(foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    @FabosJsonField(
            views = @View(title = "接收微信群组", column = "name", type = ViewType.TABLE_FORM),
            edit = @Edit(title = "接收微信群组", type = EditType.REFERENCE_TABLE)
    )
    private WeixinGroup dispatchWeixinGroup;

    /**
     * 推送时间
     */
    @FabosJsonField(
            views = @View(title = "推送时间", type = ViewType.DATE_TIME),
            edit = @Edit(title = "推送时间", type = EditType.DATE_TIME_RANGE, search = @Search(vague = true))
    )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime dispatchTime;

    /**
     * 消息内容
     */
    @FabosJsonField(
            views = @View(title = "消息内容", toolTip = true),
            edit = @Edit(title = "消息内容", search = @Search(vague = true), type = EditType.TEXTAREA, inputType = @InputType(length = 4000), formColumns = 3, index = 9999)
    )
    private String content;

    @FabosJsonField(
            views = @View(title = "告警级别"),
            edit = @Edit(index = 104, title = "告警级别", search = @Search(vague = true),
                    choiceType = @ChoiceType(fetchHandler = MessageGroupLevelEnum.ChoiceFetch.class),
                    type = EditType.CHOICE, notNull = true, readonly = @Readonly),
            dynamicField = @DynamicField(passive = true)

    )
    private String groupLevel;

    /**
     * 是否已读：未读的消息会展示在右上角区域
     */
    @FabosJsonField(
            views = @View(title = "是否已读"),
            edit = @Edit(title = "是否已读")
    )
    private String status;

    /**
     * 是否需要处理
     */
    @FabosJsonField(
            views = @View(title = "是否需要处理"),
            edit = @Edit(title = "是否需要处理", readonly = @Readonly,
                    type = EditType.CHOICE,
                    defaultVal = "N",
                    choiceType = @ChoiceType(fetchHandler = YesOrNoStatus.ChoiceFetch.class)
            ),
            dynamicField = @DynamicField(passive = true)
    )
    private String isProcess;

    @FabosJsonField(
            views = @View(title = "是否已处理"),
            edit = @Edit(title = "是否已处理",
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoStatus.ChoiceFetch.class)
            )
    )
    private String processCompleteFlag;

    @FabosJsonField(
            views = @View(title = "处理说明"),
            edit = @Edit(title = "处理说明", type = EditType.TEXTAREA,
                    formColumns = 3, inputType = @InputType(length = 400)
                    , dependFieldDisplay = @DependFieldDisplay(showOrHide = "processCompleteFlag=='Y'")
            )
    )
    private String processDetail;

    @ManyToOne
    @FabosJsonField(
            views = @View(title = "处理人", column = "name"),
            edit = @Edit(title = "处理人",
                    type = EditType.REFERENCE_TABLE, referenceTableType = @ReferenceTableType(id = "id", label = "name")
                    , dependFieldDisplay = @DependFieldDisplay(showOrHide = "processCompleteFlag=='Y'")
            ))
    private MetaUser processUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @FabosJsonField(
            views = @View(title = "处理时间", type = ViewType.text),
            edit = @Edit(title = "处理时间", readonly = @Readonly, type = EditType.DATE_TIME_RANGE,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "processCompleteFlag=='Y'")
            )
    )
    private LocalDateTime processTime;

//
//    public void initMessageRecord(MessageRecordCreateDTO createDTO) {
//        this.setTitle(createDTO.getTitle());
//        this.setContent(createDTO.getContent());
//        this.setDispatchUser(createDTO.getDispatchUser());
//        this.setDispatchWay(createDTO.getDispatchWay());
//        this.setCreateTime(LocalDateTime.now());
//    }
//
//    public void initByMessage(Message message) {
//        this.setCreateBy(message.getCreateBy());
//        this.setOid(message.getOid());
//        this.setTitle(message.getTitle());
//        this.setMessageId(message.getId());
//        this.setOriginalSystem(null);
//        this.setMessageGroupId(message.getMessageGroup().getId());
//
//    }
}
