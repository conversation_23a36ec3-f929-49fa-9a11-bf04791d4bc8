//package cec.jiutian.bc.urm.inbound.remote.rpc;
//
//import cec.jiutian.functionexecutor.dto.InvokeDTO;
//import cec.jiutian.functionexecutor.rpcProcess.RpcExecutor;
//import org.apache.dubbo.config.annotation.DubboService;
//import org.springframework.beans.BeansException;
//import org.springframework.context.ApplicationContext;
//
//@DubboService
//public class BizRpcExecutor implements RpcExecutor {
//
//    private static ApplicationContext context;
//
//    private static ClassLoader classLoader = BizRpcExecutor.class.getClassLoader();
//    @Override
//    public Object doInvoke(InvokeDTO invokeDTO) {
//        return RpcExecutor.invoke(invokeDTO, context,classLoader);
//    }
//
//    @Override
//    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
//        context = applicationContext;
//    }
//}
