package cec.jiutian.bc.file.service;

import cec.jiutian.api.file.controller.FabosFileUploadController;
import cec.jiutian.api.file.dto.FrUploadTransactionFilePO;
import cec.jiutian.api.file.model.FabosJsonFileSuper;
import cec.jiutian.api.file.utils.AsciiUtils;
import cec.jiutian.api.file.utils.FileUtil;
import cec.jiutian.bc.file.model.FabosJsonFile;
import cec.jiutian.common.constant.ContentTypeConstant;
import cec.jiutian.common.util.ContentTypeUtils;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.frame.util.FabosJsonUtil;
import cec.jiutian.core.prop.FabosJsonProp;
import cec.jiutian.core.view.fabosJson.util.DateUtil;
import cec.jiutian.view.fun.AttachmentProxy;
import com.alibaba.fastjson.JSON;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import static cec.jiutian.api.file.utils.FileUtil.FS_SEP;

@Service
@Slf4j
@RequiredArgsConstructor
public class FileService implements FabosFileUploadController.IUploadService {

    @Resource
    private final FabosJsonDao fabosJsonDao;
    private final FabosJsonProp fabosJsonProp;
    private final HttpServletRequest request;

    /**
     * 下载文件
     *
     * @param incoming 传入的文件路径或者uuid
     * @param response 此次请求的响应
     */
    @SneakyThrows
    public void download(String incoming, HttpServletResponse response) {
        if (StringUtils.isBlank(incoming)) {
            FileUtil.set404Status(response);
            return;
        }
        String path = FS_SEP + incoming;
        FabosJsonFile fabosJsonFile = retrieveFile(incoming);
        // 如果没有在数据库中找到文件档案
        if (Objects.isNull(fabosJsonFile)) {
            log.error("没有找到或找到了多个文件记录，无法下载文件：{}", path);
            FileUtil.set404Status(response);
            return;
        }
        // 如果此文件并非为公共文件，但是请求地址却为 public ，则 403 forbidden
        if (fabosJsonFile.getNonProtected() != null && !fabosJsonFile.getNonProtected()
                && request.getRequestURI().contains(FabosJsonRestPath.DOWNLOAD_PATH_NO_AUTH + "/")) {
            FileUtil.set403Status(response);
            return;
        }
        byte[] file = performDownload(fabosJsonFile);
        if (Objects.nonNull(file)) {
            FileUtil.setOutputStream(response, file, fabosJsonFile.getOriginalFileName());
        } else {
            // 走到这里说明在本地文件系统和OSS均没有找到文件，返回404
            log.error("文件下载失败，文件不存在：{}", path);
            FileUtil.set404Status(response);
            return;
        }

    }

    /**
     * 下载文件, 转为二进制流
     *
     * @param incoming 传入的文件路径或者uuid
     */
    @SneakyThrows
    public byte[] downloadAsByteStream(String incoming) {
        if (StringUtils.isBlank(incoming)) {
            return null;
        }
        FabosJsonFile fabosJsonFile = retrieveFile(incoming);
        if (Objects.isNull(fabosJsonFile)) {
            return null;
        }
        return performDownload(fabosJsonFile);

    }

    private byte[] performDownload(FabosJsonFile fabosJsonFile) {
        // 文件档案存储了本地路径，尝试去本地文件系统找文件
        if (StringUtils.isNotBlank(fabosJsonFile.getPathOnFileSystem())){
            File file = new File(fabosJsonFile.getPathOnFileSystem());
            if (file.exists()) {
                log.debug("在本地文件系统检测到文件，传回客户端: {}", fabosJsonFile.getFileUUID());
                log.info("文件下载成功：{}", fabosJsonFile.getOriginalFileName());
                return readFileToByteArray(file, false);
            }
        }

        // 如果没找到本地文件，且启用了文件代理，则尝试从OSS中找
        AttachmentProxy attachmentProxy = FabosJsonUtil.findAttachmentProxy();
        if (StringUtils.isNotBlank(fabosJsonFile.getPathOnOSS())
                && Objects.nonNull(attachmentProxy)) {
            File file = attachmentProxy.downLoad(fabosJsonFile.getBucket(), fabosJsonFile.getPathOnOSS());
            if (Objects.nonNull(file) && file.exists()) {
                log.debug("在OSS检测到文件，传回客户端: {}",  fabosJsonFile.getFileUUID());
                log.info("文件下载成功：{}", fabosJsonFile.getOriginalFileName());
                return readFileToByteArray(file, true);
            }
        }
        return null;
    }

    private byte[] readFileToByteArray(File file, boolean needDelete) {
        byte[] fileContent;

        try (FileInputStream stream = new FileInputStream(file)) {
            fileContent = stream.readAllBytes();
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new FabosJsonApiErrorTip("读取文件失败：" + e.getMessage());
        }

        if (needDelete) {
            // 流关闭后，尝试删除
            boolean deleted = file.delete();
            if (!deleted) {
                log.warn("无法删除临时文件：{}，可能需要检查文件系统权限", file.getAbsolutePath());
            }
        }
        return fileContent;
    }

    public void deleteSingle(String uuid) {
        if (StringUtils.isBlank(uuid)) {
            return;
        }
        AttachmentProxy attachmentProxy = FabosJsonUtil.findAttachmentProxy();
        if (attachmentProxy != null) {
            FabosJsonFile f = retrieveFile(uuid);
            if (f != null && StringUtils.isNotBlank(f.getPathOnOSS())) {
                attachmentProxy.remove(f.getBucket(), f.getFileUUID());
            }
            if (f != null && StringUtils.isNotBlank(f.getPathOnFileSystem())) {
                File file = new File(f.getPathOnFileSystem());
                if (file.exists()) {
                    log.debug("在本地文件系统检测到文件，执行删除: {}", f.getPathOnFileSystem());
                    file.delete();
                }
            }
        }
    }

    public void deleteMultiple(List<String> list) {
        AttachmentProxy attachmentProxy = FabosJsonUtil.findAttachmentProxy();
        if (attachmentProxy != null) {
            List<FabosJsonFile> retrievedList = fabosJsonDao.lambdaQuery(FabosJsonFile.class)
                    .addCondition("fileUUID in (:uuid) or modifiedFileName in (:fileName)", new HashMap<>() {{
                        this.put("uuid", list);
                        this.put("fileName", list);
                    }}).list();
            for (FabosJsonFile f : retrievedList) {
                if (StringUtils.isNotBlank(f.getPathOnOSS())) {
                    attachmentProxy.remove(f.getBucket(), f.getFileUUID());
                }
                if (StringUtils.isNotBlank(f.getPathOnFileSystem())) {
                    File file = new File(f.getPathOnFileSystem());
                    if (file.exists()) {
                        log.debug("在本地文件系统检测到文件，执行删除: {}", f.getPathOnFileSystem());
                        file.delete();
                    }
                }
            }
        }
    }

    /**
     * 执行文件上传核心逻辑
     *
     * @param file (二进制)文件
     * @param relativePath 相对路径，文件的父目录
     * @param modifiedFileName 唯一文件名
     * @param fabosJsonFileSuper 文件模型
     * @return 如有错误信息，返回错误信息。正常上传后返回null。
     */
    @Override
    public String createFile(MultipartFile file, String relativePath, String modifiedFileName, FabosJsonFileSuper fabosJsonFileSuper){
        boolean localSave = true;
        String msg = null;
        try {
            FabosJsonFile fabosJsonFile = fabosJsonFileSuper instanceof FabosJsonFile
                    ? (FabosJsonFile) fabosJsonFileSuper
                    : new FabosJsonFile(fabosJsonFileSuper);
            if (StringUtils.isBlank(relativePath)) {
                relativePath = File.separator + DateUtil.getFormatDate(new Date(), DateUtil.DATE);
            }

            File dest = new File(fabosJsonProp.getUploadPath() + relativePath + File.separator + modifiedFileName);
            if (!dest.getParentFile().exists()) {
                if (!dest.getParentFile().mkdirs()) {
                    throw new FabosJsonApiErrorTip("上传失败，文件目录无法创建");
                }
            }
            file.transferTo(dest);

            AttachmentProxy attachmentProxy = FabosJsonUtil.findAttachmentProxy();
            if (null != attachmentProxy) {
                HashMap<String, String> uploadResult = attachmentProxy.upLoad(dest, relativePath.replace("\\", FS_SEP) + FS_SEP + fabosJsonFile.getFileUUID());
                fabosJsonFile.setPathOnOSS(uploadResult.get("path"));
                fabosJsonFile.setBucket(uploadResult.get("bucket"));
                localSave = attachmentProxy.isLocalSave();
            }
            if (!localSave) {
                dest.delete();
            } else {
                fabosJsonFile.setPathOnFileSystem(dest.getAbsolutePath());
            }
            fabosJsonDao.persist(fabosJsonFile);
        } catch (Exception e) {
            log.error("fabosJson upload error", e);
            msg = e.getMessage();
        }
        return msg;
    }

    @Transactional
    public void changeFilename(String uuid,String newName) {
        FabosJsonFile file = retrieveFile(uuid);
        if (file != null) {
            file.setOriginalFileName(newName);
            fabosJsonDao.merge(file);
        }
    }

    private FabosJsonFile retrieveFile(String uuid) {
        if (StringUtils.isNotBlank(uuid)) {
            List<FabosJsonFile> retrievedList = fabosJsonDao.lambdaQuery(FabosJsonFile.class)
                    .addCondition("fileUUID = :uuid or modifiedFileName = :fileName", new HashMap<>() {{
                        this.put("uuid", uuid);
                        this.put("fileName", uuid);
                    }}).list();
            if (CollectionUtils.isEmpty(retrievedList) || retrievedList.size() > 1) {
                throw new FabosJsonApiErrorTip("根据文件ID：" + uuid + "找到了多个文件，请检查数据。");
            }
            return retrievedList.get(0);
        } else {
            return null;
        }
    }

    public FrUploadTransactionFilePO transformToPO(FabosJsonFile fabosJsonFile) {
        if (fabosJsonFile == null) {
            return null;
        }
        FrUploadTransactionFilePO po = new FrUploadTransactionFilePO();
        po.setUuid(fabosJsonFile.getFileUUID());
        po.setFileName(fabosJsonFile.getOriginalFileName());
//        po.setFileSourceCode(null);
        po.setFileUrl(StringUtils.isNotBlank(fabosJsonFile.getPathOnOSS())
                ? fabosJsonFile.getPathOnOSS()
                : fabosJsonFile.getPathOnFileSystem());
        // 这里有一个不一致的点，旧框架存储的文件类型是不带前置.的
        po.setFileTypeCode(fabosJsonFile.getFilenameExtension().replaceFirst(".", ""));
        po.setFileSizeNumber(Double.valueOf(fabosJsonFile.getSizeInBytes()));
//        po.setFileTagIdentifier();
        po.setTransactionCompleteFlag("Y");
//        po.setTransactionIdentifier();
        if (fabosJsonFile.getCreateTime() != null) {
            po.setCreateTime(Date.from(fabosJsonFile.getCreateTime().atZone(ZoneId.systemDefault()).toInstant()));
        }
        po.setCreateUser(fabosJsonFile.getCreateBy());
//        po.setLastEventName();
        if (fabosJsonFile.getUpdateTime() != null) {
            po.setLastEventTime(Date.from(fabosJsonFile.getUpdateTime().atZone(ZoneId.systemDefault()).toInstant()));
        }
        po.setLastEventUser(fabosJsonFile.getUpdateBy());
//        po.setLastEventComment();
        return po;
    }

    public Object getFileInfoByUUIDs(String uuid) {
        if (StringUtils.isNotBlank(uuid)) {
            List<FabosJsonFile> retrievedList = fabosJsonDao.lambdaQuery(FabosJsonFile.class)
                    .addCondition("fileUUID in (:uuid) or modifiedFileName in (:fileName)", new HashMap<>() {{
                        this.put("uuid", Arrays.asList(uuid.split(",")));
                        this.put("fileName", Arrays.asList(uuid.split(",")));
                    }}).list();
            if (!CollectionUtils.isEmpty(retrievedList)) {
                return retrievedList.stream().map(this::transformToPO).toList();
            }
        }
        return Collections.emptyList();
    }

    public void downloadById(HttpServletResponse response, String uuid) {
//        tokenAnalysisService.genToken("60f7363e-ec33-4cf1-a596-54ced9ecfdeb");
        FabosJsonFile fabosJsonFile = retrieveFile(uuid);
        if (fabosJsonFile != null) {
            FrUploadTransactionFilePO frUploadTransactionFile = transformToPO(fabosJsonFile);
            // 省略了eTag相关部分
//            HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
//            String header = request.getHeader("If-None-Match");
//            if (StringUtils.isNotBlank(frUploadTransactionFile.getFileTagIdentifier()) && StringUtils.equals(header, frUploadTransactionFile.getFileTagIdentifier())) {
//                response.setStatus(HttpStatus.SC_NOT_MODIFIED);
//            } else {
//            frUploadTransactionFile.setFileName(frUploadTransactionFile.getFileName());
            frUploadTransactionFile.setFileName(AsciiUtils.stringToAscii(frUploadTransactionFile.getFileName()));
            response.setHeader("File-Info", JSON.toJSONString(frUploadTransactionFile));
//            response.setHeader("File-Info", AsciiUtils.stringToAscii(JSON.toJSONString(frUploadTransactionFile)));
            oldDownload(response, null, fabosJsonFile, frUploadTransactionFile);
//            }
        } else {
            response.setStatus(HttpStatus.SC_NOT_FOUND);
        }
    }


    @SneakyThrows
    public void oldDownload(HttpServletResponse response, String etag, FabosJsonFile fabosJsonFile, FrUploadTransactionFilePO frUploadTransactionFile) {
//        ServletOutputStream out = null;
//        try {
            byte[] buffer = performDownload(fabosJsonFile);
            if (null == buffer) {
                FileUtil.set404Status(response);
                log.error("文件下载失败，文件不存在：{}", fabosJsonFile.getFileUUID());
                return;
            }
            response.addHeader("Content-Type", ContentTypeUtils.getContentType(ContentTypeConstant.class, frUploadTransactionFile.getFileTypeCode()));
            response.addHeader("Content-Disposition","attachment; filename=" + URLEncoder.encode(fabosJsonFile.getOriginalFileName(), StandardCharsets.UTF_8).replaceAll("\\+", "%20"));
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            if (StringUtils.isNotBlank(etag)) {
                response.addHeader("Cache-Control", "no-cache");
                response.addHeader("Etag", etag);
            }
            response.setStatus(HttpStatus.SC_OK);
            response.getOutputStream().write(buffer);

//        } catch (IOException e) {
//            ExceptionUtils.throwException(e, "文件处理失败");
//        } finally {
//            try {
//                if (null != fis) {
//                    fis.close();
//                }
//            } catch (IOException e) {
//                log.error(e.getMessage());
//            }
//        }
    }
}
