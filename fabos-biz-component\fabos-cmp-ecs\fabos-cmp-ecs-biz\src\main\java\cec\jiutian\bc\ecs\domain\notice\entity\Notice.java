//package cec.jiutian.bc.ecs.domain.notice.entity;
//
//import cec.jiutian.bc.ecs.domain.notice.event.NoticeDataProxy;
//import cec.jiutian.bc.ecs.domain.notice.event.NoticeFlowProxy;
//import cec.jiutian.bc.ecs.domain.notice.event.NoticeOperationHandler;
//import cec.jiutian.bc.ecs.enums.MessageWayEnum;
//import cec.jiutian.bc.flow.domain.model.entity.ExamineModel;
//import cec.jiutian.core.frame.module.MetaUser;
//import cec.jiutian.core.view.fabosJson.FabosJson;
//import cec.jiutian.view.FabosJsonField;
//import cec.jiutian.view.field.Edit;
//import cec.jiutian.view.field.EditType;
//import cec.jiutian.view.field.View;
//import cec.jiutian.view.field.ViewType;
//import cec.jiutian.view.field.edit.ChoiceType;
//import cec.jiutian.view.field.edit.ReferenceTableType;
//import cec.jiutian.view.field.edit.Search;
//import cec.jiutian.view.type.Filter;
//import cec.jiutian.view.type.Power;
//import cec.jiutian.view.type.RowBaseOperation;
//import cec.jiutian.view.type.RowOperation;
//import com.fasterxml.jackson.annotation.JsonIgnore;
//import jakarta.persistence.*;
//import lombok.Getter;
//import lombok.Setter;
//
//import java.io.Serializable;
//import java.util.List;
//
//
//@Getter
//@Setter
//@Entity
//@Table(name = "ecs_notice")
//@FabosJson(name = "通知公告", dataProxy = {NoticeDataProxy.class},
//        orderBy = "Notice.createTime desc",
//        rowBaseOperation = {
//                @RowBaseOperation(
//                        code = "edit",
//                        ifExpr = "examineStatus == 1 || examineStatus == 3",
//                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
//                ),
//                @RowBaseOperation(
//                        code = "delete",
//                        ifExpr = "examineStatus == 1 || examineStatus == 3",
//                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
//                ),
//                @RowBaseOperation(
//                        code = "examine",
//                        ifExpr = "examineStatus == 1 || examineStatus == 3",
//                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
//                )
//        },
//        rowOperation = {@RowOperation(
//                operationHandler = NoticeOperationHandler.class,
//                code = "reSend",
//                mode = RowOperation.Mode.SINGLE,
//                title = "重发",
//                type = RowOperation.Type.FABOSJSON
//        )},
//        flowProxy = {NoticeFlowProxy.class},
//        flowCode = "Notice",
//        power = @Power(examine = true, examineDetails = true)
//)
//public class Notice extends ExamineModel implements Serializable {
//
//    @FabosJsonField(
//            views = @View(title = "推送方式"),
//            edit = @Edit(title = "推送方式",
//                    type = EditType.multiple_select,
//                    notNull = true,
//                    choiceType = @ChoiceType(fetchHandler = MessageWayEnum.ChoiceFetch.class)
//            )
//    )
//    private String dispatchWay;
//
//    @FabosJsonField(
//            views = @View(title = "推送人员账号", show = false),
//            edit = @Edit(title = "推送人员账号", show = false)
//    )
//    private String dispatchUserAccount;
//
//    @ManyToMany(fetch = FetchType.LAZY)
//    @JoinTable(
//            name = "ecs_notice_user",
//            joinColumns = @JoinColumn(name = "notice_id", referencedColumnName = "id"),
//            inverseJoinColumns = @JoinColumn(name = "user_id", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)))
//    @FabosJsonField(
//            views = @View(title = "推送人员", column = "name", show = false, type = ViewType.text),
//            edit = @Edit(title = "推送人员",
//                    filter = @Filter(value = "MetaUser.state = 'Y'"),
//                    notNull = true,
//                    type = EditType.TAB_TABLE_REFER,
//                    referenceTableType = @ReferenceTableType)
//    )
//    @JsonIgnore
//    private List<MetaUser> users;
//
//    @FabosJsonField(
//            views = @View(title = "标题"),
//            edit = @Edit(title = "标题", notNull = true, search = @Search(vague = true))
//    )
//    private String title;
//
//    @FabosJsonField(
//            views = @View(title = "消息内容"),
//            edit = @Edit(title = "消息内容", notNull = true, search = @Search(vague = true))
//    )
//    private String content;
//
//    @FabosJsonField(
//            views = @View(title = "备注", show = false),
//            edit = @Edit(title = "备注", show = false)
//    )
//    @Column(name = "LST_EVNT_CMNT")
//    private String lastEventComment;
//
//    @FabosJsonField(
//            views = @View(title = "推送人员", show = true),
//            edit = @Edit(title = "推送人员", search = @Search(vague = true), show = false)
//    )
//    private String dispatchUsers;
//
//}
