package cec.jiutian.bc.infra.handler;

import cec.jiutian.bc.ecs.outbound.adapter.client.EcsDictClient;
import cec.jiutian.core.frame.cache.FabosJsonCache;
import cec.jiutian.core.frame.cache.FabosJsonCacheLRU;
import cec.jiutian.core.frame.util.FabosJsonAssert;
import cec.jiutian.view.fun.ChoiceFetchHandler;
import cec.jiutian.view.fun.VLModel;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class EcsChoiceFetchHandler implements ChoiceFetchHandler {

    @Resource
    private EcsDictClient ecsDictClient;

    private final FabosJsonCache<List<VLModel>> dictCache = new FabosJsonCacheLRU<>(500);

    @Override
    public List<VLModel> fetch(String[] params) {
        FabosJsonAssert.notNull(params, EcsChoiceFetchHandler.class.getSimpleName() + " → params[0] must dict → code");
        return dictCache.getAndSet(EcsChoiceFetchHandler.class.getName() + ":" + params[0],
                params.length == 2 ? Long.parseLong(params[1]) : 3000, () -> ecsDictClient.getDictItems(params[0])
                                .stream().map((item) -> new VLModel(item.getCode(), item.getName())).collect(Collectors.toList()));
    }

    @Override
    public boolean isDataBaseQuery() {
        return true;
    }
}
