package cec.jiutian.bc.urm.inbound.local.service.command;

import cec.jiutian.bc.urm.ModelRowPermission.entity.ModelRowPermission;
import cec.jiutian.bc.urm.domain.ModelColumnPermission.entity.ModelColumnPermission;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.meta.PermissionLevelEnum;
import cec.jiutian.bc.urm.domain.user.service.UserService;
import cec.jiutian.bc.urm.dto.MetaUserinfo;
import cec.jiutian.common.util.JacksonUtil;
import cec.jiutian.core.frame.exception.FabosJsonWebApiRuntimeException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.service.FabosJsonSessionService;
import cn.hutool.core.map.MapUtil;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.TypedQuery;

import java.io.IOException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static cec.jiutian.common.constant.SessionKey.FIELD_PERMISSIONS;
import static cec.jiutian.common.constant.SessionKey.ROW_PERMISSIONS;

/**
 * <AUTHOR>
 * @time 2024-10-27 19:16
 */


@Service
@Slf4j
public class RowColumnPermissionService {

    @Resource
    private FabosJsonSessionService sessionService;
    @Resource
    private UserService userService;
    @Resource
    private FabosJsonDao fabosJsonDao;

    public Map<String, Map<String, String>> getRowPermissionFromDb(List<String> roleIds) {

        EntityManager entityManager = fabosJsonDao.getEntityManager();
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<ModelRowPermission> criteriaQuery = criteriaBuilder.createQuery(ModelRowPermission.class);
        Root<ModelRowPermission> root = criteriaQuery.from(ModelRowPermission.class);
        criteriaQuery.where(
                criteriaBuilder.and(root.get("roleId").in(roleIds))
        );
        TypedQuery<ModelRowPermission> typedQuery = entityManager.createQuery(criteriaQuery);

        // 执行查询并获取结果
        List<ModelRowPermission> records = typedQuery.getResultList();
        if (CollectionUtils.isNotEmpty(records)) {
            return records.stream().collect(Collectors.groupingBy(
                    ModelRowPermission::getRoleId,
                    Collectors.toMap(
                            ModelRowPermission::getMetadataId,
                            ModelRowPermission::getFilter,
                            (v1, v2) -> {
                                // 合并策略，如果有重复的key，说明配置不正确，取v1是为了流程正常流转
                                log.error("检测到行权限有重复的配置项，请检查filter为{}、{}的配置", v1, v2);
                                return v1;
                            }
                    ))
            );
        }
        return new HashMap<>();
    }

    public Map<String, Map<String, Integer>> getColumnPermissionFromDb(List<String> roleIds) {

        EntityManager entityManager = fabosJsonDao.getEntityManager();

        // 创建 CriteriaBuilder 和 CriteriaQuery 对象
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<ModelColumnPermission> criteriaQuery = criteriaBuilder.createQuery(ModelColumnPermission.class);

        // 设置查询条件
        Root<ModelColumnPermission> root = criteriaQuery.from(ModelColumnPermission.class);
        criteriaQuery.where(
                criteriaBuilder.and(root.get("roleId").in(roleIds))
        );
        TypedQuery<ModelColumnPermission> typedQuery = entityManager.createQuery(criteriaQuery);

        // 执行查询并获取结果
        List<ModelColumnPermission> records = typedQuery.getResultList();
        if (CollectionUtils.isNotEmpty(records)) {
            return records.stream().collect(Collectors.groupingBy(
                    ModelColumnPermission::getRoleId,
                    Collectors.toMap(
                            ModelColumnPermission::getFieldId,
                            ModelColumnPermission::getPermissionLevel,
                            (v1, v2) -> {
                                // 合并策略，如果有重复的key，说明配置不正确，取v1是为了流程正常流转
                                log.error("检测到列权限有重复的配置项，请检查");
                                return v1;
                            }
                    ))
            );
        }
        return new HashMap<>();
    }

    // 已迁移到FabosJsonJpaDao内
//    public Map<String, String> getRowPermissionFromCache(String metadataModelId) {
//        MetaUserinfo userInfo = userService.getSimpleUserInfo();
//        if (Objects.isNull(userInfo) || CollectionUtils.isEmpty(userInfo.getRoleIds())) {
//            return null;
//        }
//        Map<String, String> realResult = new HashMap<>();
//        for (String roleId : userInfo.getRoleIds()) {
//            try {
//                Map<String, Object> cachedResult = JacksonUtil.fromJsonToMap(String.valueOf(
//                        sessionService.get(ROW_PERMISSIONS + roleId)
//                ));
//                if (MapUtil.isNotEmpty(cachedResult) && cachedResult.containsKey(metadataModelId)) {
//                    realResult.put(metadataModelId, String.valueOf(cachedResult.get(metadataModelId)));
//                }
//            } catch (IOException e) {
//                throw new RuntimeException(e);
//            }
//        }
//        return realResult;
//    }


    public Map<String, Integer> getColumnPermissionFromCache(List<String> metadataIds) {
        Map<String, Integer> realResult = new HashMap<>();
        UserContext.CurrentUser userInfo = UserContext.get();
        if (Objects.isNull(userInfo)
                || userInfo.getSuperAdmin()
                || userInfo.getHashAdmin()
                || CollectionUtils.isEmpty(userInfo.getRoleIds())
                || CollectionUtils.isEmpty(metadataIds)) {
            return new HashMap<>();
        }
        // 用set去重，并且其contains()时间复杂度为O(1)
        Set<String> metadataSet = new HashSet<>(metadataIds);

        // <roleId, <fieldId, permissionLevel>>
        Map<String, Map<String, Integer>> role2Permission = new HashMap<>();

        for (String roleId : userInfo.getRoleIds()) {
            Map<String, Object> cachedResult = null;
            try {
                cachedResult = JacksonUtil.fromJsonToMap(String.valueOf(
                        sessionService.get(FIELD_PERMISSIONS + roleId)
                ));
            } catch (Exception e) {
                log.error(e.getMessage());
                throw new FabosJsonWebApiRuntimeException("权限配置出现错误，请联系管理员");
            }
            // 从redis获取数据后，转换为Integer类型，如果是前端需要的权限，才写入
            if (MapUtil.isNotEmpty(cachedResult)) {
                Map<String, Integer> tempMapForTypeCast = new HashMap<>();
                cachedResult.forEach((fieldId, lvl) -> {
                    if (metadataSet.contains(fieldId)) {
                        tempMapForTypeCast.put(fieldId, Integer.parseInt(String.valueOf(lvl)));
                    }
                });
                role2Permission.put(roleId, tempMapForTypeCast);
            }
        }

        // 为全部未配置的 fieldId 补差赋值为读写
        metadataSet.forEach(f -> {
            role2Permission.forEach((roleId, permissionMap) -> {
                permissionMap.putIfAbsent(f, PermissionLevelEnum.READ_WRITE);
            });
        });

        //
        role2Permission.forEach((roleId, permissionMap) -> {
            permissionMap.forEach((fieldId, lvl) -> {
                if (!PermissionLevelEnum.NONE.getLevel().equals(lvl)
                        && !PermissionLevelEnum.READ_ONLY.getLevel().equals(lvl)
                        && !PermissionLevelEnum.READ_WRITE.equals(lvl)) {
                    throw new RuntimeException("Permission conf is incorrect");
                }
                if (metadataSet.contains(fieldId)) {
                    realResult.merge(fieldId, lvl, Integer::max);
                }
            });
        });
        // 移除realResult内value为 读写 的key
        realResult.entrySet().removeIf(entry -> Objects.equals(entry.getValue(), PermissionLevelEnum.READ_WRITE));
        return realResult;

    }

}
