package cec.jiutian.bc.generalModeler.domain.test.model;

import cec.jiutian.core.frame.constant.SwitchStatus;
import cec.jiutian.core.frame.jpa.dao.FabosJsonJpaUtils;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.core.view.fabosJson.QueryModel;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Id;
import lombok.Data;

@Data
@FabosJson(name = "库存台账",
        power = @Power(add = false, edit = false, delete = false)
)
@FabosJsonI18n
@QueryModel(hql = "select new map (CONCAT(u.id, '#', r.id) as id,u.name as userName, r.name as roleName,u.state as state) from User u left join u.roles r")
//@QueryModel(hql = "select new map (u.id as id,u.name as userName, r.name as roleName,u.state as state) from User u left join u.roles r")
public class SplitModelDto {

    @FabosJsonField(
            views = @View(title = "id"),
            edit = @Edit(title = "id"),
            customHqlField = "u.id" + FabosJsonJpaUtils.CUSTOM_SPLIT + "r.id"
    )
    @Id
    private String id;
    @FabosJsonField(
            views = @View(title = "用户姓名"),
            edit = @Edit(title = "用户姓名", search = @Search()),
            customHqlField = "u.name"
    )
    private String userName;
    @FabosJsonField(
            views = @View(title = "角色名称"),
            edit = @Edit(title = "角色名称", search = @Search()),
            customHqlField = "r.name"
    )
    private String roleName;

    @FabosJsonField(
            views = @View(title = "用户状态"),
            edit = @Edit(title = "状态", search = @Search(defaultVal = "Y"),
                    defaultVal = "Y",
                    notNull = true,
                    type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = {SwitchStatus.ChoiceFetch.class})),
            customHqlField = "u.state"
    )
    private String state;

}
