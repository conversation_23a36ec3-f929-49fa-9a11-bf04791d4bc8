//package cec.jiutian.bc.urm.inbound.remote.rpc;
//import cec.jiutian.functionexecutor.dto.InvokeDTO;
//import cec.jiutian.functionexecutor.executors.component.BizExecutor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//@Slf4j
//@Component
//public class FunctionExecutorProvider extends BizExecutor{
//    @Override
//    public Object doInvoke(InvokeDTO invokeDTO) {
//        ClassLoader classLoader = FunctionExecutorProvider.class.getClassLoader();
//        return execute(invokeDTO,context,classLoader);
//    }
//}
