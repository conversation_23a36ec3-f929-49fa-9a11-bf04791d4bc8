package cec.jiutian.bc.urm.domain.systemBasicConfig.entity;

import cec.jiutian.bc.urm.domain.systemBasicConfig.event.SystemBasicConfigDataProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.AttachmentType;
import cec.jiutian.view.type.Power;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "system_basic_config")
@Getter
@Setter
@FabosJson(
        name = "基础配置",
        orderBy = "SystemBasicConfig.createTime desc",
        dataProxy = {SystemBasicConfigDataProxy.class},
        power = @Power(delete = false)
)
public class SystemBasicConfig extends MetaModel {
    // 系统名称
    @FabosJsonField(
            views = @View(title = "系统名称"),
            edit = @Edit(title = "系统名称", notNull = true)
    )
    private String systemName;

    // 系统Logo
    @FabosJsonField(
            views = @View(title = "系统Logo"),
            edit = @Edit(title = "系统Logo",
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(fileTypes = {".jpg,.png"}, size = 5120,
                            type = AttachmentType.Type.IMAGE,
                            nonProtected = true)
            )
    )
    private String systemLogo;

    // 密级标识
    @FabosJsonField(
            views = @View(title = "密级标识"),
            edit = @Edit(title = "密级标识",
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(fileTypes = {".jpg,.png"}, size = 5120,
                            type = AttachmentType.Type.IMAGE,
                            nonProtected = true)
            )
    )
    private String securityLevel;

    // 版权信息
    @FabosJsonField(
            views = @View(title = "版权信息"),
            edit = @Edit(title = "版权信息")
    )
    private String copyrightInfo;

    // 首页背景图
    @FabosJsonField(
            views = @View(title = "首页背景图"),
            edit = @Edit(title = "首页背景图",
                    type = EditType.ATTACHMENT,
                    attachmentType = @AttachmentType(fileTypes = {".jpg,.png"}, size = 5120,
                            type = AttachmentType.Type.IMAGE,
                            nonProtected = true)
            )
    )
    private String homeBackground;

    @FabosJsonField(
            views = @View(title = "系统应用ID", show = false),
            edit = @Edit(title = "系统应用ID", show = false)
    )
    private String systemApplicationId;
}
