package cec.jiutian.bc.generalModeler.domain.warehouseArea.handler;

import cec.jiutian.bc.generalModeler.domain.warehouseArea.model.WarehouseShelf;
import cec.jiutian.bc.generalModeler.enumeration.WarehouseStateEnum;
import cec.jiutian.data.jpa.JpaCrud;
import cec.jiutian.view.fun.OperationHandler;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class WarehouseShelfUnHoldHandler implements OperationHandler<WarehouseShelf, Void> {

    @Resource
    private JpaCrud jpaCrud;

    @Override
    public String exec(List<WarehouseShelf> data, Void modelObject, String[] param) {
        if (CollectionUtils.isNotEmpty(data)) {
            WarehouseShelf warehouseShelf = data.get(0);
            warehouseShelf.setLockState(WarehouseStateEnum.Enum.Normal.name());
            jpaCrud.update(warehouseShelf);
        }
        return "msg.success('操作成功')";
    }
}
