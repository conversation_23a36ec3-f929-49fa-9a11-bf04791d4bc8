package cec.jiutian.bc.generalModeler.domain.warehouseArea.model;


import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.RowBaseOperation;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

@FabosJson(
        name = "库区详情",
        orderBy = "WarehouseBlockDetail.createTime desc",
        rowBaseOperation = {
                @RowBaseOperation(
                        code = "edit",
                        ifExpr = "1==1",
                        ifExprBehavior = RowBaseOperation.IfExprBehavior.DISABLE
                )
        }
)
@Table(name = "mos_warehouse_block_detail",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"requestDetailNumber"})
        }
)
@Entity
@Getter
@Setter
public class WarehouseBlockDetail extends MetaModel {

    @FabosJsonField(
            views = @View(title = "单据编号", show = false),
            edit = @Edit(title = "单据编号", show = false, search = @Search(vague = true),
                    readonly = @Readonly(add = false), inputType = @InputType(length = 40))
    )
    private String requestDetailNumber;

    @FabosJsonField(
            views = {
                    @View(title = "库区编码", column = "code", show = false)
            },
            edit = @Edit(title = "库区编码", type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "code")
            )
    )
    @ManyToOne
    @JsonIgnoreProperties("warehouseBlockDetails")
    private WarehouseBlock warehouseBlock;

    @FabosJsonField(
            views = @View(title = "物料主键", show = false),
            edit = @Edit(title = "物料主键", show = false)
    )
    private String materialId;

    @FabosJsonField(
            views = @View(title = "物料编码"),
            edit = @Edit(title = "物料编码", readonly = @Readonly())
    )
    private String materialCode;

    @FabosJsonField(
            views = @View(title = "物料名称"),
            edit = @Edit(title = "物料名称", readonly = @Readonly())
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "物料分类"),
            edit = @Edit(title = "物料分类", readonly = @Readonly())
    )
    private String materialCategory;

    @FabosJsonField(
            views = @View(title = "物料规格"),
            edit = @Edit(title = "物料规格", readonly = @Readonly())
    )
    private String materialSpecification;

}
