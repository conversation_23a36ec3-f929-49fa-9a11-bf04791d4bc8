package cec.jiutian.bc.generalModeler.domain.warehouseArea.model;


import cec.jiutian.bc.generalModeler.domain.warehouseArea.handler.WarehouseShelfHoldHandler;
import cec.jiutian.bc.generalModeler.domain.warehouseArea.handler.WarehouseShelfUnHoldHandler;
import cec.jiutian.bc.generalModeler.domain.warehouseArea.proxy.WarehouseShelfProxy;
import cec.jiutian.bc.generalModeler.enumeration.WarehouseShelfTypeEnum;
import cec.jiutian.bc.generalModeler.enumeration.WarehouseStateEnum;
import cec.jiutian.bc.generalModeler.enumeration.YesOrNoEnum;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.DynamicField;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.LinkedFiled;
import cec.jiutian.view.config.Comment;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.ChoiceType;
import cec.jiutian.view.field.edit.DependFieldDisplay;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.ReferenceTableType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

@FabosJson(
        name = "货位",
        dataProxy = WarehouseShelfProxy.class,
        orderBy = "WarehouseShelf.createTime desc",
        rowOperation = {
                @RowOperation(
                        code = "WarehouseShelf@Hold",
                        operationHandler = WarehouseShelfHoldHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        title = "锁定",
                        ifExpr = "lockState == 'Locked'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE),
                @RowOperation(
                        code = "WarehouseShelf@UnHold",
                        operationHandler = WarehouseShelfUnHoldHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "是否确定操作？",
                        title = "解锁",
                        ifExpr = "lockState == 'Normal'",
                        ifExprBehavior = RowOperation.IfExprBehavior.DISABLE),
        }
)
@Table(name = "mos_warehouse_shelf",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"code", "oid"}),
                @UniqueConstraint(columnNames = {"name", "oid"})
        }
)
@Entity
@Getter
@Setter
public class WarehouseShelf extends MetaModel {
    @FabosJsonField(
            views = @View(title = "货位编码"),
            edit = @Edit(title = "货位编码", notNull = true, search = @Search(vague = true),
                    readonly = @Readonly(add = false, edit = true), inputType = @InputType(length = 40))
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "货位名称"),
            edit = @Edit(title = "货位名称", notNull = true, search = @Search(vague = true),
                    readonly = @Readonly(add = false, edit = true), inputType = @InputType(length = 40))
    )
    private String name;


    @FabosJsonField(
            views = @View(title = "描述"),
            edit = @Edit(title = "描述", type = EditType.input_text)
    )
    private String description;

    @Comment("引用字段，对应warehouseBlock的ID")
    @FabosJsonField(
            views = @View(title = "所属库区", column = "name"),
            edit = @Edit(title = "所属库区", notNull = true, search = @Search(vague = true),
                    type = EditType.REFERENCE_TABLE,
                    referenceTableType = @ReferenceTableType(id = "id", label = "name"/*, dependField = "warehouse", dependColumn = "warehouseId"*/)
            )
    )
    @ManyToOne
    private WarehouseBlock warehouseBlock;


    @FabosJsonField(
            views = @View(title = "库区类型"),
            edit = @Edit(title = "库区类型", notNull = true, type = EditType.CHOICE, readonly = @Readonly(),
                    choiceType = @ChoiceType(fetchHandler = WarehouseShelfTypeEnum.class)),
            dynamicField = @DynamicField(linkedFiled = @LinkedFiled(changeBy = "warehouseBlock", beFilledBy = "type"))
    )
    private String type;

    @FabosJsonField(
            views = @View(title = "货架层数"),
            edit = @Edit(title = "货架层数", numberType = @NumberType(maxExpr = "${warehouseBlock.rowQuantity}"),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "type == 'Stereo'"))
    )
    // 立库货位填写 todo 关联库区类型 同时以库区的行数为最大值
    private Integer blockRow;

    @FabosJsonField(
            views = @View(title = "货架列数"),
            edit = @Edit(title = "货架列数", type = EditType.NUMBER, numberType = @NumberType(maxExpr = "${warehouseBlock.columnQuantity}"),
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "type == 'Stereo'"))
    )
    // 立库货位填写 todo 关联库区类型 同时以库区的列数为最大值
    private Integer blockColumn;

    @FabosJsonField(
            views = @View(title = "盘点锁"),
            edit = @Edit(title = "盘点锁", show = false, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = WarehouseStateEnum.class))
    )
    private String lockState;

//    @FabosJsonField(
//            views = @View(title = "库位管理模式"),
//            edit = @Edit(title = "库位管理模式", notNull = true, type = EditType.CHOICE,
//                    choiceType = @ChoiceType(fetchHandler = WarehouseTypeEnum.class))
//    )
//    private String manageModel;

    @FabosJsonField(
            views = @View(title = "货位是否已满"),
            edit = @Edit(title = "货位是否已满", notNull = true, type = EditType.CHOICE,
                    defaultVal = "N",
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class))
    )
    private String fullFlag;

    @FabosJsonField(
            views = @View(title = "是否多批次混放"),
            edit = @Edit(title = "是否多批次混放", notNull = true, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class),
                    defaultVal = "Y")
    )
    private String multipleLotFlag;

    @FabosJsonField(
            views = @View(title = "是否多规格混放"),
            edit = @Edit(title = "是否多规格混放", notNull = true, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class),
                    defaultVal = "Y")
    )
    private String multipleSpecificationFlag;


    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注", type = EditType.TEXTAREA)
    )
    private String remark;

//    @FabosJsonField(
//            views = @View(title = "库位使用模式"),
//            edit = @Edit(title = "库位使用模式", notNull = true, type = EditType.CHOICE,
//                    choiceType = @ChoiceType(fetchHandler = WarehouseTypeEnum.class))
//    )
//    private String usageType;
//
//    @FabosJsonField(
//            views = @View(title = "库位承载类型"),
//            edit = @Edit(title = "库位承载类型", notNull = true, type = EditType.CHOICE,
//                    choiceType = @ChoiceType(fetchHandler = WarehouseTypeEnum.class))
//    )
//    private String supportType;

//    @FabosJsonField(
//            views = @View(title = "出入库锁"),
//            edit = @Edit(title = "出入库锁", show = false, type = EditType.CHOICE,
//                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class))
//    )
//    private String inboundOutboundLock;

/*    @FabosJsonField(
            views = @View(title = "地面标识"),
            edit = @Edit(title = "地面标识", type = EditType.input_text,
                    dependFieldDisplay = @DependFieldDisplay(showOrHide = "WarehouseBlock.type == 'Block'"))
    )
    // 平库货位填写，对应地面标识号 todo 如何关联库区的类型
    private String groundSign;





    @FabosJsonField(
            views = @View(title = "是否允许存放多品类"),
            edit = @Edit(title = "是否允许存放多品类", notNull = true, type = EditType.CHOICE,
                    choiceType = @ChoiceType(fetchHandler = YesOrNoEnum.class),
                    defaultVal = "Y")
    )
    private String multipleCategoryFlag;*/

//    @Comment("引用字段，对应warehouse的ID")
//    @FabosJsonField(
//            views = @View(title = "所属仓库", column = "name"),
//            edit = @Edit(title = "所属仓库", notNull = true, search = @Search(vague = true),
//                    type = EditType.REFERENCE_TABLE,
//                    referenceTableType = @ReferenceTableType(id = "id", label = "name", type = SelectShowTypeMTO.DROPDOWN_TABLE)
//            )
//    )
//    @ManyToOne
//    private Warehouse warehouse;

}
