//package cec.jiutian.bc.ecs.domain.extendFabosJsonTest;
//
//import cec.jiutian.common.util.BeanUtils;
//import cec.jiutian.view.fun.OperationHandler;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
//
//@Component
//@Slf4j
//public class DogFormOperationHandler implements OperationHandler<Dog, Dog> {
//
//
//    @Override
//    public String exec(List<Dog> data, Dog modelObject, String[] param) {
//        log.info("DogOperationHandler.exec");
//        return "msg.success()";
//    }
//
//    @Override
//    public Dog fabosJsonFormValue(List<Dog> data, Dog fabosJsonForm, String[] param) {
//        log.info("DogOperationHandler.fabosJsonFormValue");
//        return OperationHandler.super.fabosJsonFormValue(data, fabosJsonForm, param);
//    }
//}
