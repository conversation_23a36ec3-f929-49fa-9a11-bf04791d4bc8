package cec.jiutian.bc.ecs.domain.template.entity;

import cec.jiutian.bc.ecs.domain.template.event.TemplateDataProxy;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.SubTableField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@Entity
@Table(name = "ecs_template",
       indexes = {@Index(columnList = "template_name, oid", unique = true)})
@FabosJson(name = "消息模板", dataProxy = {TemplateDataProxy.class},
        orderBy = "Template.createTime desc")
public class Template extends MetaModel implements Serializable {

    @FabosJsonField(
            views = @View(title = "模板名称"),
            edit = @Edit(title = "模板名称", notNull = true, search = @Search(vague = true))
    )
    @SubTableField
    private String name;

    @FabosJsonField(
            views = @View(title = "模板描述"),
            edit = @Edit(title = "模板描述", notNull = true, search = @Search(vague = true))
    )
    @SubTableField
    private String templateDesc;

    @FabosJsonField(
            views = @View(title = "备注", show = false),
            edit = @Edit(title = "备注", show = false)
    )
    @Column(name = "LST_EVNT_CMNT")
    private String lastEventComment;

}
