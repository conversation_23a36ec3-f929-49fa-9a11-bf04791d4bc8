package cec.jiutian.bc.urm.inbound.local.filter;

import cec.jiutian.bc.urm.domain.systemSecurityConfig.entity.SystemSecurityConfig;
import cec.jiutian.bc.urm.inbound.local.context.SecurityConfigContext;
import cec.jiutian.core.frame.exception.FabosJsonWebApiRuntimeException;
import cec.jiutian.data.jpa.JpaCrud;
import jakarta.annotation.Resource;
import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @time 2024-11-18 16:54
 */

@Component
@Slf4j
public class SecurityConfigFilter implements Filter {

    @Resource
    private JpaCrud jpaCrud;
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        Filter.super.init(filterConfig);
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        Pattern pattern = Pattern.compile(".*(/login$)");
        // 如果是登录接口，查询系统安全配置
        if (pattern.matcher(request.getRequestURI()).matches()) {
            SystemSecurityConfig securityConfig = jpaCrud.selectOne(new SystemSecurityConfig());
            if (Objects.isNull(securityConfig)) {
                log.error("未在数据库查询到系统安全配置，请管理员予以添加");
                throw new FabosJsonWebApiRuntimeException("未在数据库查询到系统安全配置，请管理员予以添加");
            }
            SecurityConfigContext.set(securityConfig);
        }
        chain.doFilter(servletRequest, servletResponse);
    }

    @Override
    public void destroy() {
        Filter.super.destroy();
    }
}
