package cec.jiutian.bc.generalModeler.domain.material.model;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

@FabosJson(
        name = "物料字段"
)
@Table(
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"code"})
        }
)
@Entity
@Getter
@Setter
public class MaterialModelField extends MetaModel {
    @FabosJsonField(
            views = @View(title = "编码"),
            edit = @Edit(title = "编码", notNull = true)
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "显示名称"),
            edit = @Edit(title = "显示名称", notNull = true)
    )
    private String displayName;


}
