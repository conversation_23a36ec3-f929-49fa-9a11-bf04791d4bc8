//package cec.jiutian.bc.generalModeler.remote.rpc;
//
//import cec.jiutian.functionexecutor.consumer.DubboGenericAPI;
//import cec.jiutian.functionexecutor.dto.InvokeDTO;
//import org.springframework.stereotype.Service;
//
///**
// * <AUTHOR>
// * @description:
// */
//@Service
//public class DubboGenericAPIImpl extends DubboGenericAPI {
//    @Override
//    public Object doInvoke(InvokeDTO invokeDTO) {
//        return super.functionExecutor.doInvoke(invokeDTO);
//    }
//}
