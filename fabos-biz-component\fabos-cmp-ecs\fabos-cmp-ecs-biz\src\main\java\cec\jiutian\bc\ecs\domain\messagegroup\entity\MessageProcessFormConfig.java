package cec.jiutian.bc.ecs.domain.messagegroup.entity;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> @description:
 */
@Entity
@Table(name = "ecs_message_process_form_config"
)
@Getter
@Setter
@FabosJson(name = "告警处理表单配置",
        orderBy = "MessageProcessFormConfig.createTime desc")
@FabosJsonI18n
public class MessageProcessFormConfig extends MetaModel {
    /**
     * 配置名
     */
    @FabosJsonField(
            views = @View(title = "处理表单名称"),
            edit = @Edit(title = "处理表单名称", notNull = true, search = @Search(vague = true))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "表单路由地址"),
            edit = @Edit(title = "表单路由地址", notNull = true, search = @Search(vague = true))
    )
    private String formUrl;

    @FabosJsonField(
            views = @View(title = "表单参数说明"),
            edit = @Edit(title = "表单参数说明", type = EditType.TEXTAREA, formColumns = 3)
    )
    private String formParameter;

    @FabosJsonField(
            edit = @Edit(title = "备注", notNull = false,
                    type = EditType.TEXTAREA, inputType = @InputType(length = 400), formColumns = 3)
    )
    private String desciption;

}
