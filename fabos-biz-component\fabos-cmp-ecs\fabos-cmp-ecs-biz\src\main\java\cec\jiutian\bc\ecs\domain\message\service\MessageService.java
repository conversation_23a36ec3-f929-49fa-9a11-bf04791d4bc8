package cec.jiutian.bc.ecs.domain.message.service;

import cec.jiutian.bc.ecs.domain.message.entity.Message;
import cec.jiutian.bc.ecs.domain.message.entity.MessageProcessFormDetail;
import cec.jiutian.bc.ecs.domain.message.repository.MsgRepository;
import cec.jiutian.bc.ecs.domain.messagegroup.entity.MessageCategory;
import cec.jiutian.bc.ecs.domain.messagegroup.entity.MessageGroup;
import cec.jiutian.bc.ecs.domain.messagegroup.entity.MessageProcessFormConfig;
import cec.jiutian.bc.ecs.domain.messagegroup.entity.WeixinGroup;
import cec.jiutian.bc.ecs.domain.messagegroup.service.MessageGroupService;
import cec.jiutian.bc.ecs.domain.messagerecord.entity.MessageRecord;
import cec.jiutian.bc.ecs.domain.messagerecord.service.MessageRecordService;
import cec.jiutian.bc.ecs.domain.modle.UserForMsg;
import cec.jiutian.bc.ecs.domain.template.entity.Template;
import cec.jiutian.bc.ecs.domain.template.service.TemplateService;
import cec.jiutian.bc.ecs.domain.templateAttribute.entity.TemplateAttribute;
import cec.jiutian.bc.ecs.domain.templateAttribute.service.TemplateAttributeService;
import cec.jiutian.bc.ecs.dto.MessageCreateDTO;
import cec.jiutian.bc.ecs.dto.MessageRecordCreateDTO;
import cec.jiutian.bc.ecs.dto.PushUserDTO;
import cec.jiutian.bc.ecs.dto.SendMsgGroupDTO;
import cec.jiutian.bc.ecs.dto.SendMsgToPersonDTO;
import cec.jiutian.bc.infra.handler.MessageHandler;
import cec.jiutian.bc.infra.handler.MessageHandlerFactory;
import cec.jiutian.bc.infra.util.EmailTemplate;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.exception.ServiceException;
import cec.jiutian.common.util.BeanUtils;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.constant.YesOrNoStatus;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.frame.module.MetaUser;
import cec.jiutian.core.service.FabosJsonSessionService;
import cec.jiutian.core.view.fabosJson.view.FabosJsonApiModel;
import cec.jiutian.meta.core.util.MD5Util;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import jakarta.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description:
 */
@Service
@Slf4j
@Transactional
public class MessageService {
    private final FabosJsonDao fabosJsonDao;
    private final MessageGroupService messageGroupService;
    private final TemplateService templateService;
    private final TemplateAttributeService templateAttributeService;
    private final MessageHandlerFactory messageHandlerFactory;
    private final MessageRecordService messageRecordService;
    private final EmailTemplate emailTemplate;

    private final MsgRepository msgRepository;

    private final FabosJsonSessionService sessionService;

    @Value("${aes.key:1234}")
    private String aesKey;

    //重复告警抑制的控制时间，默认5分钟
    @Value("${ecs.message.alarm.inhibition.expiresIn:300}")
    private String alarmInhibitionExpiresIn;

    private String alarmInhibitonRedisKeyPrefix = "AlarmInhibition:";

    public MessageService(FabosJsonDao fabosJsonDao, MessageGroupService messageGroupService,
                          TemplateService templateService, TemplateAttributeService templateAttributeService,
                          MessageHandlerFactory messageHandlerFactory,
                          MessageRecordService messageRecordService, EmailTemplate emailTemplate, MsgRepository msgRepository, FabosJsonSessionService sessionService) {
        this.fabosJsonDao = fabosJsonDao;
        this.messageGroupService = messageGroupService;
        this.templateService = templateService;
        this.templateAttributeService = templateAttributeService;
        this.messageHandlerFactory = messageHandlerFactory;
        this.messageRecordService = messageRecordService;
        this.emailTemplate = emailTemplate;
        this.msgRepository = msgRepository;
        this.sessionService = sessionService;
    }

    /**
     * 通过告警类型的方式发送消息
     * 由MQ消费时调用，或者直接调用
     * sendMsgToGroup -> doSend -> messageInit -> sendMessage
     *
     * @param sendMsgGroupDTO
     */
    public void sendMsgToGroup(SendMsgGroupDTO sendMsgGroupDTO) {
        try {
            SendMsgGroupDTO.argsCheck(sendMsgGroupDTO);
        } catch (ServiceException e) {
            log.error("内部系统消发送消费失败：{}", e.getMessage());
            //todo: 是否通知推送人
            return;
        }
        MessageCreateDTO messageCreateDTO = new MessageCreateDTO();
        messageCreateDTO.setGroupCode(sendMsgGroupDTO.getMessageGroupCode());
        messageCreateDTO.setContent(sendMsgGroupDTO.getContent());
        //生成消息的指纹，判断该指纹是否存在于redis中，不存在则记录redis并发送，否则不发送
        String messageFingerPrint = generateMessageFingerprint(messageCreateDTO);
        String cachedMessageId = sessionService.getAsString(messageFingerPrint);
        if (cachedMessageId != null) {
            // 告警抑制，不执行发送，只更新重复数量
            Message inhibitionMessage = fabosJsonDao.getById(Message.class, cachedMessageId);
            if (inhibitionMessage != null) {
                inhibitionMessage.setInhibitionCount(Optional.ofNullable(inhibitionMessage.getInhibitionCount()).orElse(0) + 1);
                fabosJsonDao.mergeAndFlush(inhibitionMessage);
                return;
            }
        }
        Message message = new Message();
        messageInit(messageCreateDTO, message);
        EntityManager em = fabosJsonDao.getEntityManager();
        em.clear();
        em.persist(message);
        em.flush();
        // 在redis中创建, 键为指纹，值为消息ID
        try {
            sessionService.put(messageFingerPrint, message.getId(), Long.parseLong(alarmInhibitionExpiresIn), TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("告警抑制redis存储失败：{}", e.getMessage());
        }
        sendMessage(message);


    }

    private String generateMessageFingerprint(MessageCreateDTO messageCreateDTO) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(messageCreateDTO.getGroupCode());
        stringBuilder.append(messageCreateDTO.getContent());
        stringBuilder.append(messageCreateDTO.getFormUrlParameters());
        return alarmInhibitonRedisKeyPrefix + MD5Util.digest(stringBuilder.toString());
    }

    /**
     * 接收业务提交的某类型告警实例，包括以下逻辑
     * 1. 根据告警实例，初始化告警消息，并存储
     * 2. 根据告警消息中的推送人员和推送方式，进行消息的推送
     * @param
     */
//    private void doSend(Message message) {
//        sendMessage(message);
//    }


    /**
     * 初始化消息对象, 接收前端或远程调用构造的messageCreateDTO，转换为Message对象
     *
     * @param messageCreateDTO
     * @param message
     */
    public void messageInit(MessageCreateDTO messageCreateDTO, Message message) {
        BeanUtils.copyProperties(messageCreateDTO, message);
        MessageGroup messageGroup = messageGroupService.getByCode(messageCreateDTO.getGroupCode());
        if (messageGroup == null) {
            String msg = "告警类型不存在: " + messageCreateDTO.getGroupCode() + ", 消息：" + messageCreateDTO.getContent();
            log.error(msg);
            throw new FabosJsonApiErrorTip(msg, FabosJsonApiModel.PromptWay.NONE);
        }
        if (!messageGroup.getEnableFlag()) {
            String msg = "告警类型未启用: " + messageCreateDTO.getGroupCode() + ", 消息：" + messageCreateDTO.getContent();
            log.error(msg);
            throw new FabosJsonApiErrorTip(msg, FabosJsonApiModel.PromptWay.NONE);
        }
        message.setReceivedTime(LocalDateTime.now());
        // 判断是否需要升级，是则记录下次升级时间
        if (messageGroup.getIsProcess() != null && messageGroup.getUpNumber() != null && messageGroup.getIsProcess().equals("Y") && messageGroup.getUpNumber() > 1) {
            message.setNextUpgradeTime(LocalDateTime.now().plusMinutes(messageGroup.getSecondIntervalTime()));
        }
        message.setMessageGroup(JSONObject.from(messageGroup).toJavaObject(MessageGroup.class));
        message.setMessageCategory(JSONObject.from(messageGroup.getMessageCategory()).toJavaObject(MessageCategory.class));
        if (message.getCurrentUpNumber() == null) {
            message.setCurrentUpNumber(0);
        }
        message.setTitle(messageGroup.getGroupTitle());
        message.setCurrentUpNumber(1);
        message.setGroupLevel(messageGroup.getGroupLevel());
        message.setUpNumber(messageGroup.getUpNumber());
        message.setFirstWay(messageGroup.getFirstWay());
        message.setSecondWay(messageGroup.getSecondWay());
        message.setThirdWay(messageGroup.getThirdWay());
        message.setFourthWay(messageGroup.getFourthWay());
        message.setFifthWay(messageGroup.getFifthWay());
//        message.setFirstUsers(messageGroup.getFirstUsers());
//        message.getFirstUsers().clear();
//        message.getFirstUsers().addAll(messageGroup.getFirstUsers());
        message.setFirstUsers(messageGroup.getFirstUsers()
                .stream()
                .map(user -> {
                    return JSONObject.from(user).toJavaObject(MetaUser.class);
                })
                .collect(Collectors.toList()));
        message.setSecondUsers(messageGroup.getSecondUsers()
                .stream()
                .map(user -> {
                    return JSONObject.from(user).toJavaObject(MetaUser.class);
                })
                .collect(Collectors.toList()));
        message.setThirdUsers(messageGroup.getThirdUsers()
                .stream()
                .map(user -> {
                    return JSONObject.from(user).toJavaObject(MetaUser.class);
                })
                .collect(Collectors.toList()));
        message.setFourthUsers(messageGroup.getFourthUsers()
                .stream()
                .map(user -> {
                    return JSONObject.from(user).toJavaObject(MetaUser.class);
                })
                .collect(Collectors.toList()));
        message.setFifthUsers(messageGroup.getFifthUsers()
                .stream()
                .map(user -> {
                    return JSONObject.from(user).toJavaObject(MetaUser.class);
                })
                .collect(Collectors.toList()));
        message.setFirstWeixinGroup(messageGroup.getFirstWeixinGroup() == null ? null : JSONObject.from(messageGroup.getFirstWeixinGroup()).toJavaObject(WeixinGroup.class));
        message.setSecondWeixinGroup(messageGroup.getSecondWeixinGroup() == null ? null : JSONObject.from(messageGroup.getSecondWeixinGroup()).toJavaObject(WeixinGroup.class));
        message.setThirdWeixinGroup(messageGroup.getThirdWeixinGroup() == null ? null : JSONObject.from(messageGroup.getThirdWeixinGroup()).toJavaObject(WeixinGroup.class));
        message.setFourthWeixinGroup(messageGroup.getFourthWeixinGroup() == null ? null : JSONObject.from(messageGroup.getFourthWeixinGroup()).toJavaObject(WeixinGroup.class));
        message.setFifthWeixinGroup(messageGroup.getFifthWeixinGroup() == null ? null : JSONObject.from(messageGroup.getFifthWeixinGroup()).toJavaObject(WeixinGroup.class));
        message.setSecondIntervalTime(messageGroup.getSecondIntervalTime());
        message.setThirdIntervalTime(messageGroup.getThirdIntervalTime());
        message.setFourthIntervalTime(messageGroup.getFourthIntervalTime());
        message.setFifthIntervalTime(messageGroup.getFifthIntervalTime());
        try {
            // 生成表单处理详情
            List<MessageProcessFormDetail> messageProcessFormDetails = message.getMessageGroup().getMessageProcessFormConfigs()
                    .stream()
                    .map(config -> {
                        MessageProcessFormDetail detail = new MessageProcessFormDetail();
                        detail.setMessageId(message.getId());
                        detail.setFormId(config.getId());
                        detail.setFormName(config.getName());
                        String urlParameters = JSONObject.parseObject(message.getFormUrlParameters())
                                .entrySet()
                                .stream()
                                .map(entry -> entry.getKey() + "=" + entry.getValue())
                                .collect(Collectors.joining("&"));
                        detail.setFormUrl(config.getFormUrl() + "?" + urlParameters);
                        detail.setProcessCompletedFlag(false);
                        return detail;
                    })
                    .collect(Collectors.toList());
            message.setMessageProcessFormConfigs(messageProcessFormDetails);
        } catch (Exception e) {
            throw new FabosJsonApiErrorTip("表单参数处理异常:" + e);
        }
        message.setProcessFormNames(messageGroup.getMessageProcessFormConfigs()
                .stream()
                .map(MessageProcessFormConfig::getName)
                .collect(Collectors.joining(",")));
        message.setContent(messageCreateDTO.getContent());
        message.setFormUrlParameters(messageCreateDTO.getFormUrlParameters());
        message.setCreateTime(LocalDateTime.now());
        String isProcess = messageGroup.getIsProcess();
        if (StringUtils.isBlank(isProcess) || YesOrNoStatus.NO.getValue().equals(isProcess)) {
            message.setIsProcess(YesOrNoStatus.NO.getValue());
        } else {
            message.setIsProcess(YesOrNoStatus.YES.getValue());
        }
        if (YesOrNoStatus.YES.getValue().equals(message.getIsProcess())) {
            message.setProcessCompleteFlag(YesOrNoStatus.NO.getValue());
        }
        message.setReceivedTime(LocalDateTime.now());
        message.setCurrentUpNumber(1);
        message.setManualCreateFlag(false);
    }

    /**
     * 根据模板格式化输出消息内容
     * 暂未启用模板功能，所以该方法返回原内容
     *
     * @param message
     * @return
     */
    private String getFormatedMessageContent(Message message) {
        StringBuilder stringBuilder = new StringBuilder();
        if (StringUtils.isNotEmpty(message.getMessageTemplateId())) {
            Template template = templateService.getById(message.getMessageTemplateId());
            List<TemplateAttribute> attributePOS = templateAttributeService.getByTemplateId(template.getId());
            attributePOS = attributePOS.stream().sorted(Comparator.comparing(TemplateAttribute::getOrderNumber)).collect(Collectors.toList());
            // 根据模板设置消息内容
            try {
                Map contentMap = JSON.parseObject(message.getContent());
                attributePOS.forEach(a -> {
                    if (contentMap.get(a.getConnectAttribute()) != null) {
                        stringBuilder.append(contentMap.get(a.getConnectAttribute()));
                    } else {
                        stringBuilder.append(a.getDefaultValue());
                    }
                });
            } catch (Exception e) {
                log.error("消息内容解析错误:{}", e);
            }
        } else {
            stringBuilder.append(message.getContent());
        }
        return stringBuilder.toString();
    }

    /**
     * 发送消息
     * 根据告警消息，创建告警消息发送记录，并实际执行发送，
     *
     * @param message
     */
    public void sendMessage(Message message) {
        // 根据该消息的当前升级级别，获取该条告警消息的发送用户和发送方式
        List<String> dispatchWayList = new ArrayList<>();
        List<MetaUser> dispatchUserList = new ArrayList<>();
        WeixinGroup dispatchWeixinGroup = new WeixinGroup();
        Long nextUpgradeIntervalTime = 0L;
        switch (message.getCurrentUpNumber().intValue()) {
            case 1:
                dispatchWayList = Arrays.asList(message.getFirstWay().split(","));
                dispatchUserList = message.getFirstUsers();
                dispatchWeixinGroup = message.getFirstWeixinGroup();
                nextUpgradeIntervalTime = message.getSecondIntervalTime();
                break;
            case 2:
                dispatchWayList = Arrays.asList(message.getSecondWay().split(","));
                dispatchUserList = message.getSecondUsers();
                dispatchWeixinGroup = message.getSecondWeixinGroup();
                nextUpgradeIntervalTime = message.getThirdIntervalTime();
                break;
            case 3:
                dispatchWayList = Arrays.asList(message.getThirdWay().split(","));
                dispatchUserList = message.getThirdUsers();
                dispatchWeixinGroup = message.getThirdWeixinGroup();
                nextUpgradeIntervalTime = message.getFourthIntervalTime();
                break;
            case 4:
                dispatchWayList = Arrays.asList(message.getFourthWay().split(","));
                dispatchUserList = message.getFourthUsers();
                dispatchWeixinGroup = message.getFourthWeixinGroup();
                nextUpgradeIntervalTime = message.getFifthIntervalTime();
                break;
            case 5:
                dispatchWayList = Arrays.asList(message.getFifthWay().split(","));
                dispatchUserList = message.getFifthUsers();
                dispatchWeixinGroup = message.getFifthWeixinGroup();
                break;
            default:
                break;
        }
        // 针对每种方式、每个人员进行消息发送
        for (String dispatchWay : dispatchWayList) {
            for (MetaUser user : dispatchUserList) {
                MessageRecord messageRecord = new MessageRecord();
                messageRecord.setTitle(message.getTitle());
                messageRecord.setContent(getFormatedMessageContent(message));
                messageRecord.setMessage(message);
                messageRecord.setDispatchWay(dispatchWay);
                messageRecord.setDispatchUser(user.getPhoneNumber());
                messageRecord.setDispatchMetaUser(user);
                if (dispatchWeixinGroup != null) {
                    messageRecord.setDispatchWeixinGroup(fabosJsonDao.getById(WeixinGroup.class, dispatchWeixinGroup.getId()));
                }
                messageRecord.setDispatchTime(LocalDateTime.now());
                messageRecord.setStatus(YesOrNoStatus.NO.getValue());
                messageRecord.setProcessCompleteFlag(message.getProcessCompleteFlag());
                messageRecord.setIsProcess(message.getIsProcess());
                messageRecord.setGroupLevel(message.getGroupLevel());
                messageRecord.setImmediateAlarmFlag(message.getMessageGroup().getImmediateAlarmFlag());
                MessageHandler messageHandler = messageHandlerFactory.getMessageHandler(dispatchWay);
                try {
                    messageHandler.sendMessage(messageRecord);
                    // 发送成功后，记录发送的消息
                    messageRecordService.create(messageRecord);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        }
        // 发送完成后，设置消息的下次升级发送时间
        if (message.getUpNumber() != null && message.getCurrentUpNumber() < message.getUpNumber()) {
            if (nextUpgradeIntervalTime != null && nextUpgradeIntervalTime > 0) {
                LocalDateTime nextUpgradeTime = LocalDateTime.now().plusMinutes(nextUpgradeIntervalTime);
                message.setNextUpgradeTime(nextUpgradeTime);
                fabosJsonDao.mergeAndFlush(message);
            } else {
                log.error("告警消息的升级时间未设置，当前升级级别为：{}，告警小时：{}", message.getCurrentUpNumber(), message);
            }
        } else {
            // 如果当前升级级别已经是最大级别，则不需要设置下次升级时间
            message.setNextUpgradeTime(null);
            fabosJsonDao.mergeAndFlush(message);
        }
    }

    /**
     * 查询符合升级条件的消息
     *
     * @param currentTime
     */
    public List<Message> getToUpgradeMessage(LocalDateTime currentTime) {
        return msgRepository.findToUpgradeMessage(currentTime);
    }

    /**
     * 关闭消息
     *
     * @param
     */
    public void closeMessage(String messageId, String processDetail) {
        Message message = fabosJsonDao.getById(Message.class, messageId);
        if (message.getProcessCompleteFlag().equals("Y")) {
            throw new FabosJsonApiErrorTip("该消息已处理完成，不能关闭");
        }
        if (message.getIsProcess().equals("N")) {
            throw new FabosJsonApiErrorTip("该消息不需要处理");
        }
        if (message.getMessageProcessFormConfigs().stream().anyMatch(detail -> !detail.isProcessCompletedFlag())) {
            throw new FabosJsonApiErrorTip("存在未完成处理的表单，不能关闭消息");
        }
        message.setProcessCompleteFlag(YesOrNoStatus.YES.getValue());
        message.setProcessTime(LocalDateTime.now());
        UserContext.CurrentUser currentUser = UserContext.get();
        message.setProcessUser(fabosJsonDao.getById(MetaUser.class, currentUser.getUserId()));
        message.setProcessDetail(processDetail);
        //清除下次升级时间
        message.setNextUpgradeTime(null);
        fabosJsonDao.mergeAndFlush(message);
    }

//    private List<PushUserDTO> getUserInfo(MessageRecordCreateDTO createDTO) {
//        List<PushUserDTO> list = new ArrayList<>();
//        if (StringUtils.isNotEmpty(createDTO.getDispatchUser())) {
//            for (String account : createDTO.getDispatchUser().split(",")) {
//                PushUserDTO pushUserDTO = new PushUserDTO();
//                MetaUser metaUser = UserSearchUtil.getMetaUserByPhoneNum(account);
//                BeanUtils.copyProperties(metaUser, pushUserDTO);
//                list.add(pushUserDTO);
//            }
//        }
//        return list;
//    }


    /**
     * 发送消息
     * 发送消息的入口方法，
     * @param messageCreateDTO
     */
//    public void messageCreate(MessageCreateDTO messageCreateDTO) {
//        if (StringUtils.isEmpty(messageCreateDTO.getGroupCode())) {
//            if (StringUtils.isEmpty(messageCreateDTO.getContent())) {
//                throw new FabosJsonApiErrorTip("需求提供预警类型与预警内容");
//            }
//        } else {
//            sendMsgToGroup(messageCreateDTO);
//        }
//        doSend(messageCreateDTO);
//    }


//    public void sendMsgToGroup(MessageCreateDTO messageCreateDTO) {
//        MessageGroup messageGroup = messageGroupService.getByCode(messageCreateDTO.getGroupCode());
//        if (messageGroup == null) {
//            throw new FabosJsonApiErrorTip("预警类型不存在，请重试");
//        }
//    }
    // TODO: 发送邮件的逻辑需调整，由人员获取对应邮箱
//    public void sendMessageByEmail(MessageCreateDTO messageCreateDTO) {
//        if (StringUtils.isNotEmpty(messageCreateDTO.getEmail())) {
//            emailTemplate.sendHtmlMail(messageCreateDTO.getEmail(), messageCreateDTO.getTitle(), messageCreateDTO.getContent());
//            emailTemplate.sendMail587(messageCreateDTO.getEmail(), messageCreateDTO.getTitle(), messageCreateDTO.getContent());
//        }
//    }

//    public List<Message> getMessageListByStatus(String messageStatus) {
//        return fabosJsonDao.queryEntityList(Message.class, "message_status = :messageStatus", new HashMap<String, Object>(1) {{
//            this.put("messageStatus", messageStatus);
//        }});
//    }

//    public void update(Message message) {
//        fabosJsonDao.mergeAndFlush(message);
//    }

//    private List<GeneralIp> selectByCondition(String system, String status) {
//        EntityManager entityManager = fabosJsonDao.getEntityManager();
//        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
//        CriteriaQuery<GeneralIp> criteriaQuery = criteriaBuilder.createQuery(GeneralIp.class);
//        Root<GeneralIp> root = criteriaQuery.from(GeneralIp.class);
//
//        // 添加条件
//        criteriaQuery.where(
//                criteriaBuilder.equal(root.get("status"), status),
//                criteriaBuilder.equal(root.get("sysName"), system)
//        );
//
//        // 创建查询
//        TypedQuery<GeneralIp> typedQuery = entityManager.createQuery(criteriaQuery);
//
//        // 执行查询
//        return typedQuery.getResultList();
//    }

    // 处理MQ中的消息
//    public void processMessage(MessageCreateDTO messageCreateDTO) {
//        Message message = new Message();
//        messageInit(messageCreateDTO, message);
//        fabosJsonDao.persistAndFlush(message);
//        sendMessage(message);
//    }

//    public List<MessageRecord> queryByRangeCreateTime(String startTime, String endTime) {
//        LocalDateTime startDateTime = startTimeValidate(startTime);
//        LocalDateTime endDateTime = ednTimeValidate(endTime);
//        return fabosJsonDao.queryEntityList(MessageRecord.class,
//                "createTime between :startTime and :endTime order by createTime",
//                new HashMap<String, Object>(2) {{
//                    this.put("startTime", startDateTime);
//                    this.put("endTime", endDateTime);
//                }});
//    }

//    public List<Message> getMessageList(String startTime, String endTime) {
//        LocalDateTime startDateTime = startTimeValidate(startTime);
//        LocalDateTime endDateTime = ednTimeValidate(endTime);
//        List<Message> messages = fabosJsonDao.queryEntityList(Message.class,
//                "createTime between :startTime and :endTime order by createTime desc",
//                new HashMap<String, Object>(2) {{
//                    this.put("startTime", startDateTime);
//                    this.put("endTime", endDateTime);
//                }});
//        return messages;
//    }

//    public List<Map<String, Object>> messageSendCount(String startTime, String endTime) {
//        LocalDateTime startDateTime = startTimeValidate(startTime);
//        LocalDateTime endDateTime = ednTimeValidate(endTime);
//
//        //pgslq 语法
////        String sql = "select a.date date,coalesce(b.num,0) num from" +
////                "(select to_char(t,'HH24') date from generate_series(cast('2023-06-01 00:00:00' as timestamp), " +
////                "cast('2023-06-01 23:00:00' as timestamp),'1 hour') t) a " +
////                "left join (select to_char(create_time,'HH24') date, count(distinct id) num from ecs_message_record " +
////                "where id is not null " +
////                "and create_time >= :startTime and create_time <= :endTime group by date) b on a.date=b.date ORDER BY date";
//        //dm 语法
//        String sql = "WITH RECURSIVE time_series AS (\n" +
//                "    SELECT TO_CHAR(CAST('2023-06-01 00:00:00' AS TIMESTAMP), 'HH24') AS date\n" +
//                "    UNION ALL\n" +
//                "    SELECT TO_CHAR(TIMESTAMPADD(HOUR, 1, t.date), 'HH24')\n" +
//                "    FROM time_series t\n" +
//                "    WHERE t.date < TO_CHAR(CAST('2023-06-01 23:00:00' AS TIMESTAMP), 'HH24')\n" +
//                ")\n" +
//                "SELECT a.date, COALESCE(b.num, 0) AS num\n" +
//                "FROM time_series a\n" +
//                "LEFT JOIN (\n" +
//                "    SELECT TO_CHAR(create_time, 'HH24') AS date, COUNT(DISTINCT id) AS num\n" +
//                "    FROM ecs_message_record\n" +
//                "    WHERE id IS NOT NULL\n" +
//                "      AND create_time >= ?\n" +
//                "      AND create_time <= ?\n" +
//                "    GROUP BY date\n" +
//                ") b ON a.date = b.date\n" +
//                "ORDER BY a.date;\n";
//        Query query = fabosJsonDao.getEntityManager().createNativeQuery(sql, Map.class);
//        query.setParameter("startTime", startDateTime);
//        query.setParameter("endTime", endDateTime);
//
//        return query.getResultList();
//    }

//    public LocalDateTime startTimeValidate(String startTime) {
//        if (StringUtils.isEmpty(startTime)) {
//            LocalDateTime localDateTime = LocalDateTime.now().plusDays(-7);
//            localDateTime = localDateTime.withHour(0).withMinute(0).withSecond(0);
//            return localDateTime;
//        }
//        return dateTransform(startTime);
//    }
//
//    public LocalDateTime ednTimeValidate(String endTime) {
//        if (StringUtils.isEmpty(endTime)) {
//            endTime = "2099-12-31 23:59:59";
//        }
//        return dateTransform(endTime);
//    }

//    private LocalDateTime dateTransform(String date) {
//        if (StringUtils.isBlank(date)) {
//            throw new RuntimeException("时间不能为空");
//        }
//        LocalDateTime localDateTime = null;
//        try {
//            localDateTime = LocalDateTime.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
//        } catch (Exception e) {
//            log.error("时间格式有误", e);
//        }
//        return localDateTime;
//    }


    /**
     * 直接发送给接收者，不通过消息组发送
     * 用于如果工作流等组件，直接发送通知给相关人员
     *
     * @param sendMsgToPersonDTO
     */
    public void sendMsgToPerson(SendMsgToPersonDTO sendMsgToPersonDTO) throws Exception {
        if (!SendMsgToPersonDTO.argsCheck(sendMsgToPersonDTO)) {
            log.error("消息发送失败：{}", sendMsgToPersonDTO);
            return;
        }

        MessageRecordCreateDTO messageRecordCreateDTO = new MessageRecordCreateDTO();
        messageRecordCreateDTO.setTitle(sendMsgToPersonDTO.getTitle());
        messageRecordCreateDTO.setContent(sendMsgToPersonDTO.getContent());
        messageRecordCreateDTO.setCreateUser(sendMsgToPersonDTO.getSendBy());
        messageRecordCreateDTO.setDispatchWay(sendMsgToPersonDTO.getWay().name());
        messageRecordCreateDTO.setUserDTOList(new ArrayList<>(sendMsgToPersonDTO.getReceivers().size()));
        MessageRecord messageRecord = new MessageRecord();
        messageRecord.setIsProcess(YesOrNoStatus.NO.getValue());
        messageRecord.setStatus(YesOrNoStatus.NO.getValue());
        messageRecord.setDispatchTime(LocalDateTime.now());
        messageRecord.initMessageRecord(messageRecordCreateDTO);
        for (String receiver : sendMsgToPersonDTO.getReceivers()) {
            messageRecord.setDispatchUser(receiver);
            MetaUser metaUser = new MetaUser();
            metaUser = fabosJsonDao.queryEntity(MetaUser.class, "phoneNumber = :phoneNumber", new HashMap<String, Object>(4) {{
                this.put("phoneNumber", receiver);
            }});
            if (metaUser == null) {
                log.error("发送对象为非系统用户");
            }
            messageRecord.setDispatchMetaUser(metaUser);


            MessageHandler messageHandler = messageHandlerFactory.getMessageHandler(sendMsgToPersonDTO.getWay().name());
            try {
                messageHandler.sendMessage(messageRecord);
                // 发送成功后，记录发送的消息
                messageRecordService.create(messageRecord);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

//            UserForMsg user = UserSearchUtil.getUserByPhoneNum(receiver);
//            PushUserDTO pushUserDTO = createByUserForMsg(user);
//            messageRecordCreateDTO.getUserDTOList().add(pushUserDTO);
        }
    }

    public static PushUserDTO createByUserForMsg(UserForMsg userForMsg) {
        PushUserDTO pushUserDTO = new PushUserDTO();
        pushUserDTO.setAccount(userForMsg.getAccount());
        pushUserDTO.setName(userForMsg.getName());
        pushUserDTO.setState(userForMsg.getState());
        pushUserDTO.setPhoneNumber(userForMsg.getPhoneNumber());
        pushUserDTO.setEmailAddress(userForMsg.getEmailAddress());
        return pushUserDTO;
    }

}
