package cec.jiutian.bc.urm.inbound.local.filter;

import cec.jiutian.bc.urm.domain.systemSecurityConfig.entity.SystemSecurityConfig;
import cec.jiutian.bc.urm.domain.user.entity.LoginModel;
import cec.jiutian.bc.urm.dto.MetaUserinfo;
import cec.jiutian.bc.urm.inbound.local.config.SystemConfig;
import cec.jiutian.bc.urm.inbound.local.context.BackgroundTaskTraceContext;
import cec.jiutian.bc.urm.inbound.local.context.SecurityConfigContext;
import cec.jiutian.bc.urm.inbound.local.service.command.SystemBootstrap;
import cec.jiutian.bc.urm.inbound.local.service.command.UrmCommandService;
import cec.jiutian.bc.urm.outbound.port.client.UbpFeignClient;
import cec.jiutian.common.constant.AuthorizationConstant;
import cec.jiutian.common.constant.SessionKey;
import cec.jiutian.common.context.RequestContext;
import cec.jiutian.common.context.UserContext;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.frame.constant.FabosJsonRestPath;
import cec.jiutian.core.frame.exception.FabosJsonApiErrorTip;
import cec.jiutian.core.frame.exception.FabosJsonWebApiRuntimeException;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import cec.jiutian.core.service.FabosJsonSessionService;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTPayload;
import cn.hutool.jwt.RegisteredPayload;
import cn.hutool.jwt.signers.JWTSignerUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.io.IOException;
import java.security.interfaces.RSAPublicKey;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

import static org.springframework.http.HttpStatus.BAD_REQUEST;
import static org.springframework.http.HttpStatus.FORBIDDEN;
import static org.springframework.http.HttpStatus.UNAUTHORIZED;

/**
 * 此filter用于校验权限，2025-03-10由于需要调用登录部分逻辑，从core包迁移至此
 * 之前的记录可以在fabos-framework->fabos-core内的cec.jiutian.core.filter包下查看
 */
@Slf4j
@Component
@Order(10)
public class PermissionFilter implements Filter {
    private static final List<String> refreshTokenExcludeUrls = Arrays.asList("/queryAllBiz", "/uninstallBiz", "/loadingBiz", "/fabos-cmp-urm/check", "/fabos-cmp-urm/login", "/fabos-cmp-urm/logout", "/fabos-cmp-urm/code-img");
    private static final Pattern authExcludeUrls = Pattern.compile(".*(/check).*|.*(/acquireAllModels).*|.*(/refreshToken).*|.*(/getOAuth2Params).*|.*(/getSystemConfig).*|.*(/public/download-attachment).*|(.*(/login$))|.*(/code-img).*|.*(/oauth2/).*|.*(/weixin/).*|.*(/websocket/).*|.*(/getLoginAbleApplications$)|.*(/imp/template/download).*|.*(/fabos-api/apk-management/getLatestRelease$)");
    private static final String openApiRequestingUrl = FabosJsonRestPath.FABOS_OPEN_API + "/" + "requestToken";
    private final FabosJsonSessionService sessionService;
    private final UrmCommandService urmCommandService;
    private final FabosJsonDao fabosJsonDao;
    private final SystemBootstrap systemBootstrap;
    private final UbpFeignClient ubpFeignClient;
    private final SystemConfig systemConfig;
    @Value("${spring.application.name}")
    public String currentService = "";

    public PermissionFilter(FabosJsonSessionService sessionService, UrmCommandService urmCommandService, FabosJsonDao fabosJsonDao, SystemBootstrap systemBootstrap, UbpFeignClient ubpFeignClient, SystemConfig systemConfig) {
        this.sessionService = sessionService;
        this.urmCommandService = urmCommandService;
        this.fabosJsonDao = fabosJsonDao;
        this.systemBootstrap = systemBootstrap;
        this.ubpFeignClient = ubpFeignClient;
        this.systemConfig = systemConfig;
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        Filter.super.init(filterConfig);
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;

        String tokenMaybeEmpty  = request.getHeader("token");
        String uri = request.getRequestURI();
        if (uri.startsWith(FabosJsonRestPath.FABOS_OPEN_API + "/")) {
            boolean authorized = checkAuthForOpenApiEndpoints(uri, tokenMaybeEmpty, response);
            if (authorized) {
                chain.doFilter(request, response);
            }
            // 如果是开放接口，且未经认证，则打回
            return;
        } else {
            if (uri.startsWith(FabosJsonRestPath.FABOS_TASK_API + "/")) {
                // 如果是后台任务接口，检查其trace id
                String traceId = request.getHeader(AuthorizationConstant.BACKGROUND_TASK_AUTHORIZATION);
                String backgroundUser = performBackgroundTaskUriCheck(traceId);
                if (StringUtils.isNotBlank(backgroundUser)) {
                    BackgroundTaskTraceContext.setTraceId(traceId);
                    chain.doFilter(request, response);
                    BackgroundTaskTraceContext.clear();
                } else {
                    handleErrorResponse(response, FORBIDDEN.value(), "For background task uris, the header 'X-Background-Task-Id' is required");
                }
                return;
            } else {
                // 若传入了biz token，则会直接跳过校验，不在排除列表内的，走 access token 校验逻辑
                if (StringUtils.isBlank(tokenMaybeEmpty) && !authExcludeUrls.matcher(uri).matches()) {
                    tokenMaybeEmpty = checkBearerTokenAndSetBizToken(response);
                    if (StringUtils.isBlank(tokenMaybeEmpty)) {
                        // 如果token为空，说明access token校验失败，无需走后续的filter chain直接返回
                        return;
                    }
                }
            }
        }

        performUserContextCaching(tokenMaybeEmpty, uri);
        chain.doFilter(request, response);
    }

    private String performBackgroundTaskUriCheck(String traceId) {
        if (StringUtils.isBlank(traceId)) {
            return null;
        }
        String backgroundUser = sessionService.getAsString(SessionKey.BACKGROUND_TASKS + traceId);
        if (StringUtils.isBlank(backgroundUser)) {
            return null;
        }
        return backgroundUser;
    }

    /**
     * 执行token续期和缓存用户信息
     *
     * @param token 此次请求的token
     * @param url   此次请求的URI
     */
    private void performUserContextCaching(String token, String url) {
        MetaUserinfo metaUserinfo = sessionService.getSimpleUserInfo();
        if (metaUserinfo == null) {
            return;
        }
        UserContext.CurrentUser userParam = new UserContext.CurrentUser();
        UserContext.set(userParam);
        BeanUtils.copyProperties(metaUserinfo, userParam);
        userParam.setUserId(metaUserinfo.getId());
        userParam.setUserName(metaUserinfo.getName());
        userParam.setAccount(metaUserinfo.getAccount());
        userParam.setRoles(metaUserinfo.getRoles());
        userParam.setHashAdmin(metaUserinfo.isAdmin());
        userParam.setToken(token);
        userParam.setUri(url);
        userParam.setMenuIds(metaUserinfo.getMenus());
        UserContext.set(userParam);
        UserContext.setToken(token);
    }

    private RSAPublicKey tryToGetPublicKey() {
        // 如果启动时未获取到公钥，则重试一次
        RSAPublicKey publicKey = systemBootstrap.performJwkInitialization();
        if (Objects.isNull(publicKey)) {
            throw new FabosJsonApiErrorTip("在验证签名时出现问题，请检查UBP服务的状态");
        }
        return publicKey;
    }

    /**
     * 检查当前的access token是否有效，如果有效则设置biz token
     *
     * @param response 响应
     * @return biz token，如果为空则表示校验失败
     */
    private String checkBearerTokenAndSetBizToken(HttpServletResponse response) throws IOException {
        StopWatch stopWatch = new StopWatch("JWT操作讯息");
        stopWatch.start("识别、校验、解析");
        String authorization = sessionService.getAuthorization();
        if (StringUtils.isBlank(authorization)) {
            handleErrorResponse(response, BAD_REQUEST.value(), "Access token is required");
            return null;
        }
        JWT jwt;
        if (systemBootstrap.needVerifySign()) {
            RSAPublicKey publicKey = SecurityConfigContext.getOauth2PublicKey();
            if (Objects.isNull(publicKey)) {
                // 如果启动时未获取到公钥，则重试一次
                publicKey = tryToGetPublicKey();
            }
            jwt = JWT.of(authorization).setSigner(JWTSignerUtil.rs256(publicKey));
            if (!jwt.validate(0)) {
                // 如果拿到了公钥但是验签失败，重试一次(讲道理这里可以判断新旧public key是否一致，一致则直接抛错)
                publicKey = tryToGetPublicKey();
                jwt = JWT.of(authorization).setSigner(JWTSignerUtil.rs256(publicKey));
                if (!jwt.validate(0)) {
                    handleErrorResponse(response, UNAUTHORIZED.value(), "Access token validation failed");
                    return null;
                }
            }
        } else {
            jwt = JWT.of(authorization);
        }

        JWTPayload payload = jwt.getPayload();
        Object payloadClaim = payload.getClaim(RegisteredPayload.SUBJECT);
        stopWatch.stop();
        stopWatch.start("获取缓存信息");
        // subject 其实是用户名
        String subject = payloadClaim.toString();
        String redisKey = SessionKey.ONLINE_USERS + subject;
        String cachedToken = (String) sessionService.getValueFromMap(redisKey, currentService);
        stopWatch.stop();
        stopWatch.start("检查、生成新token");
        if (StringUtils.isNotBlank(cachedToken)
                && urmCommandService.checkTokenOnline(cachedToken)) {
            log.debug("检测到用户{}于系统{}具有仍在线的token: {}，将复用此token", subject, currentService, cachedToken);
        } else {
            // 生成新token，将其写入缓存，此处关于SecurityConfigContext有一个潜在问题，每个系统都维护了这个Context
            // 可能这些时间都不一致，如果是从ubp定向过去的，会导致这个时间不生效（每次从此执行performInternalLogin时）
            if (!checkIfSystemAvailableForUser(subject)) {
                // 如果用户没有当前系统的权限，则403
                log.debug("用户{}不具有访问系统{}的权限，返回403", subject, currentService);
                handleErrorResponse(response, FORBIDDEN.value(), "Access unauthorized");
                return null;
            }
            if (SecurityConfigContext.isNotExists()) {
                setSecurityContext();
            }
            LoginModel loginModel = urmCommandService.performInternalLogin(subject);
            cachedToken = loginModel.getToken();
            // 维护 user-systems-biz token 的map
            Object serviceTokenMap = sessionService.getMap(redisKey);
            if (Objects.isNull(serviceTokenMap)) {
                // map 为空，说明任何系统没登录过
                HashMap<String, Object> initialMap = new HashMap<>();
                sessionService.putMap(redisKey, initialMap, -1);
            }
            sessionService.putValueToMap(redisKey, currentService, cachedToken);
            log.debug("未找到用户{}于系统{}中的token，执行快速登录生成token:{}", subject, currentService, cachedToken);
        }
        UserContext.setToken(cachedToken);
        stopWatch.stop();
        log.debug(stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        return cachedToken;
    }

    /**
     * 执行远程调用以检查此用户是否具有该系统的权限，如果是管理员账户则永远返回true
     *
     * @param userName 用户名
     * @return true如果具有权限
     */
    private boolean checkIfSystemAvailableForUser(String userName) {
        if (userName.equals(systemConfig.getSuperUserName())) {
            return true;
        }
        HashMap<String,String > map = new HashMap<>();
        map.put("svsCode", currentService);
        map.put("userName", userName);
        Boolean retrieved = null;
        try {
            retrieved = ubpFeignClient.checkIfSystemAvailableForUser(map);
        } catch (Exception e) {
            throw new FabosJsonApiErrorTip(e.getMessage());
        }
        return Objects.nonNull(retrieved) && retrieved;
    }

    private void setSecurityContext(){
        SystemSecurityConfig securityConfig = fabosJsonDao.selectOne(new SystemSecurityConfig());
        if (Objects.isNull(securityConfig)) {
            log.error("未在数据库查询到系统安全配置，请管理员予以添加");
            throw new FabosJsonWebApiRuntimeException("未在数据库查询到系统安全配置，请管理员予以添加");
        }
        SecurityConfigContext.set(securityConfig);
    }

    /**
     * 对于开放接口，校验token和能否访问对应资源
     *
     * @param uri      请求URI
     * @param token    请求偷啃
     * @param response 响应
     * @return 是否通过校验，是为ture
     * @throws IOException 如果写入response失败
     */
    private boolean checkAuthForOpenApiEndpoints(String uri, String token, HttpServletResponse response) throws IOException {
        boolean errorIndicator = false;
        if (!openApiRequestingUrl.equals(uri)) {
            // 针对请求token的接口绕过
            if (StringUtils.isBlank(token)) {
                handleErrorResponse(response, UNAUTHORIZED.value(), "Token is required");
                log.warn("在请求此URI时[{}]未提供token", uri);
                errorIndicator = true;
            } else {
                String availableResources = sessionService.getAsString(SessionKey.OPEN_API_TOKENS + token);
                if (StringUtils.isBlank(availableResources)) {
                    handleErrorResponse(response, UNAUTHORIZED.value(), "Invalid token");
                    log.warn("在请求此URI时[{}]提供了非法token:{} ", uri, token);
                    errorIndicator = true;
                } else {
                    List<String> availableResourceList = JSON.parseArray(availableResources, String.class);
                    if (!availableResourceList.contains(uri)) {
                        handleErrorResponse(response, FORBIDDEN.value(), "Access unauthorized");
                        log.warn("在请求此URI时[{}]提供了权限不足的token:{} ", uri, token);
                        errorIndicator = true;
                    }
                }
            }
        }
        return !errorIndicator;
    }

    @Override
    public void destroy() {
        Filter.super.destroy();
        UserContext.clear();
        RequestContext.clear();
    }

    private static void handleErrorResponse(HttpServletResponse response, int status, String reason) throws IOException {
        JSONObject responseBody = new JSONObject();
        responseBody.put("status", status);
        responseBody.put("reason", reason);
        response.setStatus(status);
        response.getWriter().write(responseBody.toString());
    }


}
