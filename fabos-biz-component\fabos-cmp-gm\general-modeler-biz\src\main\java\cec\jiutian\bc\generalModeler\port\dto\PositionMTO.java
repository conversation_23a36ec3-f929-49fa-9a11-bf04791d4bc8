package cec.jiutian.bc.generalModeler.port.dto;

import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.FabosJsonI18n;
import cec.jiutian.view.constant.AnnotationConst;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.InputType;
import cec.jiutian.view.field.edit.NumberType;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Entity
@Table(name = "fd_position")
@FabosJson(
        name = "岗位管理",
        orderBy = "PositionMTO.sort asc"
)
@FabosJsonI18n
@Getter
@Setter
@NoArgsConstructor
public class PositionMTO extends MetaModel {

    @Column(length = AnnotationConst.CODE_LENGTH, unique = true)
    @FabosJsonField(
            views = @View(title = "岗位编码", sortable = true),
            edit = @Edit(title = "岗位编码", notNull = true, readonly = @Readonly(add = false), search = @Search(vague = true))
    )
    private String code;

    @FabosJsonField(
            views = @View(title = "岗位名称", sortable = true),
            edit = @Edit(title = "岗位名称", notNull = true, search = @Search(vague = true))
    )
    private String name;

    @FabosJsonField(
            views = @View(title = "岗位职责", sortable = true),
            edit = @Edit(title = "岗位职责", notNull = false)
    )
    private String responsibility;

    @FabosJsonField(
            views = @View(title = "显示顺序", sortable = true),
            edit = @Edit(title = "显示顺序", notNull = true, numberType = @NumberType(min = 1, max = 9999))
    )
    private Integer sort;

    @FabosJsonField(
            views = @View(title = "备注"),
            edit = @Edit(title = "备注", notNull = false,
                    type = EditType.TEXTAREA, inputType = @InputType(length = 400))
    )
    private String remark;

}