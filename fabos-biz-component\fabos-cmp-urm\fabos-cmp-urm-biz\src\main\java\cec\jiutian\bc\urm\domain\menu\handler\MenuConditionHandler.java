package cec.jiutian.bc.urm.domain.menu.handler;

import cec.jiutian.view.fun.FilterHandler;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class MenuConditionHandler implements FilterHandler {

    @Value("${spring.application.name}")
    public String currentService = "";
    @Override
    public String filter(String condition, String[] params) {
        return "(Menu.applicationName is null or Menu.applicationName = '" + this.currentService + "')";
    }
}
