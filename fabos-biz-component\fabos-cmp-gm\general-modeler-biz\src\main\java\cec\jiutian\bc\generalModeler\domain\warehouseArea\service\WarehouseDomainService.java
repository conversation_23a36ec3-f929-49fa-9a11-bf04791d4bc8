package cec.jiutian.bc.generalModeler.domain.warehouseArea.service;

import cec.jiutian.bc.generalModeler.domain.warehouseArea.model.Warehouse;
import cec.jiutian.bc.generalModeler.enumeration.WarehouseStateEnum;
import cec.jiutian.core.frame.jpa.dao.FabosJsonDao;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class WarehouseDomainService {
    @Resource
    private FabosJsonDao FabosJsonDao;

    public Boolean hold(String id) {
        Warehouse warehouse = FabosJsonDao.findById(Warehouse.class, id);
        warehouse.setLockState(WarehouseStateEnum.Enum.Locked.name());
        FabosJsonDao.mergeAndFlush(warehouse);
        return true;
    }

    public Boolean unHold(String id) {
        Warehouse warehouse = FabosJsonDao.findById(Warehouse.class, id);
        warehouse.setLockState(WarehouseStateEnum.Enum.Normal.name());
        FabosJsonDao.mergeAndFlush(warehouse);
        return true;
    }

}
