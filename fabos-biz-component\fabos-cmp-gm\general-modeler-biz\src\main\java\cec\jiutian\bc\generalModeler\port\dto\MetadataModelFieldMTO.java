package cec.jiutian.bc.generalModeler.port.dto;

import cec.jiutian.core.frame.module.BaseModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.SubTableField;
import cec.jiutian.view.config.Comment;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.edit.Search;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;

@FabosJson(
        name = "模型字段"
)
@Entity
@Table(name = "metadata_model_field")
@Getter
public class MetadataModelFieldMTO extends BaseModel {

//    @Id
//    private String id;

    @FabosJsonField(
            views = @View(title = "字段名称"),
            edit = @Edit(title = "字段名称", search = @Search)
    )
    @Comment("字段名称")
    @SubTableField
    private String displayName;

    // 依赖模型中对应的关联字段 记录依赖模型字段id
//    @ManyToOne
////    @JoinColumn(referencedColumnName = "metadataId", name = "modelMetadataId")
//    @JsonIgnoreProperties({"fields"})
//    private MetadataModel referenceModel;

//    @Formula("(select t2.DISPLAY_NAME from metadata_model_field t1, METADATA_MODEL t2 where t1.REFERENCE_MODEL_ID = t2.id and t1.id = id)")
//    @FabosJsonField(
//            views = @View(title = "模型名称")
//    )
//    @Comment("模型名称")
//    private String modelName;

    @FabosJsonField(
            views = @View(title = "字段编码"),
            edit = @Edit(title = "字段编码", search = @Search)
    )
    @Comment("字段编码")
    @SubTableField
    private String columnName;

    private String model;


}
