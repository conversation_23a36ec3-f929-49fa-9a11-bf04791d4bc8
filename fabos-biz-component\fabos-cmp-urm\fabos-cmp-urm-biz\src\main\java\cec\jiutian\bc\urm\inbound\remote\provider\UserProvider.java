package cec.jiutian.bc.urm.inbound.remote.provider;

import cec.jiutian.bc.urm.inbound.local.service.command.UrmCommandService;
import cec.jiutian.bc.urm.dto.MetaUserinfo;
import cec.jiutian.bc.urm.provider.IUserProvider;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/29
 */
@Slf4j
@Component
public class UserProvider implements IUserProvider {

    @Resource
    private UrmCommandService urmCommandService;

    @Override
    public List<MetaUserinfo> getUserByRoleIds(List<String> items) {
        return urmCommandService.getUserByRoleIds(items);
    }

    @Override
    public MetaUserinfo getMetaUserByAccount(String account) {
        return urmCommandService.getMetaUserByAccount(account);
    }

    @Override
    public MetaUserinfo getMetaUserByPhoneNumber(String phoneNumber) {
        return urmCommandService.getMetaUserByPhoneNumber(phoneNumber);
    }

    @Override
    public List<MetaUserinfo> getMetaUserByPhoneNumbers(List<String> phoneNumbers) {
        return urmCommandService.getMetaUserByPhoneNumbers(phoneNumbers);
    }

}
