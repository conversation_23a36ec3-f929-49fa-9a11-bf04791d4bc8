package cec.jiutian.bc.infra.aspect;

import cec.jiutian.bc.urm.annotation.FabosBackgroundTask;
import cec.jiutian.bc.urm.inbound.local.context.BackgroundTaskTraceContext;
import cec.jiutian.common.constant.SessionKey;
import cec.jiutian.common.util.StringUtils;
import cec.jiutian.core.prop.FabosJsonProp;
import cec.jiutian.core.service.FabosJsonSessionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @time 2025-06-23 12:48
 */

@Aspect
@Component
@Slf4j
public class BackgroundTaskAspect {

    @Resource
    private FabosJsonSessionService sessionService;

    @Resource
    private FabosJsonProp fabosJsonProp;

    @Value("${system.manage.super-account:superUser}")
    private String superUserName;

    @Pointcut("@annotation(cec.jiutian.meta.FabosJob)")
    public void fabosJob() {
    }

    @Pointcut("@annotation(cec.jiutian.bc.urm.annotation.FabosBackgroundTask)")
    public void backgroundTask() {
    }


    @Around("fabosJob()")
    public Object aroundExecutingFabosJob(ProceedingJoinPoint joinPoint) throws Throwable {
        return aroundExecutingFabosJob(joinPoint, null);
    }

    @Around("backgroundTask() && @annotation(annotated)")
    public Object aroundExecutingFabosJob(ProceedingJoinPoint joinPoint, FabosBackgroundTask annotated) throws Throwable {
        String uuid = UUID.randomUUID().toString();
        BackgroundTaskTraceContext.setTraceId(uuid);
        String methodName = joinPoint.getSignature().getName();
        // 获取后台任务用户，如不配置则为superUser
        String backgroundTaskUser = StringUtils.isNotBlank(fabosJsonProp.getBackgroundTaskUser())
                ? fabosJsonProp.getBackgroundTaskUser()
                : superUserName;
        log.debug("后台任务[{}]开始执行，trace id: {}，用户：{}", methodName, uuid, backgroundTaskUser);
        // 超时时间如果为非正数，则不给定超时时间
        int timeout = Objects.nonNull(annotated)
                ? annotated.timeout() <= 0 ? -1 : annotated.timeout()
                : 30 * 60;
        sessionService.put(SessionKey.BACKGROUND_TASKS + uuid, backgroundTaskUser, timeout);
        // 执行目标方法
        Object methodResult = joinPoint.proceed();
        log.debug("后台任务[{}]结束，trace id: {}", methodName, uuid);
        // 打扫
        sessionService.remove(SessionKey.BACKGROUND_TASKS + uuid);
        BackgroundTaskTraceContext.clear();
        return methodResult;
    }


}

