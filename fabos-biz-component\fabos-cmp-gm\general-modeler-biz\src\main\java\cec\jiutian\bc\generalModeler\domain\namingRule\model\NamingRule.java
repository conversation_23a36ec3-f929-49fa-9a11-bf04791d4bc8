package cec.jiutian.bc.generalModeler.domain.namingRule.model;


import cec.jiutian.bc.generalModeler.domain.namingRule.proxy.NamingRuleDataProxy;
import cec.jiutian.bc.generalModeler.handler.NamingRuleOperationHandler;
import cec.jiutian.core.frame.module.MetaModel;
import cec.jiutian.core.view.fabosJson.FabosJson;
import cec.jiutian.view.FabosJsonField;
import cec.jiutian.view.field.Edit;
import cec.jiutian.view.field.EditType;
import cec.jiutian.view.field.Readonly;
import cec.jiutian.view.field.View;
import cec.jiutian.view.field.ViewType;
import cec.jiutian.view.field.edit.Search;
import cec.jiutian.view.type.RowOperation;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FabosJson(
        name = "编码规则",
        dataProxy = NamingRuleDataProxy.class,
        rowOperation = {
                @RowOperation(
                        code = "namingrule_getnextnumber",
                        operationHandler = NamingRuleOperationHandler.class,
                        mode = RowOperation.Mode.SINGLE,
                        callHint = "确定获取下一编码值？",
                        title = "获取下一编码值"),
        }
)
@Table(name = "naming_rule",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"namingRuleCode"})
        }
)
@Entity
@Getter
@Setter
public class NamingRule extends MetaModel {
    @FabosJsonField(
            views = @View(title = "编码"),
            edit = @Edit(title = "编码", readonly = @Readonly(add = false, edit = true), notNull = true, search = @Search(vague = true))
    )
    private String namingRuleCode;

    @FabosJsonField(
            views = @View(title = "显示名称"),
            edit = @Edit(title = "显示名称", notNull = true, search = @Search(vague = true))
    )
    private String displayName;

    @FabosJsonField(
            edit = @Edit(title = "说明", notNull = false,
                    type = EditType.TEXTAREA)
    )
    private String description;


    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY) //一对多，且开启级联
    @JoinColumn(name = "naming_rule_id")//this表示当前的表名，如：order_id子表会自动创建该列来标识与主表的关系
    @FabosJsonField(
            views = @View(title = "规则参数", column = "namingRuleParametersList", type = ViewType.TABLE_VIEW, export = false),
            edit = @Edit(title = "规则参数", notNull = true, search = @Search,
                    type = EditType.TAB_TABLE_ADD
            )
    )
    private List<NamingRuleParameter> namingRuleParameterList;
}
